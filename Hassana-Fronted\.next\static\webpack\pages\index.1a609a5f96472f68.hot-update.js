"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject2());\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notification-details/\".concat(notificationData.id),\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        onClick: ()=>removeHandler(notificationData.id),\n                                                        sx: {\n                                                            p: 2,\n                                                            borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                                cursor: \"pointer\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\",\n                                                            position: \"relative\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 2,\n                                                                    alignItems: \"flex-start\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            width: 40,\n                                                                            height: 40,\n                                                                            borderRadius: \"50%\",\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            justifyContent: \"center\",\n                                                                            flexShrink: 0,\n                                                                            boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                                            sx: {\n                                                                                color: \"white\",\n                                                                                fontSize: \"20px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1,\n                                                                            minWidth: 0\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"body1\",\n                                                                                sx: {\n                                                                                    fontSize: \"14px\",\n                                                                                    fontWeight: 500,\n                                                                                    lineHeight: \"20px\",\n                                                                                    color: \"#333\",\n                                                                                    mb: 0.5,\n                                                                                    wordBreak: \"break-word\"\n                                                                                },\n                                                                                children: notificationData.notification\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"caption\",\n                                                                                sx: {\n                                                                                    fontSize: \"12px\",\n                                                                                    color: \"#666\",\n                                                                                    display: \"flex\",\n                                                                                    alignItems: \"center\",\n                                                                                    gap: 0.5\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                        sx: {\n                                                                                            fontSize: \"4px\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                        lineNumber: 335,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    position: \"absolute\",\n                                                                    left: 0,\n                                                                    top: 0,\n                                                                    bottom: 0,\n                                                                    width: \"3px\",\n                                                                    borderRadius: \"0 2px 2px 0\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, notificationData.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 434,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getUnseenNotificationsCount, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 10000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\"\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    const [markAllAsSeen] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationMarkAllNotificationsAsSeen);\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.notifications);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.notifications)) {\n            const allNotifications = data.notifications;\n            const currentCount = allNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"All notifications received:\", allNotifications);\n            console.log(\"Count:\", currentCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 1000);\n            }\n            setNotifications(allNotifications);\n            setPreviousNotificationCount(currentCount);\n            console.log(\"Notification count updated to: \".concat(currentCount));\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 585,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = async ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        if (!wasOpen && userId) {\n            try {\n                await markAllAsSeen({\n                    variables: {\n                        userId\n                    }\n                });\n                refetchUnseenCount();\n                console.log(\"All notifications marked as seen\");\n            } catch (error) {\n                console.error(\"Error marking notifications as seen:\", error);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 690,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 697,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 683,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 708,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 707,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 700,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: \"''\",\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 747,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 729,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.blue.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.blue.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.green.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.green.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.purple.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.purple.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 812,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 775,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 774,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 765,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications} notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"8px\",\n                                                    sm: \"10px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: (unseenCountData === null || unseenCountData === void 0 ? void 0 : unseenCountData.unseenNotificationsCount) || 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 838,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 822,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 821,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme,\n                                    selectedNotification: selectedNotification,\n                                    onNotificationClick: handleNotificationClick,\n                                    onBackToList: handleBackToList\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 860,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 670,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 669,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid white\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 893,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 919,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 932,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 931,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 936,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 930,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 942,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 941,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 946,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 952,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 951,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 956,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 961,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 966,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 960,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 972,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 976,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 982,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 981,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 992,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 991,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 996,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 914,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1005,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1004,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 904,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 885,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 877,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1015,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1010,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Bd4QzAVOel/6dhCABCMIh6zDEbc=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});