"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_ArrowBack__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ArrowBack */ \"./node_modules/@mui/icons-material/ArrowBack.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.keyframes)(_templateObject2());\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_16__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme, selectedNotification, onNotificationClick, onBackToList } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, undefined) : selectedNotification ? // Show notification details\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    mb: 2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        onClick: onBackToList,\n                                                        sx: {\n                                                            mr: 1,\n                                                            p: 0.5\n                                                        },\n                                                        size: \"small\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowBack__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: \"18px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            fontSize: \"16px\",\n                                                            fontWeight: 600\n                                                        },\n                                                        children: \"Notification Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Divider, {\n                                                sx: {\n                                                    mb: 2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                variant: \"body1\",\n                                                sx: {\n                                                    fontSize: \"14px\",\n                                                    lineHeight: 1.6,\n                                                    color: theme.palette.text.primary,\n                                                    mb: 2\n                                                },\n                                                children: selectedNotification.notification\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    fontSize: \"12px\",\n                                                    color: theme.palette.text.secondary,\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: new Date(selectedNotification.createdAt).toLocaleDateString(\"en-US\", {\n                                                    year: \"numeric\",\n                                                    month: \"long\",\n                                                    day: \"numeric\",\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: {\n                                                        pathname: \"/notifications\",\n                                                        query: {\n                                                            notificationId: notificationData.id\n                                                        }\n                                                    },\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        onClick: ()=>removeHandler(notificationData.id),\n                                                        sx: {\n                                                            p: 2,\n                                                            borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                                cursor: \"pointer\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\",\n                                                            position: \"relative\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 2,\n                                                                    alignItems: \"flex-start\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        sx: {\n                                                                            width: 40,\n                                                                            height: 40,\n                                                                            borderRadius: \"50%\",\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            justifyContent: \"center\",\n                                                                            flexShrink: 0,\n                                                                            boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                                            sx: {\n                                                                                color: \"white\",\n                                                                                fontSize: \"20px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1,\n                                                                            minWidth: 0\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                variant: \"body1\",\n                                                                                sx: {\n                                                                                    fontSize: \"14px\",\n                                                                                    fontWeight: 500,\n                                                                                    lineHeight: \"20px\",\n                                                                                    color: \"#333\",\n                                                                                    mb: 0.5,\n                                                                                    wordBreak: \"break-word\"\n                                                                                },\n                                                                                children: notificationData.notification\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                variant: \"caption\",\n                                                                                sx: {\n                                                                                    fontSize: \"12px\",\n                                                                                    color: \"#666\",\n                                                                                    display: \"flex\",\n                                                                                    alignItems: \"center\",\n                                                                                    gap: 0.5\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                        sx: {\n                                                                                            fontSize: \"4px\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                        lineNumber: 385,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                sx: {\n                                                                    position: \"absolute\",\n                                                                    left: 0,\n                                                                    top: 0,\n                                                                    bottom: 0,\n                                                                    width: \"3px\",\n                                                                    borderRadius: \"0 2px 2px 0\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, notificationData.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 191,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_10__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 484,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedNotification, setSelectedNotification] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getUnseenNotificationsCount, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 10000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\"\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationAddNotificationView);\n    const [markAllAsSeen] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationMarkAllNotificationsAsSeen);\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.notifications);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.notifications)) {\n            const allNotifications = data.notifications;\n            const currentCount = allNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"All notifications received:\", allNotifications);\n            console.log(\"Count:\", currentCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 1000);\n            }\n            setNotifications(allNotifications);\n            setPreviousNotificationCount(currentCount);\n            console.log(\"Notification count updated to: \".concat(currentCount));\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 636,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 635,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = async ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        if (!wasOpen && userId) {\n            try {\n                await markAllAsSeen({\n                    variables: {\n                        userId\n                    }\n                });\n                refetchUnseenCount();\n                console.log(\"All notifications marked as seen\");\n            } catch (error) {\n                console.error(\"Error marking notifications as seen:\", error);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        setSelectedNotification(null); // Reset selected notification when closing\n    };\n    const handleNotificationClick = (notification)=>{\n        setSelectedNotification(notification);\n    };\n    const handleBackToList = ()=>{\n        setSelectedNotification(null);\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        console.log(\"=== Marking Notification Debug ===\");\n        console.log(\"Notification ID:\", notificationId);\n        console.log(\"User ID:\", userId);\n        console.log(\"Session:\", session === null || session === void 0 ? void 0 : session.user);\n        if (!userId) {\n            console.error(\"No user ID found\");\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        if (!notificationId) {\n            console.error(\"No notification ID provided\");\n            setSnackbarMessage(\"Invalid notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            var _response_data;\n            console.log(\"Sending mutation with variables:\", {\n                notificationId: notificationId,\n                userId: userId\n            });\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            console.log(\"Mutation response:\", response);\n            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.addNotificationView) {\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                console.log(\"Notification marked successfully\");\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            } else {\n                console.error(\"No data returned from mutation\");\n                setSnackbarMessage(\"Failed to mark notification - no response\");\n                setSnackbarSeverity(\"error\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"=== Error marking notification ===\");\n            console.error(\"Error object:\", error);\n            console.error(\"GraphQL errors:\", error.graphQLErrors);\n            console.error(\"Network error:\", error.networkError);\n            console.error(\"Error message:\", error.message);\n            let errorMessage = \"Failed to mark notification\";\n            if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n                errorMessage = error.graphQLErrors[0].message;\n            } else if (error.networkError) {\n                errorMessage = \"Network error - please check your connection\";\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            setSnackbarMessage(errorMessage);\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 791,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 798,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 784,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 809,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 808,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 801,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: \"''\",\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 848,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 830,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.blue.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.blue.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.green.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.green.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.purple.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.purple.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 913,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 876,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 875,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 866,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications} notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"8px\",\n                                                    sm: \"10px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: (unseenCountData === null || unseenCountData === void 0 ? void 0 : unseenCountData.unseenNotificationsCount) || 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 939,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 923,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme,\n                                    selectedNotification: selectedNotification,\n                                    onNotificationClick: handleNotificationClick,\n                                    onBackToList: handleBackToList\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 818,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 771,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 770,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid white\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 1003,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 994,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_11__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 1016,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 1025,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1033,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1043,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1041,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1040,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1053,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1057,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1051,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1050,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1063,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1062,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1067,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1061,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1073,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1072,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1077,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1071,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1083,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1082,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1087,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1081,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1080,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1093,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1092,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_14__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1097,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1090,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1015,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_11__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 1005,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 986,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 978,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_22__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"wJg79I1fszMX9D61H1BtuoMZjmQ=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_15__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_10__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_28__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});