"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/Data/Notification.js":
/*!**********************************!*\
  !*** ./src/Data/Notification.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNewNotifications: function() { return /* binding */ getNewNotifications; },\n/* harmony export */   getNotifications: function() { return /* binding */ getNotifications; },\n/* harmony export */   getUnseenNotificationsCount: function() { return /* binding */ getUnseenNotificationsCount; },\n/* harmony export */   mutationAddNotificationView: function() { return /* binding */ mutationAddNotificationView; },\n/* harmony export */   mutationCreateNotification: function() { return /* binding */ mutationCreateNotification; },\n/* harmony export */   mutationMarkAllNotificationsAsSeen: function() { return /* binding */ mutationMarkAllNotificationsAsSeen; },\n/* harmony export */   mutationRemoveNotification: function() { return /* binding */ mutationRemoveNotification; },\n/* harmony export */   mutationUpdateNotification: function() { return /* binding */ mutationUpdateNotification; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query{\\n    notifications {\\n        id\\n        notification\\n        createdAt\\n       \\n    }\\n}\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation AddNotificationView($notificationId: ID!, $userId: ID!) {\\n    addNotificationView(notificationId: $notificationId, userId: $userId) {\\n      id\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query NewNotificationsForUser($userId: ID!) {\\n    newNotificationsForUser(id: $userId) {\\n      id\\n      notification\\n      createdAt\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nmutation CreateNotification(\\n  $notification: String!\\n#   $status: Boolean!\\n) {\\n  createNotification(createNotificationInput: {\\n    notification: $notification\\n    # status: $status\\n  }) {\\n    id,\\n    notification,\\n    # status\\n  }\\n}\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateNotification($id: ID!, $updateNotificationInput: UpdateNotificationInput!) {\\n    updateNotification(id: $id, updateNotificationInput: $updateNotificationInput) {\\n      id\\n      notification\\n      createdAt\\n      views {\\n        id\\n        name\\n        email\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RemoveNotification($id: ID!) {\\n    removeNotification(id: $id) {\\n        notification,\\n    }\\n  }\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query UnseenNotificationsCount($userId: ID!) {\\n    unseenNotificationsCount(userId: $userId)\\n  }\\n\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation MarkAllNotificationsAsSeen($userId: ID!) {\\n    markAllNotificationsAsSeen(userId: $userId)\\n  }\\n\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\n\nconst getNotifications = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\nconst mutationAddNotificationView = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\nconst getNewNotifications = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2());\nconst mutationCreateNotification = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3());\nconst mutationUpdateNotification = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4());\nconst mutationRemoveNotification = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject5());\nconst getUnseenNotificationsCount = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject6());\nconst mutationMarkAllNotificationsAsSeen = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject7());\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/Data/Notification.js\n"));

/***/ })

});