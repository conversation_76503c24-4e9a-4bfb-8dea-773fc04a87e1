"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject2());\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notification-details/\".concat(notificationData.id),\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        onClick: ()=>removeHandler(notificationData.id),\n                                                        sx: {\n                                                            p: 2,\n                                                            borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                                cursor: \"pointer\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\",\n                                                            position: \"relative\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 2,\n                                                                    alignItems: \"flex-start\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            width: 40,\n                                                                            height: 40,\n                                                                            borderRadius: \"50%\",\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            justifyContent: \"center\",\n                                                                            flexShrink: 0,\n                                                                            boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                                            sx: {\n                                                                                color: \"white\",\n                                                                                fontSize: \"20px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1,\n                                                                            minWidth: 0\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"body1\",\n                                                                                sx: {\n                                                                                    fontSize: \"14px\",\n                                                                                    fontWeight: 500,\n                                                                                    lineHeight: \"20px\",\n                                                                                    color: \"#333\",\n                                                                                    mb: 0.5,\n                                                                                    wordBreak: \"break-word\"\n                                                                                },\n                                                                                children: notificationData.notification\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"caption\",\n                                                                                sx: {\n                                                                                    fontSize: \"12px\",\n                                                                                    color: \"#666\",\n                                                                                    display: \"flex\",\n                                                                                    alignItems: \"center\",\n                                                                                    gap: 0.5\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                        sx: {\n                                                                                            fontSize: \"4px\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                        lineNumber: 335,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    position: \"absolute\",\n                                                                    left: 0,\n                                                                    top: 0,\n                                                                    bottom: 0,\n                                                                    width: \"3px\",\n                                                                    borderRadius: \"0 2px 2px 0\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, notificationData.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 434,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedNotification, setSelectedNotification] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getUnseenNotificationsCount, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 10000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\"\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    const [markAllAsSeen] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationMarkAllNotificationsAsSeen);\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.notifications);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.notifications)) {\n            const allNotifications = data.notifications;\n            const currentCount = allNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"All notifications received:\", allNotifications);\n            console.log(\"Count:\", currentCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 1000);\n            }\n            setNotifications(allNotifications);\n            setPreviousNotificationCount(currentCount);\n            console.log(\"Notification count updated to: \".concat(currentCount));\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 585,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = async ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        if (!wasOpen && userId) {\n            try {\n                await markAllAsSeen({\n                    variables: {\n                        userId\n                    }\n                });\n                refetchUnseenCount();\n                console.log(\"All notifications marked as seen\");\n            } catch (error) {\n                console.error(\"Error marking notifications as seen:\", error);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        setSelectedNotification(null); // Reset selected notification when closing\n    };\n    const handleNotificationClick = (notification)=>{\n        setSelectedNotification(notification);\n    };\n    const handleBackToList = ()=>{\n        setSelectedNotification(null);\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 699,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 706,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 717,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 716,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: \"''\",\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 756,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.blue.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.blue.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.green.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.green.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.purple.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.purple.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 784,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 774,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications} notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"8px\",\n                                                    sm: \"10px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: (unseenCountData === null || unseenCountData === void 0 ? void 0 : unseenCountData.unseenNotificationsCount) || 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 847,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 831,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme,\n                                    selectedNotification: selectedNotification,\n                                    onNotificationClick: handleNotificationClick,\n                                    onBackToList: handleBackToList\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 869,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 726,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 679,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid white\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 911,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 902,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 941,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 940,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 945,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 950,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 980,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 991,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1001,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1014,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1013,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 913,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 894,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 886,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1024,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1019,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"wJg79I1fszMX9D61H1BtuoMZjmQ=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});