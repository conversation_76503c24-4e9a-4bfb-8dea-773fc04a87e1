"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/WeatherComponent.jsx":
/*!*********************************************!*\
  !*** ./src/components/WeatherComponent.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Data/ApolloClient */ \"./src/Data/ApolloClient.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,CircularProgress,Container,Typography,useTheme!=!@mui/material */ \"__barrel_optimize__?names=Alert,Box,CircularProgress,Container,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HelperFunctions */ \"./src/components/HelperFunctions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst WeatherComponent = ()=>{\n    _s();\n    const [weatherData, setWeatherData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const theme = (0,_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const lat = position.coords.latitude;\n                const lon = position.coords.longitude;\n                fetchWeatherData(lat, lon);\n            }, ()=>{\n                // this function will be called if the user denies permission\n                const defaultLat = 24.6877;\n                const defaultLon = 46.7219;\n                fetchWeatherData(defaultLat, defaultLon);\n            });\n        } else {\n            setError(\"Geolocation is not supported by this browser.\");\n        }\n    }, []);\n    function fetchWeatherData(lat, lon) {\n        // Check if the backend weather endpoint exists, otherwise use mock data\n        fetch(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__.baseUrl, \"/weather?lat=\").concat(lat, \"&lon=\").concat(lon) // Updated to a more standard endpoint\n        ).then((response)=>{\n            if (!response.ok) {\n                // If the endpoint doesn't exist, use mock data\n                if (response.status === 404) {\n                    throw new Error(\"Weather service not available\");\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return response.json();\n        }).then((data)=>{\n            setWeatherData(data.data || data);\n            setLoading(false);\n        }).catch((error)=>{\n            console.warn(\"Weather API not available, using mock data:\", error.message);\n            // Use mock weather data when API is not available\n            setWeatherData({\n                name: \"Riyadh\",\n                main: {\n                    temp: 298.15,\n                    feels_like: 301.15 // 28°C in Kelvin\n                },\n                weather: [\n                    {\n                        main: \"Clear\",\n                        description: \"clear sky\",\n                        icon: \"01d\"\n                    }\n                ],\n                dt: Math.floor(Date.now() / 1000)\n            });\n            setLoading(false);\n        });\n    }\n    const formatDateTime = (unixTime)=>{\n        const date = new Date(unixTime * 1000);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 85,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n            severity: \"error\",\n            children: error\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 89,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!weatherData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No weather data available.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 93,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!weatherData.weather || !weatherData.main) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Weather data is incomplete.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 96,\n            columnNumber: 12\n        }, undefined);\n    }\n    const weatherIconUrl = \"/weatherIcons/\".concat(weatherData.weather[0].icon, \".svg\");\n    // const weatherIconUrl = `/weatherIcons/01d.png`;\n    return(// <Container>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        sx: {\n            background: theme.palette.background.secondary,\n            borderRadius: \"10px\",\n            boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n            padding: \"20px\",\n            // margin: \"75px 0px 20px 0px\", \n            height: \"100%\",\n            marginTop: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                variant: \"h6\",\n                fontSize: \"1.3rem\",\n                fontWeight: \"700\",\n                marginX: \"5px\",\n                children: \"Weather \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                sx: {\n                    // display: \"flex\",\n                    // flexDirection: \"column\",\n                    // justifyContent: \"center\",\n                    // background: theme.palette.background.secondary,\n                    // borderRadius: \"10px\",\n                    // boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n                    // padding: \"20px\",\n                    margin: \"75px 0px 20px 0px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                        variant: \"body1\",\n                        sx: {\n                            fontWeight: \"600\",\n                            fontSize: \"1rem\"\n                        },\n                        children: [\n                            \"Current Weather in \",\n                            weatherData.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        sx: {\n                            margin: \"10px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: \"600\",\n                                        lineHeight: \"0px\",\n                                        fontSize: \"1rem\"\n                                    },\n                                    children: (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_4__.getCurrentTime)()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                sx: {\n                                    margin: \"30px 0px 0px\",\n                                    display: \"flex\",\n                                    justifyContent: \"center\",\n                                    alignItems: \"center\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    // src='/weatherIcons/09d.svg'\n                                    src: weatherIconUrl,\n                                    alt: weatherData.weather[0].description,\n                                    width: 125,\n                                    height: 125,\n                                    style: {\n                                        WebkitFilter: \"drop-shadow(5px 5px 5px #666666)\",\n                                        filter: \"drop-shadow(5px 5px 5px #666666)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                sx: {\n                                    marginTop: \"10px\",\n                                    display: \"flex\",\n                                    justifyContent: \"space-around\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        marginLeft: \"20px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                            variant: \"h3\",\n                                            children: [\n                                                Math.round(weatherData.main.temp - 273.15),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sup\", {\n                                                    children: \"o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"C\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        sx: {\n                                            marginLeft: \"10px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    fontSize: \"1rem\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: weatherData.weather[0].main\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                sx: {\n                                                    width: \"100%\",\n                                                    fontSize: \"1rem\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                variant: \"body2\",\n                                                children: [\n                                                    \"RealFeel \",\n                                                    Math.round(weatherData.main.feels_like - 273.15),\n                                                    \"\\xb0C\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined));\n};\n_s(WeatherComponent, \"b1U2Hh1bAxGZAriWxe/OVE1OvOg=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = WeatherComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherComponent);\nvar _c;\n$RefreshReg$(_c, \"WeatherComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/WeatherComponent.jsx\n"));

/***/ })

});