import React, { useState } from "react";
import { signIn } from "next-auth/react";
import styles from "../styles/login.module.css";
import Image from "next/image";
import { useRouter } from "next/router";
import Divider from "@mui/material/Divider";
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  FormControlLabel,
  Checkbox,
  Grid,
  Paper,
  Slide,
  Typography,
  TextField,
  Box,
  Container,
  createTheme,
  Modal,
  useMediaQuery,
} from "@mui/material";
import { ThemeProvider } from "@mui/material";
import { IconButton } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { InputAdornment } from "@mui/material";

const LoginSlideForm = ({ isOpen, handleClose }) => {
  const router = useRouter();
  const theme = createTheme();
  const isSmallScreen = useMediaQuery("(max-width:600px)");
  const isMediumScreen = useMediaQuery(
    "(min-width:900px) and (max-width:1200px)"
  );

  const [remember, setRemember] = useState(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [btnDisable, setBtnDisable] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setBtnDisable(true);

    const result = await signIn("credentials", {
      redirect: false,
      username: username.toLowerCase().trim(),
      password,
    });

    if (result.error) {
      console.log(result);
      alert("Login failed");
      setBtnDisable(false);
    } else {
      // console.table([result, result.username, result.password]);
      console.log(result);
      setBtnDisable(false);
      handleClose();
      router.push("/");
    }
  };

  const backgroundImageProp = {
    backgroundImage: isSmallScreen ? "none" : `url(/images/login/login.png)`,
  };

  return (
    <ThemeProvider theme={theme}>
      <Modal
        sx={{
          "&:focus": {
            outline: "none",
            border: "none",
          },
        }}
        className="modal"
        open={isOpen}
        onClose={handleClose}
        closeAfterTransition
      >
        <Slide direction="up" in={isOpen} mountOnEnter unmountOnExit>
          <Container
            sx={{
              "&:focus": {
                outline: "none",
                border: "none",
              },
            }}
            disableGutters
          >
            <Grid
              container
              className={styles.main}
              justifyContent="center"
              alignItems="center"
              style={{ minHeight: "100vh" }}
            >
              <Grid item xs={12} sm={8} md={5} lg={4}>
                {" "}
                {/* Make sure `item` prop is included */}
                <Paper
                  style={{
                    marginLeft: "0px",
                    padding: isMediumScreen
                      ? "78px 20px 78px 20px"
                      : isSmallScreen
                      ? "70px 30px 70px 30px"
                      : "78px 20px 78px 20px",
                  }}
                >
                  <Box style={{ display: "flex", justifyContent: "center" }}>
                    <Image
                      style={{
                        marginBottom: "50px",
                      }}
                      src="/HassanaLogoL.png"
                      alt="hassana"
                      width={216}
                      height={24}
                    />
                  </Box>
                  <form onSubmit={handleSubmit}>
                    {/* Username input */}
                    <Typography>
                      <strong>Username</strong>
                    </Typography>
                    <TextField
                      style={{
                        borderRadius: "10px",
                        border: "2px solid #A665E1",
                      }}
                      fullWidth
                      type="email"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      placeholder="<EMAIL>"
                      // autoComplete="off"
                      defaultValue={"abc.HICUAT.local"}
                      margin="normal"
                    />
                    {/* Password input */}
                    {/* <Typography>
                      <strong>Password</strong>
                    </Typography>
                    <TextField
                      style={{
                        borderRadius: "10px",
                        border: "2px solid #A665E1",
                      }}
                      fullWidth
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      defaultValue={"aA1bB2cC3"}
                      // autoComplete="off"
                      placeholder="Password"
                      margin="normal"
                    /> */}
                    {/* Remember me checkbox */}
                    <Typography>
                      <strong>Password</strong>
                    </Typography>
                    <TextField
                      style={{
                        borderRadius: "10px",
                        border: "2px solid #A665E1",
                      }}
                      fullWidth
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Password"
                      margin="normal"
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle password visibility"
                              onClick={togglePasswordVisibility}
                              edge="end"
                            >
                              {showPassword ? (
                                <VisibilityOff />
                              ) : (
                                <Visibility />
                              )}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Stack direction="row">
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <FormControlLabel
                          onClick={() => setRemember(!remember)}
                          control={
                            <Checkbox
                              style={{ color: "#A665E1" }}
                              checked={remember}
                            />
                          }
                          label="Remember me"
                        />
                        <Typography
                          variant="body1"
                          component="span"
                          onClick={handleClose}
                          style={{
                            marginTop: "10px",
                            color: "#A665E1",
                            cursor: "pointer",
                          }}
                        >
                          Forgot password?
                        </Typography>
                      </Box>
                    </Stack>
                    {/* Sign in button */}
                    <Button
                      className={styles.button}
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={btnDisable ? true : false}
                    >
                      Sign in
                    </Button>
                  </form>
                </Paper>
              </Grid>
              {/* Right Side: Image */}
              <Grid
                item
                md={7}
                lg={8}
                {...backgroundImageProp}
                className={styles.container}
              >
                {" "}
                {/* Include `item` prop here as well */}
                <Box className={styles.text}>
                <Typography className={styles.mainText}>
                    We are Hassana Investment Company
                  </Typography>
                  <Divider
                        sx={{
                          height: "10px",
                          width: "10%",
                          marginTop:"10px",
                          backgroundColor:"#00BC82"
                        }}
                      />
                  <Typography className={styles.welcomeText}>
                  We enable a financially secure future for all Saudi generations
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Slide>
      </Modal>
    </ThemeProvider>
  );
};

export default LoginSlideForm;
