import authclient from "@/Data/AuthClientServer";
import { LOGIN_USER } from "@/Data/Auth";
import { client } from "@/Data/ApolloClient";

import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

export default NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { username, password } = credentials;

        try {
        
          const { data, error } = await authclient.query({
            query: LOGIN_USER,
            variables: { username, password },
          });
          console.log("+++++++data", data);
          console.log("+++++++++++++++++++++++++error", error);
          const { loginUser } = data;
          console.log("loginUser", loginUser);

          if (loginUser) {
            console.log(loginUser, "Login");
            return {
              ...loginUser,
              accessToken: loginUser.token || "test", // Ensure token is returned from loginUser
            };
          } else {
            console.log("No user found");
            return null;
          }
        } catch (error) {
          console.log("Error:", error.message);
          return null;
        }
      },
    }),
  ],
  session: {
    jwt: false, // You're using session-based auth, not JWT
  },
  jwt: {
    secret: "test",
    encryption: true,
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log('JWT----->',user);
      if (user) {
        token.id = user.id;
        token.role = user.role.toUpperCase();
        token.name = user.username;
        token.accessToken = user.accessToken; 
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.id;
      session.user.role = token.role;
      session.user.username = token.name;
      session.accessToken = token.accessToken;
      console.log('JSESSIOWT----->',session); 
      return session;
    },
  },
});