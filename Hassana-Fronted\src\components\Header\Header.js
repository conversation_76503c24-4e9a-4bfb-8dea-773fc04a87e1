
import { gql } from "@apollo/client";
import React, { useState, useContext, useRef, useEffect } from "react";
import { styled, useTheme } from "@mui/material/styles";
import MuiDrawer from "@mui/material/Drawer";
import {
  useSelectedColor,
  color,
  formatDateTimeUTC,
} from "../HelperFunctions";
import Box from "@mui/material/Box";
import useMediaQuery from "@mui/material/useMediaQuery";
import MuiAppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import List from "@mui/material/List";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import {
  Task,
  Notifications,
  DarkMode,
  LightMode,
  Campaign,
  Celebration,
  FormatQuote,
  NotificationsActiveRounded,
  LocalOffer,
  NotificationsActive,
} from "@mui/icons-material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import MenuIcon from "@mui/icons-material/Menu";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import SettingsIcon from "@mui/icons-material/Settings";
import NewspaperIcon from "@mui/icons-material/Newspaper";
import CircleIcon from "@mui/icons-material/Circle";
import Image from "next/image";
import Badge from "@mui/material/Badge";
import Link from "next/link";
import {
  Popper,
  Paper,
  ClickAwayListener,
  MenuItem,
  MenuList,
  ListItem,
  ListItemText,
  Collapse,
  ListItemIcon,
  ListItemButton,
  Slide,
  Zoom,
  keyframes,
} from "@mui/material";
import { useColor } from "../ColorContext";
import { DrawerContext } from "./DrawerContext";
import { useMode } from "../ModeContext";
import { useMutation, useQuery } from "@apollo/client";
import {
  getNewNotifications,
  getNotifications,
  getUnseenNotificationsCount,
  mutationMarkAllNotificationsAsSeen
} from "@/Data/Notification";
import { mutationAddNotificationView } from "@/Data/Announcement";
import { useSession } from "next-auth/react";
import { CircularProgress, Snackbar, Alert, Divider } from "@mui/material";
import { MainListItems, SecondaryListItems } from "../ListItems";
import Profile from "../Profile";
import { useRouter } from "next/router";

const drawerWidth = "17rem";

// Keyframes for notification animations
const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const shake = keyframes`
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 5px #ff4444; }
  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }
  100% { box-shadow: 0 0 5px #ff4444; }
`;

const AnimatedBadge = styled(Badge)(({ theme, hasNewNotifications }) => ({
  "& .MuiBadge-badge": {
    backgroundColor: "#ff4444",
    color: "white",
    fontWeight: "bold",
    fontSize: "12px",
    minWidth: "20px",
    height: "20px",
    borderRadius: "10px",
    border: "2px solid white",
    animation: hasNewNotifications
      ? `${pulse} 2s infinite, ${glow} 2s infinite`
      : "none",
    boxShadow: "0 2px 8px rgba(255, 68, 68, 0.3)",
  },
}));

const AnimatedNotificationIcon = styled(Box)(({ theme, hasNewNotifications }) => ({
  animation: hasNewNotifications ? `${shake} 0.5s ease-in-out` : "none",
  "&:hover": {
    transform: "scale(1.1)",
    transition: "transform 0.2s ease-in-out",
  },
}));

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: open ? drawerWidth : 0,
  width: open ? `calc(100% - ${drawerWidth})` : "100%",
  [theme.breakpoints.up("sm")]: {
    marginLeft: open ? drawerWidth : theme.spacing(9),
    width: open
      ? `calc(100% - ${drawerWidth})`
      : `calc(100% - ${theme.spacing(9)})`,
  },
  [theme.breakpoints.down("xs")]: {
    marginLeft: 0,
    width: "100%",
  },
  ...(open && {
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  "& .MuiDrawer-paper": {
    backgroundColor: theme.palette.background.secondary,
    position: "relative",
    whiteSpace: "nowrap",
    width: open ? drawerWidth : theme.spacing(7),
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.complex,
    }),
    boxSizing: "border-box",
    ...(!open && {
      overflowX: "hidden",
      transition: theme.transitions.create("width", {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.leavingScreen,
      }),
      width: theme.spacing(7),
      [theme.breakpoints.up("sm")]: {
        width: theme.spacing(9),
      },
      [theme.breakpoints.down("xs")]: {
        width: "100%",
      },
    }),
  },
}));

// Enhanced Social Media Style Notification Popper
const SocialNotificationPopper = ({
  open,
  anchorEl,
  onClose,
  notifications,
  loading,
  removeHandler,
  selectedColor,
  theme,
  selectedNotification,
  onNotificationClick,
  onBackToList,
}) => (
  <Popper
    open={open}
    anchorEl={anchorEl}
    role={undefined}
    transition
    sx={{
      maxHeight: notifications.length > 4 ? "70vh" : "auto",
      overflowY: notifications.length > 4 ? "auto" : "visible",
      zIndex: 9999,
      width: "400px",
      maxWidth: "90vw",
    }}
    disablePortal
    popperOptions={{
      modifiers: [
        { name: "offset", options: { offset: [0, 15] } },
        { name: "preventOverflow", options: { padding: 20 } },
      ],
    }}
  >
    {({ TransitionProps }) => (
      <Zoom {...TransitionProps} timeout={300}>
        <Paper
          elevation={24}
          sx={{
            borderRadius: "16px",
            overflow: "hidden",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            backdropFilter: "blur(10px)",
            background:
              "linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)",
            boxShadow:
              "0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)",
          }}
        >
          <ClickAwayListener onClickAway={onClose}>
            <Box sx={{ maxWidth: "400px", minWidth: "320px" }}>
              <Box
                sx={{
                  p: 2,
                  borderBottom: "1px solid rgba(0,0,0,0.1)",
                  background: theme.palette.background.header,
                  color: theme.palette.text.white,
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <NotificationsActive sx={{ fontSize: "20px" }} />
                  Notifications
                  {notifications.length > 0 && (
                    <Box
                      sx={{
                        backgroundColor: "rgba(255,255,255,0.2)",
                        borderRadius: "12px",
                        px: 1,
                        py: 0.5,
                        fontSize: "12px",
                        fontWeight: "bold",
                      }}
                    >
                      {notifications.length}
                    </Box>
                  )}
                </Typography>
              </Box>
              <Box sx={{ maxHeight: "400px", overflowY: "auto" }}>
                {loading ? (
                  <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                    <CircularProgress color="primary" />
                  </Box>
                ) : selectedNotification ? (
                  // Show notification details
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                      <IconButton
                        onClick={onBackToList}
                        sx={{ mr: 1, p: 0.5 }}
                        size="small"
                      >
                        <ArrowBackIcon sx={{ fontSize: "18px" }} />
                      </IconButton>
                      <Typography variant="h6" sx={{ fontSize: "16px", fontWeight: 600 }}>
                        Notification Details
                      </Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Typography
                      variant="body1"
                      sx={{
                        fontSize: "14px",
                        lineHeight: 1.6,
                        color: theme.palette.text.primary,
                        mb: 2,
                      }}
                    >
                      {selectedNotification.notification}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: "12px",
                        color: theme.palette.text.secondary,
                        fontStyle: "italic",
                      }}
                    >
                      {new Date(selectedNotification.createdAt).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </Typography>
                  </Box>
                ) : (
                  <>
                    {notifications.length > 0 ? (
                      notifications.map((notificationData, index) => (
                        <Link
                          key={notificationData.id}
                          href={{
                            pathname: "/notifications",
                            query: { notificationId: notificationData.id },
                          }}
                          passHref
                        >
                          <Box
                            onClick={() => removeHandler(notificationData.id)}
                            sx={{
                              p: 2,
                              borderBottom:
                                index < notifications.length - 1
                                  ? "1px solid rgba(0,0,0,0.05)"
                                  : "none",
                              "&:hover": {
                                backgroundColor: "rgba(102, 126, 234, 0.05)",
                                cursor: "pointer",
                              },
                              transition: "all 0.2s ease-in-out",
                              position: "relative",
                            }}
                          >
                            <Box
                              sx={{ display: "flex", gap: 2, alignItems: "flex-start" }}
                            >
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: "50%",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  flexShrink: 0,
                                  boxShadow:
                                    "0 4px 12px rgba(102, 126, 234, 0.3)",
                                }}
                              >
                                <NotificationsActive
                                  sx={{ color: "white", fontSize: "20px" }}
                                />
                              </Box>
                              <Box sx={{ flex: 1, minWidth: 0 }}>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontSize: "14px",
                                    fontWeight: 500,
                                    lineHeight: "20px",
                                    color: "#333",
                                    mb: 0.5,
                                    wordBreak: "break-word",
                                  }}
                                >
                                  {notificationData.notification}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{
                                    fontSize: "12px",
                                    color: "#666",
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 0.5,
                                  }}
                                >
                                  <CircleIcon sx={{ fontSize: "4px" }} />
                                  {formatDateTimeUTC(notificationData.createdAt)}
                                </Typography>
                              </Box>
                            </Box>
                            {index === 0 && (
                              <Box
                                sx={{
                                  position: "absolute",
                                  left: 0,
                                  top: 0,
                                  bottom: 0,
                                  width: "3px",
                                  borderRadius: "0 2px 2px 0",
                                }}
                              />
                            )}
                          </Box>
                        </Link>
                      ))
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          p: 4,
                          textAlign: "center",
                        }}
                      >
                        <NotificationsActive
                          sx={{ fontSize: "48px", color: "#ccc", mb: 2 }}
                        />
                        <Typography variant="h6" sx={{ color: "#666", mb: 1 }}>
                          No new notifications
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#999" }}>
                          You&apos;re all caught up!
                        </Typography>
                      </Box>
                    )}
                    {notifications.length > 0 && (
                      <Box
                        sx={{
                          p: 2,
                          borderTop: "1px solid rgba(0,0,0,0.1)",
                          background: "rgba(102, 126, 234, 0.02)",
                        }}
                      >
                        <Link href="/notifications" passHref>
                          <Typography
                            component="a"
                            variant="body2"
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "14px",
                              fontWeight: 600,
                              textDecoration: "none",
                              color: "#667eea",
                              "&:hover": {
                                color: "#764ba2",
                                transform: "translateX(2px)",
                              },
                              transition: "all 0.2s ease-in-out",
                            }}
                          >
                            View All Notifications
                            <ArrowForwardIcon sx={{ fontSize: "16px" }} />
                          </Typography>
                        </Link>
                      </Box>
                    )}
                  </>
                )}
              </Box>
            </Box>
          </ClickAwayListener>
        </Paper>
      </Zoom>
    )}
  </Popper>
);

export default function Header() {
  const { open, setOpen } = useContext(DrawerContext);
  const { mode, setMode } = useMode();
  const { setGlobalColor } = useColor();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { data: session, status } = useSession();
  const isAdmin = session?.user?.role === "ADMIN";
  const notificationAnchorRef = useRef(null);

  const [moreItem, setMoreItem] = useState(false);
  const [notificationOpen, setNotificationOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedIcon, setSelectedIcon] = useState(<LightMode />);
  const [notifications, setNotifications] = useState([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [hasNewNotifications, setHasNewNotifications] = useState(false);
  const [previousNotificationCount, setPreviousNotificationCount] = useState(0);
  const [lastNotificationTime, setLastNotificationTime] = useState(null);
  const [enablePolling, setEnablePolling] = useState(true);
  const [selectedNotification, setSelectedNotification] = useState(null);

  const logoSource = "/HassanaLogoD.png";
  const drawerVariant = isMobile && !open ? "temporary" : "permanent";
  const selectedColor = useSelectedColor(color);

  const userId = session?.user?.id || session?.user?.user_id;

  const { loading, error, data } = useQuery(getNotifications, {
    variables: { userId: userId ? userId : undefined },
    skip: !userId || status !== "authenticated",
    pollInterval: enablePolling ? 30000 : 0,
    fetchPolicy: "cache-and-network",
    errorPolicy: "all",
    notifyOnNetworkStatusChange: true,
  });

  const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = useQuery(getUnseenNotificationsCount, {
    variables: { userId: userId ? userId : undefined },
    skip: !userId || status !== "authenticated",
    pollInterval: enablePolling ? 10000 : 0,
    fetchPolicy: "cache-and-network",
    errorPolicy: "all",
  });

  const [addNotificationView] = useMutation(mutationAddNotificationView);
  const [markAllAsSeen] = useMutation(mutationMarkAllNotificationsAsSeen);

  const playNotificationSound = () => {
    try {
      const audio = new Audio("/sounds/notification.mp3");
      audio.volume = 0.5;
      audio.play().catch((e) => console.log("Could not play notification sound:", e));
    } catch (error) {
      console.log("Notification sound not available:", error);
    }
  };

  const showBrowserNotification = (message) => {
    if ("Notification" in window && Notification.permission === "granted") {
      new Notification("Hassana Portal", {
        body: message,
        icon: "/favicon.ico",
        badge: "/favicon.ico",
        tag: "hassana-notification",
        requireInteraction: false,
        silent: false,
      });
    }
  };

  useEffect(() => {
    if ("Notification" in window && Notification.permission === "default") {
      Notification.requestPermission();
    }
  }, []);

  useEffect(() => {
    if (status === "authenticated" && !userId) {
      console.warn("User ID is missing in authenticated session:", session?.user);
    }
    console.log("=== Session Debug ===");
    console.log("Session Status:", status);
    console.log("User Object:", session?.user);
    console.log("User ID:", userId);
  }, [session, status, userId]);

  useEffect(() => {
    console.log("=== Notification Backend Debug ===");
    console.log("Loading:", loading);
    console.log("Error:", error);
    console.log("Data:", data?.notifications);
    console.log("User ID:", userId);

    if (!loading && !error && data?.notifications) {
      const allNotifications = data.notifications;
      const currentCount = allNotifications.length;

      console.log("Backend Connected Successfully!");
      console.log("All notifications received:", allNotifications);
      console.log("Count:", currentCount);

      setEnablePolling(true);

      if (
        currentCount > previousNotificationCount &&
        previousNotificationCount > 0
      ) {
        setHasNewNotifications(true);
        setLastNotificationTime(Date.now());

        playNotificationSound();

        if (currentCount > previousNotificationCount) {
          const newNotificationCount = currentCount - previousNotificationCount;
          const message =
            newNotificationCount === 1
              ? "You have a new notification!"
              : `You have ${newNotificationCount} new notifications!`;

          showBrowserNotification(message);

          setSnackbarMessage(message);
          setSnackbarSeverity("info");
          setSnackbarOpen(true);
        }

        setTimeout(() => {
          setHasNewNotifications(false);
        }, 1000);
      }

      setNotifications(allNotifications);
      setPreviousNotificationCount(currentCount);
      console.log(`Notification count updated to: ${currentCount}`);
    } else if (error) {
      console.error("Backend Connection Error:", error);
      if (error.graphQLErrors) {
        console.error("GraphQL Errors:", error.graphQLErrors.map((e) => e.message));
      }
      if (error.networkError) {
        console.error("Network Error:", error.networkError);
      }

      setEnablePolling(false);

      setSnackbarMessage("Failed to load notifications. Retrying in 30 seconds...");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);

      setTimeout(() => {
        setEnablePolling(true);
      }, 30000);
    } else if (!userId) {
      console.warn("No user ID found in session");
    } else if (!loading && !data) {
      console.warn("No data received from backend");
    }
  }, [loading, error, data, previousNotificationCount, userId]);

  if (status === "loading") {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
        <CircularProgress color="primary" />
      </Box>
    );
  }

  const toggleDrawer = () => setOpen(!open);
  const handleSetMoreItemClick = () => setMoreItem(!moreItem);
  const handleClick = (event) => setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);
  const handleNotificationToggle = async () => {
    const wasOpen = notificationOpen;
    setNotificationOpen((prev) => !prev);

    if (!wasOpen && userId) {
      try {
        await markAllAsSeen({
          variables: { userId }
        });

        refetchUnseenCount();

        console.log("All notifications marked as seen");
      } catch (error) {
        console.error("Error marking notifications as seen:", error);
      }
    }
  };

  const handleNotificationClose = (event) => {
    if (notificationAnchorRef.current?.contains(event.target)) return;
    setNotificationOpen(false);
    setSelectedNotification(null); // Reset selected notification when closing
  };

  const handleNotificationClick = (notification) => {
    setSelectedNotification(notification);
  };

  const handleBackToList = () => {
    setSelectedNotification(null);
  };

  const handleThemeChange = (theme, icon) => {
    setMode(theme);
    setSelectedIcon(icon);
    handleClose();
  };

  const handleColorChange = (color, icon) => {
    setGlobalColor(color);
    setSelectedIcon(icon);
    handleClose();
  };

  const removeAnnouncementHandler = async (notificationId) => {
    if (!userId) {
      setSnackbarMessage("User not authenticated");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    try {
      const response = await addNotificationView({
        variables: {
          notificationId: notificationId,
          userId: userId,
        },
      });

      if (response.data.addNotificationView) {
        const updatedNotifications = notifications.filter(
          (n) => n.id !== notificationId
        );
        setNotifications(updatedNotifications);
        setPreviousNotificationCount(updatedNotifications.length);

        setSnackbarMessage("Notification marked as viewed");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error("Error marking notification:", error);
      setSnackbarMessage("Failed to mark notification");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  return (
    <>
      {(mode === "light" || mode === "dark") && (
        <AppBar position="fixed" open={open}>
          <Toolbar
            sx={{
              position: "absolute",
              width: "100%",
              backgroundColor: theme.palette.background.header,
              zIndex: 1,
              borderTop: `4px solid ${theme.palette.text.purple}`,
              borderBottom: `4px solid ${theme.palette.text.purple}`,
              [theme.breakpoints.down("xs")]: {
                flexDirection: "column",
              },
            }}
          >
            <IconButton
              edge="start"
              aria-label="Toggle drawer"
              onClick={toggleDrawer}
              sx={{ marginRight: "15px" }}
            >
              {open ? (
                <Image
                  src="/NavIcons/left_hamburger.svg"
                  alt="Close drawer"
                  width={24}
                  height={24}
                />
              ) : (
                <MenuIcon sx={{ color: theme.palette.text.white }} />
              )}
            </IconButton>
            <Typography
              component="h1"
              variant="h6"
              color="inherit"
              noWrap
              sx={{ flexGrow: 1 }}
            >
              <Link href="/">
                <Image
                  src={logoSource}
                  alt="Hassana Logo"
                  loading="lazy"
                  width={180}
                  height={42}
                />
              </Link>
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: { xs: 0.5, sm: 1, md: 1.5 },
                flexShrink: 0,
                [theme.breakpoints.down("xs")]: {
                  flexDirection: "row",
                  gap: 0.25,
                },
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  "&::after": {
                    content: "''",
                    position: "absolute",
                    right: "-8px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    width: "1px",
                    height: "24px",
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                    [theme.breakpoints.down("sm")]: {
                      display: "none",
                    },
                  },
                }}
              >
                <IconButton
                  aria-label="Change theme or color"
                  aria-controls="theme-menu"
                  aria-haspopup="true"
                  onClick={handleClick}
                  sx={{
                    color: "inherit",
                    padding: { xs: "6px", sm: "8px" },
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      transform: "scale(1.05)",
                    },
                    transition: "all 0.2s ease-in-out",
                  }}
                >
                  {selectedIcon}
                </IconButton>
              </Box>
              <Popper
                id="theme-menu"
                open={Boolean(anchorEl)}
                anchorEl={anchorEl}
                placement="bottom-end"
                transition
                sx={{ zIndex: 10000 }}
              >
                {({ TransitionProps }) => (
                  <Slide {...TransitionProps} direction="down" timeout={350}>
                    <Paper
                      sx={{
                        background: theme.palette.background.secondary,
                        borderRadius: "25px",
                      }}
                    >
                      <ClickAwayListener onClickAway={handleClose}>
                        <MenuList autoFocusItem={Boolean(anchorEl)}>
                          <MenuItem
                            onClick={() => handleThemeChange("light", <LightMode />)}
                          >
                            <LightMode />
                          </MenuItem>
                          <MenuItem
                            onClick={() => handleThemeChange("dark", <DarkMode />)}
                          >
                            <DarkMode />
                          </MenuItem>
                          <MenuItem
                            onClick={() =>
                              handleColorChange("blue", <CircleIcon sx={{ fill: theme.palette.blue.main }} />)
                            }
                          >
                            <CircleIcon sx={{ fill: theme.palette.blue.main }} />
                          </MenuItem>
                          <MenuItem
                            onClick={() =>
                              handleColorChange("green", <CircleIcon sx={{ fill: theme.palette.green.main }} />)
                            }
                          >
                            <CircleIcon sx={{ fill: theme.palette.green.main }} />
                          </MenuItem>
                          <MenuItem
                            onClick={() =>
                              handleColorChange("purple", <CircleIcon sx={{ fill: theme.palette.purple.main }} />)
                            }
                          >
                            <CircleIcon sx={{ fill: theme.palette.purple.main }} />
                          </MenuItem>
                        </MenuList>
                      </ClickAwayListener>
                    </Paper>
                  </Slide>
                )}
              </Popper>
              <Box sx={{ position: "relative" }}>
                <AnimatedNotificationIcon hasNewNotifications={hasNewNotifications}>
                  <IconButton
                    color="inherit"
                    ref={notificationAnchorRef}
                    onClick={handleNotificationToggle}
                    aria-label={`Show ${notifications.length} notifications} notifications`}
                    sx={{
                      position: "relative",
                      color: "inherit",
                      padding: { xs: "8px", sm: "10px" },
                      "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        transform: "scale(1.05)",
                      },
                      transition: "all 0.2s ease-in-out",
                    }}
                  >
                    <AnimatedBadge
                      badgeContent={unseenCountData?.unseenNotificationsCount || 0}
                      hasNewNotifications={hasNewNotifications}
                      max={99}
                    >
                      {notifications.length > 0 ? (
                        <NotificationsActive
                          sx={{
                            color: hasNewNotifications ? "#ff4444" : "inherit",
                            fontSize: { xs: "20px", sm: "24px" },
                            filter: hasNewNotifications
                              ? "drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))"
                              : "none",
                          }}
                        />
                      ) : (
                        <Notifications sx={{ fontSize: { xs: "20px", sm: "24px" } }} />
                      )}
                    </AnimatedBadge>
                  </IconButton>
                </AnimatedNotificationIcon>
              </Box>
              <SocialNotificationPopper
                open={notificationOpen}
                anchorEl={notificationAnchorRef.current}
                onClose={handleNotificationClose}
                notifications={notifications}
                loading={loading}
                removeHandler={removeAnnouncementHandler}
                selectedColor={selectedColor}
                theme={theme}
                selectedNotification={selectedNotification}
                onNotificationClick={handleNotificationClick}
                onBackToList={handleBackToList}
              />
            </Box>
          </Toolbar>
        </AppBar>
      )}
      <Drawer
        variant={drawerVariant}
        open={open}
        sx={{
          zIndex: 2,
          borderRight: mode === "light" ? "1px solid white" : "none",
        }}
      >
        <Box
          sx={{
            backgroundColor: theme.palette.background.primary,
            margin: "10px",
            borderRadius: "0.625rem",
            height: "100%",
          }}
        >
          <Toolbar
            sx={{
              display: "flex",
              alignItems: "center",
              marginTop: "auto",
              justifyContent: "flex-end",
              px: [1],
            }}
          >
            <Profile />
          </Toolbar>
          <List
            component="nav"
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
              height: "80vh",
              marginTop: "20px",
            }}
          >
            <Box>
              <MainListItems />
              {isAdmin && (
                <>
                  <ListItemButton onClick={handleSetMoreItemClick}>
                    <ListItemIcon>
                      <SettingsIcon
                        sx={{ color: mode !== "light" ? "white" : "inherit" }}
                      />
                    </ListItemIcon>
                    <ListItemText primary="Admin Settings" />
                    {moreItem ? <ExpandLess /> : <ExpandMore />}
                  </ListItemButton>
                  <Collapse in={moreItem} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      <Link href="/admin/news" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <NewspaperIcon
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="News" />
                        </ListItemButton>
                      </Link>
                      <Link href="/admin/announcements" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <Campaign
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Announcements" />
                        </ListItemButton>
                      </Link>
                      <Link href="/admin/events" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <Celebration
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Events" />
                        </ListItemButton>
                      </Link>
                      <Link href="/admin/quotes" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <FormatQuote
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Quotes" />
                        </ListItemButton>
                      </Link>
                      <Link href="/adminOffer" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <LocalOffer
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Offers" />
                        </ListItemButton>
                      </Link>
                      <Link href="/admin/notifications" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <NotificationsActiveRounded
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Notifications" />
                        </ListItemButton>
                      </Link>
                      <Link href="/admin/leaves" passHref>
                        <ListItemButton sx={{ height: 30 }} size="sm">
                          <ListItemIcon>
                            <Task
                              sx={{ color: mode !== "light" ? "white" : "inherit" }}
                            />
                          </ListItemIcon>
                          <ListItemText primary="Leaves" />
                        </ListItemButton>
                      </Link>
                    </List>
                  </Collapse>
                </>
              )}
            </Box>
            <Box>
              <SecondaryListItems />
            </Box>
          </List>
        </Box>
      </Drawer>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
      >
        <Alert
          severity={snackbarSeverity}
          onClose={() => setSnackbarOpen(false)}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}
