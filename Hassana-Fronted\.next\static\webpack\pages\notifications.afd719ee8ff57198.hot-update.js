"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/pages/notifications.js":
/*!************************************!*\
  !*** ./src/pages/notifications.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Divider */ \"./node_modules/@mui/material/Divider/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NotificationButtons */ \"./src/components/NotificationButtons.jsx\");\n/* harmony import */ var _components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AnnouncementCard */ \"./src/components/AnnouncementCard.js\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/NotificationCard */ \"./src/components/NotificationCard.jsx\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Dashboard */ \"./src/components/Dashboard.jsx\");\n/* harmony import */ var _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _Data_Events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/Data/Events */ \"./src/Data/Events.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Booking__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Booking */ \"./src/Data/Booking.js\");\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/auth/withAuth */ \"./src/components/auth/withAuth.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ResponsiveText = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(\"div\")((param)=>{\n    let { theme } = param;\n    return {\n        color: \"#1B3745\",\n        fontSize: \"16.02px\",\n        fontFamily: \"Helvetica\",\n        fontWeight: 400,\n        wordWrap: \"break-word\",\n        maxWidth: \"100%\"\n    };\n});\nconst ResponsiveBox = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box)((param)=>{\n    let { theme, backgroundColor, disableBorder, isCurrentNotification } = param;\n    return {\n        width: \"100%\",\n        height: \"auto !important\",\n        background: backgroundColor || \"white\",\n        backgroundColor: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : theme.palette.background.secondary,\n        boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.05)\",\n        borderRadius: 10,\n        position: \"relative\",\n        \"&::before\": !disableBorder && {\n            content: '\"\"',\n            position: \"absolute\",\n            top: 0,\n            left: 0.6,\n            width: \"5px\",\n            height: \"100%\",\n            background: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : isCurrentNotification ? \"linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)\" : \"none\",\n            borderRadius: \"20px 0 0 20px\"\n        }\n    };\n});\n_c = ResponsiveBox;\nconst Notifications = ()=>{\n    var _notificationData_notifications;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const { notificationId } = router.query; // Get notificationId from query params\n    const { loading: eventLoading, data: eventData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Events__WEBPACK_IMPORTED_MODULE_8__.getEvents);\n    const { loading: notificationLoading, data: notificationData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications);\n    const { loading: bookingLoading, data: bookingData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Booking__WEBPACK_IMPORTED_MODULE_10__.getBookings);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openModal, setOpenModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAnnouncement, setSelectedAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meetings, setMeetings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeButton, setActiveButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const isWideScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1150px)\");\n    const isLargeTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1079px)\");\n    const isTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1060px)\");\n    const isSmallTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:967px)\");\n    const isWideMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:869px)\");\n    const isMediumMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:823px)\");\n    const isNarrowMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:528px)\");\n    const isStandardDesktop = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1439px)\");\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme)();\n    const { color } = (0,_components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const selectedColor = (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor)(color);\n    // Handle navigation from query params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (notificationId && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            // Find the notification directly in notifications data\n            const notification = notificationData.notifications.find((notif)=>notif.id === notificationId);\n            if (notification) {\n                // Switch to notifications tab and select the notification\n                setDisplay(\"notifications\");\n                setActiveButton(\"notifications\");\n                // Find the index in the notifications array\n                const notifIndex = notificationData.notifications.findIndex((notif)=>notif.id === notificationId);\n                // Adjust index based on how notifications are displayed (newest first)\n                const adjustedIndex = notifIndex === notificationData.notifications.length - 1 ? 0 : notificationData.notifications.length - 1 - notifIndex;\n                setSelectedCard(adjustedIndex);\n                setSelectedAnnouncement(notification);\n            }\n        } else if (!notificationId) {\n            // If no notificationId in URL (like when clicking \"View All Notifications\"), reset selection\n            setSelectedCard(null);\n            setSelectedAnnouncement(null);\n        }\n    }, [\n        notificationId,\n        notificationData\n    ]);\n    const handleButtonClick = (buttonType)=>{\n        setDisplay(buttonType);\n        setActiveButton(buttonType);\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!notificationLoading && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        notificationLoading,\n        notificationData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (eventData && eventData.events) {\n            setEvents(eventData.events);\n        }\n        if (bookingData && bookingData.bookings) {\n            setMeetings(bookingData.bookings);\n        }\n        if (notificationData && notificationData.notifications) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        eventData,\n        bookingData,\n        notificationData\n    ]);\n    const handleCardClick = (index)=>{\n        setSelectedCard(index);\n        let selectedItem = null;\n        if (display === \"all\") {\n            selectedItem = combinedItems[index];\n        } else if (display === \"events\") {\n            selectedItem = index < currentEvents.length ? currentEvents[index] : previousEvents[index - currentEvents.length];\n        } else if (display === \"notifications\") {\n            selectedItem = index === 0 ? notifications[notifications.length - 1] : notifications.slice(0, -1)[index - 1];\n        } else if (display === \"meetings\") {\n            selectedItem = index < upcomingMeetings.length ? upcomingMeetings[index] : previousMeetings[index - upcomingMeetings.length];\n        }\n        setSelectedAnnouncement(selectedItem);\n    };\n    const handleOpenModal = (announcement)=>{\n        setSelectedAnnouncement(announcement);\n        setOpenModal(true);\n    };\n    const handleReset = ()=>{\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n        // Remove notificationId from URL\n        router.push(\"/notifications\", undefined, {\n            shallow: true\n        });\n    };\n    const handleCloseModal = ()=>{\n        setOpenModal(false);\n        setSelectedAnnouncement(null);\n    };\n    const currentDate = new Date();\n    const startOfWeek = currentDate.getDate() - currentDate.getDay();\n    const endOfWeek = startOfWeek + 6;\n    const currentWeekStart = new Date(currentDate);\n    currentWeekStart.setDate(startOfWeek);\n    const currentWeekEnd = new Date(currentDate);\n    currentWeekEnd.setDate(endOfWeek);\n    const currentEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate >= currentWeekStart && eventDate <= currentWeekEnd;\n    });\n    const previousEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate < currentWeekStart;\n    });\n    const upcomingMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) >= currentDate).sort((a, b)=>new Date(a.startTime) - new Date(b.startTime));\n    const previousMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) < currentDate).sort((a, b)=>new Date(b.startTime) - new Date(a.startTime));\n    const currentNotifications = notifications.filter((notification)=>new Date(notification.createdAt) >= currentWeekStart).sort((a, b)=>new Date(a.createdAt) - new Date(b.createdAt));\n    const previousNotifications = notifications.filter((notification)=>new Date(notification.createdAt) < currentWeekStart).sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n    const combinedItems = [\n        ...events.map((item)=>({\n                ...item,\n                type: \"event\"\n            })),\n        ...meetings.map((item)=>({\n                ...item,\n                type: \"meeting\"\n            })),\n        ...notifications.map((item)=>({\n                ...item,\n                type: \"notification\"\n            }))\n    ].sort((a, b)=>{\n        const dateA = a.createdAt || a.startTime || a.date;\n        const dateB = b.createdAt || b.startTime || b.date;\n        return new Date(dateB) - new Date(dateA);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                sx: {\n                    display: \"flex\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                    component: \"main\",\n                    sx: {\n                        background: selectedColor === theme.palette.background.primary ? theme.palette.background.secondary : selectedColor,\n                        paddingLeft: isLargeTablet ? \"50px\" : \"\",\n                        paddingRight: isLargeTablet ? \"50px\" : \"\",\n                        width: isMediumMobile ? \"100%\" : isWideMobile ? \"45rem\" : isSmallTablet ? \"48rem\" : isTablet ? \"54rem\" : \"60vw\",\n                        margin: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginLeft: \"15px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                        variant: \"h5\",\n                                        style: {\n                                            marginTop: \"32px\",\n                                            fontSize: \"16.024px\",\n                                            marginLeft: isStandardDesktop ? \"16px\" : \"\"\n                                        },\n                                        children: \"Latest updates from Hassana\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"h1\",\n                                            style: {\n                                                fontSize: isWideScreen ? \"22px\" : \"27.47px\",\n                                                fontWeight: \"700\",\n                                                marginTop: \"0\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"15px\"\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                paddingRight: \"25px\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"20px\",\n                                                marginTop: \"20px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                handleButtonClick: handleButtonClick,\n                                                activeButton: activeButton,\n                                                isNarrowMobile: isNarrowMobile,\n                                                isWideScreen: isWideScreen\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedCard !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    onClick: handleReset,\n                                    style: {\n                                        marginLeft: isStandardDesktop ? \"20px\" : \"5px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\",\n                                        color: \"#888\",\n                                        gap: 5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icons/arrow-left.svg\",\n                                            alt: \"arrow\",\n                                            height: 16,\n                                            width: true,\n                                            parametern: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"8px\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            sx: {\n                                                width: \"15px\",\n                                                backgroundColor: \"#A7A7A7\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginTop: \"2px\"\n                                            },\n                                            children: (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 333,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"4px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 358,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 303,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                padding: \"3%\",\n                                height: \"70vh\",\n                                overflow: \"auto\",\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 12,\n                                lg: 12,\n                                order: {\n                                    xs: 2,\n                                    sm: 2,\n                                    md: 2,\n                                    lg: 1,\n                                    xl: 1\n                                },\n                                children: [\n                                    display === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading || eventLoading || bookingLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 393,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: combinedItems.map((item, index)=>{\n                                                const isEvent = item.type === \"event\";\n                                                const isMeeting = item.type === \"meeting\";\n                                                const isNotification = item.type === \"notification\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: isNotification,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\",\n                                                        background: isNotification || isMeeting ? theme.palette.background.secondary : undefined\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        isEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.date : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isMeeting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.startTime : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isNotification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: item.notification,\n                                                            isSelected: selectedCard === index,\n                                                            isCurrentNotification: true,\n                                                            showDate: selectedCard === index,\n                                                            date: selectedCard === index ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(item.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 29\n                                                }, undefined);\n                                            })\n                                        }, void 0, false)\n                                    }, void 0, false),\n                                    display === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            currentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        isCurrentNotification: true,\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        date: selectedCard === index ? event.date : null,\n                                                        isSelected: selectedCard === index\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Events\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + currentEvents.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + currentEvents.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + currentEvents.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        isSelected: selectedCard === index + currentEvents.length,\n                                                        date: selectedCard === index + currentEvents.length ? event.date : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true),\n                                    display === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 23\n                                        }, undefined) : (notificationData === null || notificationData === void 0 ? void 0 : (_notificationData_notifications = notificationData.notifications) === null || _notificationData_notifications === void 0 ? void 0 : _notificationData_notifications.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"16px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== 0 ? \"none\" : \"block\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        notification: notificationData.notifications[notificationData.notifications.length - 1].notification,\n                                                        isSelected: selectedCard === 0,\n                                                        isCurrentNotification: true,\n                                                        showDate: selectedCard === 0,\n                                                        date: selectedCard === 0 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notificationData.notifications[notificationData.notifications.length - 1].createdAt)) : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                notificationData.notifications.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    marginBottom: \"24px\",\n                                                    marginTop: \"24px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderBottom: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Previous Notifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderTop: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                notificationData.notifications.slice(0, -1).map((notification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                        sx: {\n                                                            background: theme.palette.background.secondary,\n                                                            padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                            marginTop: \"20px\",\n                                                            display: selectedCard !== null && selectedCard !== index + 1 ? \"none\" : \"block\",\n                                                            height: selectedCard === index + 1 ? \"auto\" : \"170px\"\n                                                        },\n                                                        onClick: ()=>handleCardClick(index + 1),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: notification.notification,\n                                                            isSelected: selectedCard === index + 1,\n                                                            isCurrentNotification: false,\n                                                            showDate: selectedCard === index + 1,\n                                                            date: selectedCard === index + 1 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notification.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, notification.id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"No notifications available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 607,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false),\n                                    display === \"meetings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            upcomingMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Upcoming Meetings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: meeting.title,\n                                                            details: meeting.details,\n                                                            date: selectedCard === index ? meeting.startTime : null,\n                                                            isSelected: selectedCard === index,\n                                                            onClick: ()=>handleOpenModal(meeting)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, meeting.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousMeetings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Meetings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 653,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + upcomingMeetings.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + upcomingMeetings.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + upcomingMeetings.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: meeting.title,\n                                                        details: meeting.details,\n                                                        date: selectedCard === index + upcomingMeetings.length ? meeting.startTime : null,\n                                                        isSelected: selectedCard === index + upcomingMeetings.length,\n                                                        onClick: ()=>handleOpenModal(meeting)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, meeting.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                            open: openModal,\n                            onClose: handleCloseModal,\n                            maxWidth: \"sm\",\n                            fullWidth: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                    children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 720,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body1\",\n                                            children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.notification) || (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.details) || \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Start Time: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.createdAt))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 738,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogActions, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                        onClick: handleCloseModal,\n                                        color: \"primary\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 743,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 719,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(Notifications, \"k15HFFbkW2IpBjetlj1ltXnmtLg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme,\n        _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor\n    ];\n});\n_c1 = Notifications;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c2 = (0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(Notifications));\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResponsiveBox\");\n$RefreshReg$(_c1, \"Notifications\");\n$RefreshReg$(_c2, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/notifications.js\n"));

/***/ })

});