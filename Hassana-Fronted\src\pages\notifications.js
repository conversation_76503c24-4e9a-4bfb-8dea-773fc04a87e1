import { gql } from "@apollo/client";
import React, { useState, useEffect } from "react";
import { styled, useTheme } from "@mui/material/styles";
import {
  Box,
  Grid,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import Divider from "@mui/material/Divider";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useQuery } from "@apollo/client";
import NotificationButtons from "@/components/NotificationButtons";
import AnnouncementCard from "../components/AnnouncementCard";
import NotificationCard from "../components/NotificationCard";
import Dashboard from "@/components/Dashboard";
import { useColor } from "@/components/ColorContext";
import { useSelectedColor } from "@/components/HelperFunctions";
import { getEvents } from "@/Data/Events";
import { getNotifications } from "@/Data/Notification";
import { getFormattedDate, monthName } from "@/components/HelperFunctions";
import { getBookings } from "@/Data/Booking";
import withAuth from "@/components/auth/withAuth";
import { useRouter } from "next/router";

const ResponsiveText = styled("div")(({ theme }) => ({
  color: "#1B3745",
  fontSize: "16.02px",
  fontFamily: "Helvetica",
  fontWeight: 400,
  wordWrap: "break-word",
  maxWidth: "100%",
}));

const ResponsiveBox = styled(Box)(
  ({ theme, backgroundColor, disableBorder, isCurrentNotification }) => ({
    width: "100%",
    height: "auto !important",
    background: backgroundColor || "white",
    backgroundColor: !isCurrentNotification
      ? theme.palette.type === "light"
        ? "#F6F5FD"
        : theme.palette.background.secondary
      : theme.palette.background.secondary,
    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
    borderRadius: 10,
    position: "relative",
    "&::before": !disableBorder && {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0.6,
      width: "5px",
      height: "100%",
      background: !isCurrentNotification
        ? theme.palette.type === "light"
          ? "#F6F5FD"
          : theme.palette.background.secondary
        : isCurrentNotification
          ? `linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)`
          : "none",
      borderRadius: "20px 0 0 20px",
    },
  })
);

const Notifications = () => {
  const router = useRouter();
  const { notificationId } = router.query; // Get notificationId from query params
  const { loading: eventLoading, data: eventData } = useQuery(getEvents);
  const { loading: notificationLoading, data: notificationData } = useQuery(getNotifications);
  const { loading: bookingLoading, data: bookingData } = useQuery(getBookings);

  const [selectedCard, setSelectedCard] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
  const [events, setEvents] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [meetings, setMeetings] = useState([]);
  const [activeButton, setActiveButton] = useState("all");
  const [display, setDisplay] = useState("all");

  const isWideScreen = useMediaQuery("(max-width:1150px)");
  const isLargeTablet = useMediaQuery("(max-width:1079px)");
  const isTablet = useMediaQuery("(max-width:1060px)");
  const isSmallTablet = useMediaQuery("(max-width:967px)");
  const isWideMobile = useMediaQuery("(max-width:869px)");
  const isMediumMobile = useMediaQuery("(max-width:823px)");
  const isNarrowMobile = useMediaQuery("(max-width:528px)");
  const isStandardDesktop = useMediaQuery("(max-width:1439px)");

  const theme = useTheme();
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);

  // Handle navigation from query params
  useEffect(() => {
    if (notificationId && notificationData?.notifications) {
      // First try to find in combinedItems (for "all" tab)
      const allIndex = combinedItems.findIndex(
        (item) => item.id === notificationId && item.type === "notification"
      );

      if (allIndex !== -1) {
        setSelectedCard(allIndex);
        setSelectedAnnouncement(combinedItems[allIndex]);
        setDisplay("all");
        setActiveButton("all");
      } else {
        // If not found in combinedItems, try to find in notifications directly
        const notification = notificationData.notifications.find(
          (notif) => notif.id === notificationId
        );

        if (notification) {
          // Switch to notifications tab and select the notification
          setDisplay("notifications");
          setActiveButton("notifications");

          // Find the index in the notifications array
          const notifIndex = notificationData.notifications.findIndex(
            (notif) => notif.id === notificationId
          );

          // Adjust index based on how notifications are displayed (newest first)
          const adjustedIndex = notifIndex === notificationData.notifications.length - 1 ? 0 : notificationData.notifications.length - 1 - notifIndex;

          setSelectedCard(adjustedIndex);
          setSelectedAnnouncement(notification);
        }
      }
    }
  }, [notificationId, notificationData, combinedItems]);

  const handleButtonClick = (buttonType) => {
    setDisplay(buttonType);
    setActiveButton(buttonType);
    setSelectedCard(null);
    setSelectedAnnouncement(null);
  };

  useEffect(() => {
    if (!notificationLoading && notificationData?.notifications) {
      setNotifications(notificationData.notifications);
    }
  }, [notificationLoading, notificationData]);

  useEffect(() => {
    if (eventData && eventData.events) {
      setEvents(eventData.events);
    }
    if (bookingData && bookingData.bookings) {
      setMeetings(bookingData.bookings);
    }
    if (notificationData && notificationData.notifications) {
      setNotifications(notificationData.notifications);
    }
  }, [eventData, bookingData, notificationData]);

  const handleCardClick = (index) => {
    setSelectedCard(index);
    let selectedItem = null;
    if (display === "all") {
      selectedItem = combinedItems[index];
    } else if (display === "events") {
      selectedItem = index < currentEvents.length ? currentEvents[index] : previousEvents[index - currentEvents.length];
    } else if (display === "notifications") {
      selectedItem = index === 0 ? notifications[notifications.length - 1] : notifications.slice(0, -1)[index - 1];
    } else if (display === "meetings") {
      selectedItem = index < upcomingMeetings.length ? upcomingMeetings[index] : previousMeetings[index - upcomingMeetings.length];
    }
    setSelectedAnnouncement(selectedItem);
  };

  const handleOpenModal = (announcement) => {
    setSelectedAnnouncement(announcement);
    setOpenModal(true);
  };

  const handleReset = () => {
    setSelectedCard(null);
    setSelectedAnnouncement(null);
    // Remove notificationId from URL
    router.push("/notifications", undefined, { shallow: true });
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedAnnouncement(null);
  };

  const currentDate = new Date();
  const startOfWeek = currentDate.getDate() - currentDate.getDay();
  const endOfWeek = startOfWeek + 6;

  const currentWeekStart = new Date(currentDate);
  currentWeekStart.setDate(startOfWeek);
  const currentWeekEnd = new Date(currentDate);
  currentWeekEnd.setDate(endOfWeek);

  const currentEvents = events.filter((event) => {
    const eventDate = new Date(event.date);
    return eventDate >= currentWeekStart && eventDate <= currentWeekEnd;
  });

  const previousEvents = events.filter((event) => {
    const eventDate = new Date(event.date);
    return eventDate < currentWeekStart;
  });

  const upcomingMeetings = meetings
    .filter((meeting) => new Date(meeting.startTime) >= currentDate)
    .sort((a, b) => new Date(a.startTime) - new Date(b.startTime));

  const previousMeetings = meetings
    .filter((meeting) => new Date(meeting.startTime) < currentDate)
    .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

  const currentNotifications = notifications
    .filter((notification) => new Date(notification.createdAt) >= currentWeekStart)
    .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

  const previousNotifications = notifications
    .filter((notification) => new Date(notification.createdAt) < currentWeekStart)
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  const combinedItems = [
    ...events.map((item) => ({ ...item, type: "event" })),
    ...meetings.map((item) => ({ ...item, type: "meeting" })),
    ...notifications.map((item) => ({ ...item, type: "notification" })),
  ].sort((a, b) => {
    const dateA = a.createdAt || a.startTime || a.date;
    const dateB = b.createdAt || b.startTime || b.date;
    return new Date(dateB) - new Date(dateA);
  });

  return (
    <>
      <Dashboard>
        <Box sx={{ display: "flex" }}>
          <Box
            component="main"
            sx={{
              background:
                selectedColor === theme.palette.background.primary
                  ? theme.palette.background.secondary
                  : selectedColor,
              paddingLeft: isLargeTablet ? "50px" : "",
              paddingRight: isLargeTablet ? "50px" : "",
              width: isMediumMobile
                ? "100%"
                : isWideMobile
                  ? "45rem"
                  : isSmallTablet
                    ? "48rem"
                    : isTablet
                      ? "54rem"
                      : "60vw",
              margin: "auto",
            }}
          >
            <Box>
              <div style={{ marginLeft: "15px" }}>
                <Typography
                  variant="h5"
                  style={{
                    marginTop: "32px",
                    fontSize: "16.024px",
                    marginLeft: isStandardDesktop ? "16px" : "",
                  }}
                >
                  Latest updates from Hassana
                </Typography>
              </div>
              <div style={{ marginTop: "20px" }}>
                <Typography
                  variant="h1"
                  style={{
                    fontSize: isWideScreen ? "22px" : "27.47px",
                    fontWeight: "700",
                    marginTop: "0",
                    marginLeft: isStandardDesktop ? "30px" : "15px",
                  }}
                >
                  Notifications
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    paddingRight: "25px",
                    marginLeft: isStandardDesktop ? "30px" : "20px",
                    marginTop: "20px",
                  }}
                >
                  <NotificationButtons
                    handleButtonClick={handleButtonClick}
                    activeButton={activeButton}
                    isNarrowMobile={isNarrowMobile}
                    isWideScreen={isWideScreen}
                  />
                </Box>
              </div>
            </Box>
            {selectedCard !== null && (
              <div style={{ display: "flex", alignItems: "center", marginTop: "20px" }}>
                <IconButton
                  onClick={handleReset}
                  style={{
                    marginLeft: isStandardDesktop ? "20px" : "5px",
                    display: "flex",
                    alignItems: "center",
                    fontSize: "16px",
                    color: "#888",
                    gap: 5,
                  }}
                >
                  <img src="/icons/arrow-left.svg" alt="arrow" height={16} width parametern={16} />
                  {selectedAnnouncement?.title || "Notification Details"}
                </IconButton>
                <div
                  style={{
                    display: "flex",
                    gap: "8px",
                    alignItems: "center",
                    fontSize: "16px",
                  }}
                >
                  <Divider
                    sx={{
                      width: "15px",
                      backgroundColor: "#A7A7A7",
                    }}
                  />
                  {selectedAnnouncement?.createdAt && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: "#888",
                        fontSize: "12px",
                        marginTop: "2px",
                      }}
                    >
                      {getFormattedDate(selectedAnnouncement.createdAt)}
                    </Typography>
                  )}
                  {selectedAnnouncement?.date && !selectedAnnouncement?.createdAt && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: "#888",
                        fontSize: "12px",
                        marginBottom: "2px",
                      }}
                    >
                      {getFormattedDate(selectedAnnouncement.date)} —{" "}
                      {monthName(new Date(selectedAnnouncement.date))}
                    </Typography>
                  )}
                  {selectedAnnouncement?.startTime && !selectedAnnouncement?.createdAt && !selectedAnnouncement?.date && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: "#888",
                        fontSize: "12px",
                        marginBottom: "4px",
                      }}
                    >
                      {getFormattedDate(selectedAnnouncement.startTime)} —{" "}
                      {monthName(new Date(selectedAnnouncement.startTime))}
                    </Typography>
                  )}
                </div>
              </div>
            )}
            <Grid
              container
              spacing={3}
              sx={{
                padding: "3%",
                height: "70vh",
                overflow: "auto",
                marginTop: "10px",
              }}
            >
              <Grid
                item
                xs={12}
                md={12}
                lg={12}
                order={{ xs: 2, sm: 2, md: 2, lg: 1, xl: 1 }}
              >
                {display === "all" && (
                  <>
                    {notificationLoading || eventLoading || bookingLoading ? (
                      <Typography>Loading...</Typography>
                    ) : (
                      <>
                        {combinedItems.map((item, index) => {
                          const isEvent = item.type === "event";
                          const isMeeting = item.type === "meeting";
                          const isNotification = item.type === "notification";
                          return (
                            <ResponsiveBox
                              key={item.id || index}
                              isCurrentNotification={isNotification}
                              sx={{
                                padding: "30.41px 16.91px 29.04px 16.91px",
                                marginTop: "20px",
                                display:
                                  selectedCard !== null && selectedCard !== index
                                    ? "none"
                                    : "block",
                                height:
                                  selectedCard !== null && selectedCard === index
                                    ? "auto"
                                    : "170px",
                                background: isNotification || isMeeting ? theme.palette.background.secondary : undefined,
                              }}
                              onClick={() => handleCardClick(index)}
                            >
                              {isEvent && (
                                <AnnouncementCard
                                  title={item.title}
                                  details={item.details}
                                  isSelected={selectedCard === index}
                                  date={selectedCard === index ? item.date : null}
                                />
                              )}
                              {isMeeting && (
                                <AnnouncementCard
                                  title={item.title}
                                  details={item.details}
                                  isSelected={selectedCard === index}
                                  date={selectedCard === index ? item.startTime : null}
                                />
                              )}
                              {isNotification && (
                                <NotificationCard
                                  notification={item.notification}
                                  isSelected={selectedCard === index}
                                  isCurrentNotification={true}
                                  showDate={selectedCard === index}
                                  date={
                                    selectedCard === index
                                      ? `${getFormattedDate(item.createdAt)}`
                                      : null
                                  }
                                />
                              )}
                            </ResponsiveBox>
                          );
                        })}
                      </>
                    )}
                  </>
                )}
                {display === "events" && (
                  <>
                    {currentEvents.map((event, index) => (
                      <ResponsiveBox
                        key={event.id || index}
                        isCurrentNotification={true}
                        sx={{
                          padding: "30.41px 16.91px 29.04px 16.91px",
                          marginTop: "10px",
                          display:
                            selectedCard !== null && selectedCard !== index
                              ? "none"
                              : "block",
                          height:
                            selectedCard !== null && selectedCard === index
                              ? "auto"
                              : "170px",
                        }}
                        onClick={() => handleCardClick(index)}
                      >
                        <AnnouncementCard
                          isCurrentNotification={true}
                          title={event.title}
                          details={event.details}
                          date={selectedCard === index ? event.date : null}
                          isSelected={selectedCard === index}
                        />
                      </ResponsiveBox>
                    ))}
                    {previousEvents.length > 0 && (
                      <Box
                        display="flex"
                        alignItems="center"
                        marginBottom="24px"
                        marginTop="24px"
                      >
                        <Box
                          borderBottom="2px solid #E2E0F1"
                          width="100%"
                        ></Box>
                        <Typography
                          variant="body2"
                          align="center"
                          sx={{
                            mx: 0,
                            color: "#949494",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                          }}
                        >
                          Previous Events
                        </Typography>
                        <Box borderTop="2px solid #E2E0F1" width="100%"></Box>
                      </Box>
                    )}
                    {previousEvents.map((event, index) => (
                      <ResponsiveBox
                        key={event.id || index}
                        sx={{
                          background: theme.palette.background.secondary,
                          padding: "30.41px 16.91px 29.04px 16.91px",
                          marginTop: "20px",
                          display:
                            selectedCard !== null &&
                              selectedCard !== index + currentEvents.length
                              ? "none"
                              : "block",
                          height:
                            selectedCard !== null &&
                              selectedCard === index + currentEvents.length
                              ? "auto"
                              : "170px",
                        }}
                        onClick={() =>
                          handleCardClick(index + currentEvents.length)
                        }
                      >
                        <AnnouncementCard
                          title={event.title}
                          details={event.details}
                          isSelected={
                            selectedCard === index + currentEvents.length
                          }
                          date={selectedCard === index + currentEvents.length ? event.date : null}
                        />
                      </ResponsiveBox>
                    ))}
                  </>
                )}
                {display === "notifications" && (
                  <>
                    {notificationLoading ? (
                      <Typography>Loading...</Typography>
                    ) : notificationData?.notifications?.length > 0 ? (
                      <>
                        <ResponsiveBox
                          isCurrentNotification={true}
                          sx={{
                            padding: "16px",
                            marginTop: "10px",
                            display: selectedCard !== null && selectedCard !== 0 ? "none" : "block",
                          }}
                          onClick={() => handleCardClick(0)}
                        >
                          <NotificationCard
                            notification={notificationData.notifications[notificationData.notifications.length - 1].notification}
                            isSelected={selectedCard === 0}
                            isCurrentNotification={true}
                            showDate={selectedCard === 0}
                            date={
                              selectedCard === 0
                                ? `${getFormattedDate(notificationData.notifications[notificationData.notifications.length - 1].createdAt)}`
                                : null
                            }
                          />
                        </ResponsiveBox>
                        {notificationData.notifications.length > 1 && (
                          <Box display="flex" alignItems="center" marginBottom="24px" marginTop="24px">
                            <Box borderBottom="2px solid #E2E0F1" width="100%"></Box>
                            <Typography variant="body2" align="center" sx={{ mx: 0, color: "#949494", fontSize: "12px", whiteSpace: "nowrap" }}>
                              Previous Notifications
                            </Typography>
                            <Box borderTop="2px solid #E2E0F1" width="100%"></Box>
                          </Box>
                        )}
                        {notificationData.notifications.slice(0, -1).map((notification, index) => (
                          <ResponsiveBox
                            key={notification.id || index}
                            sx={{
                              background: theme.palette.background.secondary,
                              padding: "30.41px 16.91px 29.04px 16.91px",
                              marginTop: "20px",
                              display: selectedCard !== null && selectedCard !== index + 1 ? "none" : "block",
                              height: selectedCard === index + 1 ? "auto" : "170px",
                            }}
                            onClick={() => handleCardClick(index + 1)}
                          >
                            <NotificationCard
                              notification={notification.notification}
                              isSelected={selectedCard === index + 1}
                              isCurrentNotification={false}
                              showDate={selectedCard === index + 1}
                              date={
                                selectedCard === index + 1
                                  ? `${getFormattedDate(notification.createdAt)}`
                                  : null
                              }
                            />
                          </ResponsiveBox>
                        ))}
                      </>
                    ) : (
                      <Typography>No notifications available</Typography>
                    )}
                  </>
                )}
                {display === "meetings" && (
                  <>
                    {upcomingMeetings.map((meeting, index) => (
                      <ResponsiveBox
                        key={meeting.id || index}
                        sx={{
                          background: theme.palette.background.secondary,
                          padding: "30.41px 16.91px 29.04px 16.91px",
                          marginTop: "20px",
                          display:
                            selectedCard !== null && selectedCard !== index
                              ? "none"
                              : "block",
                          height:
                            selectedCard !== null && selectedCard === index
                              ? "auto"
                              : "170px",
                        }}
                        onClick={() => handleCardClick(index)}
                      >
                        <Typography
                          variant="body2"
                          align="center"
                          sx={{
                            mx: 0,
                            color: "#949494",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                          }}
                        >
                          Upcoming Meetings
                        </Typography>
                        <AnnouncementCard
                          title={meeting.title}
                          details={meeting.details}
                          date={selectedCard === index ? meeting.startTime : null}
                          isSelected={selectedCard === index}
                          onClick={() => handleOpenModal(meeting)}
                        />
                      </ResponsiveBox>
                    ))}
                    {previousMeetings.length > 0 && (
                      <Box
                        display="flex"
                        alignItems="center"
                        marginBottom="24px"
                        marginTop="24px"
                      >
                        <Box
                          borderBottom="2px solid #E2E0F1"
                          width="100%"
                        ></Box>
                        <Typography
                          variant="body2"
                          align="center"
                          sx={{
                            mx: 0,
                            color: "#949494",
                            fontSize: "12px",
                            whiteSpace: "nowrap",
                          }}
                        >
                          Previous Meetings
                        </Typography>
                        <Box borderTop="2px solid #E2E0F1" width="100%"></Box>
                      </Box>
                    )}
                    {previousMeetings.map((meeting, index) => (
                      <ResponsiveBox
                        key={meeting.id || index}
                        sx={{
                          background: theme.palette.background.secondary,
                          padding: "30.41px 16.91px 29.04px 16.91px",
                          marginTop: "20px",
                          display:
                            selectedCard !== null &&
                              selectedCard !== index + upcomingMeetings.length
                              ? "none"
                              : "block",
                          height:
                            selectedCard !== null &&
                              selectedCard === index + upcomingMeetings.length
                              ? "auto"
                              : "170px",
                        }}
                        onClick={() =>
                          handleCardClick(index + upcomingMeetings.length)
                        }
                      >
                        <AnnouncementCard
                          title={meeting.title}
                          details={meeting.details}
                          date={
                            selectedCard === index + upcomingMeetings.length
                              ? meeting.startTime
                              : null
                          }
                          isSelected={
                            selectedCard === index + upcomingMeetings.length
                          }
                          onClick={() => handleOpenModal(meeting)}
                        />
                      </ResponsiveBox>
                    ))}
                  </>
                )}
              </Grid>
            </Grid>
            <Dialog open={openModal} onClose={handleCloseModal} maxWidth="sm" fullWidth>
              <DialogTitle>
                {selectedAnnouncement?.title || "Notification Details"}
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1">
                  {selectedAnnouncement?.notification || selectedAnnouncement?.details || "No details available"}
                </Typography>
                {selectedAnnouncement?.date && (
                  <Typography variant="body2" color="textSecondary">
                    Date: {getFormattedDate(selectedAnnouncement.date)} {monthName(new Date(selectedAnnouncement.date))}
                  </Typography>
                )}
                {selectedAnnouncement?.startTime && (
                  <Typography variant="body2" color="textSecondary">
                    Start Time: {getFormattedDate(selectedAnnouncement.startTime)} {monthName(new Date(selectedAnnouncement.startTime))}
                  </Typography>
                )}
                {selectedAnnouncement?.createdAt && !selectedAnnouncement?.date && !selectedAnnouncement?.startTime && (
                  <Typography variant="body2" color="textSecondary">
                    Date: {getFormattedDate(selectedAnnouncement.createdAt)} {monthName(new Date(selectedAnnouncement.createdAt))}
                  </Typography>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseModal} color="primary">
                  Close
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Box>
      </Dashboard>
    </>
  );
};

export default withAuth(Notifications);