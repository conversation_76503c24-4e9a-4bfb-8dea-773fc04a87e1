{"c": ["webpack"], "r": ["pages/notification-details/[id]"], "m": ["./node_modules/@mui/icons-material/ArrowBack.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CDELL%5CDesktop%5Chassana%5CHassana-Fronted%5Csrc%5Cpages%5Cnotification-details%5C%5Bid%5D.js&page=%2Fnotification-details%2F%5Bid%5D!", "./src/pages/notification-details/[id].js", "__barrel_optimize__?names=<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,CircularProgress,Divider,IconButton,Paper,Typography!=!./node_modules/@mui/material/index.js"]}