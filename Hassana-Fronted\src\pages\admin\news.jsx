import Dashboard from "@/components/Dashboard";
import { Cancel, PhotoCamera } from "@mui/icons-material";
import {
  Box,
  Button,
  IconButton,
  Input,
  InputAdornment,
  MenuItem,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { useEffect, useState } from "react";
import {
  createNews,
  deleteNews,
  getAllNews,
  updateNews,
} from "../../Data/News";
import DataTable from "./component/DataTable";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { getDateFromPicker } from "@/components/HelperFunctions";
import SnackbarComponent from "@/components/SnackBar";
import withAdminAuth from "@/components/auth/withAdminAuth";
import Image from "next/image";

const columns = [
  { id: "title", label: "Title", minWidth: 50 },
  { id: "summary", label: "Excerpt/Summary", minWidth: 300, align: "left" },
  { id: "author", label: "Author", minWidth: 50, align: "center" },
  { id: "visibility", label: "Visibility", minWidth: 100, align: "center" },
  { id: "category", label: "Category", minWidth: 50, align: "center" },
  { id: "status", label: "Status", minWidth: 50, align: "center" },
];

const AddNews = ({
  opt,
  data,
  news,
  setNews,
  setOpen,
  setSnackbarOpen,
  setSnackbarSeverity,
  setSnackbarMessage,
}) => {
  const theme = useTheme();
  const authUser = { id: "system" };

  const [featuredImage, setFeaturedImage] = useState(
    data && opt === "update" ? data.featuredImage || "" : ""
  );
  const [id, setId] = useState(data && opt === "update" ? data.id : "");
  const [title, setTitle] = useState(data && opt === "update" ? data.title : "");
  const [url, setUrl] = useState(data && opt === "update" ? data.url || "" : "");
  const [author, setAuthor] = useState(
    data && opt === "update" ? data.author || "" : ""
  );
  const [category, setCategory] = useState(
    data && opt === "update" ? data.category || "internal" : "internal"
  );
  const [source, setSource] = useState(
    data && opt === "update" ? data.source || "" : ""
  );
  const [visibility, setVisibility] = useState(
    data && opt === "update" ? dayjs(data.visibility) : dayjs()
  );
  const [status, setStatus] = useState(
    data && opt === "update" ? data.status || "true" : "true"
  );
  const [summary, setSummary] = useState(
    data && opt === "update" ? data.summary || "" : ""
  );
  const [image, setImage] = useState(null);
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [errors, setErrors] = useState({});

  const { getNewsImageUrl } = require('@/utils/imageUtils');

  useEffect(() => {
    if (data?.featuredImage) {
      const fullImageUrl = getNewsImageUrl(data.featuredImage);
      setImage(fullImageUrl);
      setFeaturedImage(fullImageUrl);
    }
  }, [data?.featuredImage]);

  const handleImageChange = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFeaturedImage(file);
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
      setIsImageChanged(true);
    }
  };

  const handleImageRemove = () => {
    setImage(null);
    setFeaturedImage("");
    setIsImageChanged(true);
  };

  const validateForm = () => {
    const tempErrors = {};
    if (!title.trim()) tempErrors.title = "Title is required.";
    if (!category) tempErrors.category = "Category is required.";
    if (!summary.trim()) tempErrors.summary = "Summary is required.";
    if (!visibility) tempErrors.visibility = "Visibility is required.";
    if (!status) tempErrors.status = "Status is required.";
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const submitHandler = async () => {
    if (!validateForm()) {
      setSnackbarMessage("Please fill all required fields");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      throw new Error("Validation failed");
    }

    const fieldsData = {
      id,
      title,
      url,
      category,
      source,
      visibility: getDateFromPicker(visibility),
      status,
      summary,
      author,
      featuredImage:
        featuredImage instanceof File
          ? featuredImage
          : featuredImage || undefined,
      created_on: getDateFromPicker(dayjs()),
      created_by: authUser.id,
      updated_on: getDateFromPicker(dayjs()),
      updated_by: authUser.id,
    };

    try {
      if (opt !== "update") {
        const response = await createNews(fieldsData);
        if (response?.code !== 200) {
          throw new Error(response?.message || "Failed to add news");
        }
        const newNewsItem = {
          ...response.data,
          visibility: response.data.visibility || fieldsData.visibility,
          createdAt: response.data.createdAt || new Date().toISOString(),
          updatedAt: response.data.updatedAt || new Date().toISOString(),
        };
        setNews((prevNews) => {
          const updatedNews = Array.isArray(prevNews)
            ? [...prevNews, newNewsItem]
            : [newNewsItem];
          return updatedNews.sort(
            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
          );
        });
        setSnackbarMessage("News added successfully");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
        setOpen(false);
        setTitle("");
        setUrl("");
        setAuthor("");
        setCategory("internal");
        setSource("");
        setVisibility(dayjs());
        setStatus("true");
        setSummary("");
        setFeaturedImage("");
        setImage(null);
        return newNewsItem;
      } else {
        const response = await updateNews(fieldsData, isImageChanged);
        if (response?.code !== 200) {
          throw new Error(response?.message || "Failed to update news");
        }
        setNews((prevNews) => {
          const updatedNews = Array.isArray(prevNews)
            ? prevNews.map((news) =>
                news.id === fieldsData.id ? { ...news, ...response.data } : news
              )
            : [response.data];
          return updatedNews.sort(
            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
          );
        });
        setSnackbarMessage("News updated successfully");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
        setOpen(false);
        setTitle("");
        setUrl("");
        setAuthor("");
        setCategory("internal");
        setSource("");
        setVisibility(dayjs());
        setStatus("true");
        setSummary("");
        setFeaturedImage("");
        setImage(null);
        return response.data;
      }
    } catch (error) {
      console.error("Error in submitHandler:", error.message);
      setSnackbarMessage(error.message || "Operation failed");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      throw error;
    }
  };

  return (
    <>
      <Typography variant="body2">Add News</Typography>
      <TextField
        error={!!errors.title}
        helperText={errors.title}
        margin="normal"
        size="small"
        id="Title"
        fullWidth
        label="Title"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
      />
      <TextField
        margin="normal"
        size="small"
        id="Slug/URL"
        fullWidth
        label="Slug/URL"
        value={url}
        onChange={(e) => setUrl(e.target.value)}
      />
      <TextField
        error={!!errors.summary}
        helperText={errors.summary}
        margin="normal"
        size="small"
        id="Excerpt/Summary"
        fullWidth
        label="Excerpt/Summary"
        multiline
        rows={6}
        value={summary}
        onChange={(e) => setSummary(e.target.value)}
      />
      <TextField
        margin="normal"
        size="small"
        id="Author"
        fullWidth
        label="Author"
        value={author}
        onChange={(e) => setAuthor(e.target.value)}
      />
      <TextField
        error={!!errors.category}
        helperText={errors.category}
        id="Category/Topic"
        select
        label="Category/Topic"
        value={category}
        onChange={(e) => setCategory(e.target.value)}
        sx={{ width: "100%", marginY: "10px" }}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: { backgroundColor: theme.palette.background.primary },
            },
          },
        }}
      >
        <MenuItem value="internal">Internal</MenuItem>
        <MenuItem value="external">External</MenuItem>
      </TextField>
      <TextField
        margin="normal"
        size="small"
        id="Source"
        fullWidth
        label="Source"
        value={source}
        onChange={(e) => setSource(e.target.value)}
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DateTimePicker
          label="Date"
          error={!!errors.visibility}
          helperText={errors.visibility}
          value={visibility}
          onChange={(newValue) => setVisibility(newValue || dayjs())}
          sx={{ width: "100%", marginY: "20px" }}
        />
      </LocalizationProvider>
      <TextField
        id="Status"
        select
        error={!!errors.status}
        helperText={errors.status}
        label="Status"
        value={status}
        onChange={(e) => setStatus(e.target.value)}
        sx={{ width: "100%", marginY: "10px" }}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: { backgroundColor: theme.palette.background.primary },
            },
          },
        }}
      >
        <MenuItem value="true">Active</MenuItem>
        <MenuItem value="false">Inactive</MenuItem>
      </TextField>
      <Box>
        <Input
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          disableUnderline
          endAdornment={
            <InputAdornment position="end">
              <IconButton component="label" htmlFor="imageInput">
                <PhotoCamera />
              </IconButton>
            </InputAdornment>
          }
          inputProps={{ id: "imageInput", style: { display: "none" } }}
        />
        {image && (
          <div>
            <img
              src={image}
              alt="Selected"
              style={{ maxWidth: "100%", maxHeight: "200px" }}
            />
            <IconButton onClick={handleImageRemove}>
              <Cancel />
            </IconButton>
          </div>
        )}
      </Box>
      <Button
        onClick={submitHandler}
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt !== "update" ? "Submit" : "Update"}
      </Button>
    </>
  );
};

const News = () => {
  const [index, setIndex] = useState(null);
  const [openUpdateBox, setOpenUpdateBox] = useState(false);
  const [openAddBox, setOpenAddBox] = useState(false);
  const [data, setData] = useState(null);
  const [news, setNews] = useState([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") return;
    setSnackbarOpen(false);
  };

  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        const data = await getAllNews();
        const sortedNews = Array.isArray(data)
          ? data.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          : [];
        setNews(sortedNews);
        setError(null);
      } catch (err) {
        console.error("Error fetching news:", err);
        setError("Failed to fetch news data");
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, []);

  const deleteHandler = async (deletedId) => {
    try {
      const response = await deleteNews(deletedId);
      if (response?.code === 200) {
        setNews((prevNews) =>
          Array.isArray(prevNews)
            ? prevNews.filter((news) => news.id !== deletedId)
            : []
        );
        setSnackbarMessage("Deleted successfully");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      } else {
        setSnackbarMessage("Deletion failed");
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    } catch (error) {
      setSnackbarMessage("Deletion failed");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  return (
    <Dashboard>
      <Box sx={{ margin: "25px" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="h5" sx={{ marginY: "10px" }}>
            News Panel
          </Typography>
          <BasicModal
            title="Add News"
            comp={
              <AddNews
                opt="add"
                data={data}
                setData={setData}
                news={news}
                setNews={setNews}
                setOpen={setOpenAddBox}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
              />
            }
            btn={true}
            open={openAddBox}
            setOpen={setOpenAddBox}
          />
        </Box>
        <UpdateModal
          title="Update News"
          comp={
            <AddNews
              opt="update"
              data={data}
              setData={setData}
              news={news}
              setNews={setNews}
              setOpen={setOpenUpdateBox}
              setSnackbarMessage={setSnackbarMessage}
              setSnackbarSeverity={setSnackbarSeverity}
              setSnackbarOpen={setSnackbarOpen}
            />
          }
          btn={false}
          openUpdate={openUpdateBox}
          setOpenUpdate={setOpenUpdateBox}
        />
        <Box sx={{ maxWidth: "100%", overflow: "auto" }}>
          {loading ? (
            <Typography variant="h5" sx={{ marginY: "10px", textAlign: "center" }}>
              Loading...
            </Typography>
          ) : error ? (
            <Typography variant="h5" sx={{ marginY: "10px", textAlign: "center" }}>
              {error}
            </Typography>
          ) : news.length > 0 ? (
            <DataTable
              columns={columns}
              rows={news}
              setIndex={setIndex}
              setOpen={setOpenUpdateBox}
              setData={setData}
              deleteHandler={deleteHandler}
              action={true}
              updateKey="title"
            />
          ) : (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Typography variant="h5" sx={{ marginY: "10px" }}>
                No data found
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
      <SnackbarComponent
        open={snackbarOpen}
        handleClose={handleCloseSnackbar}
        severity={snackbarSeverity}
        message={snackbarMessage}
      />
    </Dashboard>
  );
};

export default withAdminAuth(News);