import Dashboard from "@/components/Dashboard";
import { Box } from "@mui/system";
import withAuth from "@/components/auth/withAuth";
import React, { useState, useRef, useEffect } from "react";
import {
  AppBar,
  Toolbar,
  Typography,
  Paper,
  IconButton,
  Grid,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Breadcrumbs,
  Link,
  Alert,
} from "@mui/material";
import {
  FolderOpenOutlined,
  MoreHoriz as MoreHorizIcon,
  Folder as FolderIcon,
  ArrowBack as ArrowBackIcon,
  CreateNewFolder as CreateNewFolderIcon,
  FolderOff,
  FolderCopyRounded,
  FolderZip,
  FolderOpen,
  FileOpen,
  FileUpload,
  FileCopyRounded,
} from "@mui/icons-material";
import { useSession } from "next-auth/react";
import { useSelectedColor } from "@/components/HelperFunctions";
import { useColor } from "@/components/ColorContext";
import { useTheme } from "@mui/material/styles";
import { baseUrl } from "@/Data/ApolloClient";
import { useDropzone } from "react-dropzone";
import Image from "next/image";

let base_url = baseUrl;
const endpoints = {
  createFolder: "file-system/folder",
  uploadFile: "file-system/upload",
  listContent: "file-system/list",
  downloadFile: "file-system/file",
  deleteItem: "file-system/delete",
  renameFile: "file-system/rename-file",
  renameFolder: "file-system/rename-folder",
};

// Helper function to get icon based on file type
const getFileIcon = (fileType) => {
  const type = fileType?.toLowerCase();
  switch (type) {
    case "pdf":
      return "/pdf.svg";
    case "doc":
    case "docx":
      return "/word.svg";
    case "zip":
      return "/zip.svg";
    case "ppt":
    case "pptx":
      return "/ppt.svg";
    case "png":
    case "jpg":
    case "jpeg":
      return "/file.svg";
    case "txt":
      return "/file.svg";
    default:
      return "/file.svg";
  }
};

const ContentLibrary = () => {
  const [files, setFiles] = useState([]);
  const [folders, setFolders] = useState([]);
  const [currentPath, setCurrentPath] = useState([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [showZipNotification, setShowZipNotification] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const fileInputRef = useRef(null);
  const { data: session, status } = useSession();
  const isAdmin = session?.user.role === "admin" || session?.user.role === "ADMIN";
  const [showUploadArea, setShowUploadArea] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const [selectedItemType, setSelectedItemType] = useState(null);
  const dropzoneRef = useRef(null);
  const [alertMessage, setAlertMessage] = useState(null);
  const [alertSeverity, setAlertSeverity] = useState("success");

  useEffect(() => {
    console.log("isAdmin:", isAdmin, "role:", session?.user.role);
  }, [isAdmin, session]);

  // Function to show alerts
  const showAlert = (message, severity = "success") => {
    setAlertMessage(message);
    setAlertSeverity(severity);
  };

  // Fetch current folder content from backend
  const fetchContent = async () => {
    try {
      const response = await fetch(`${base_url}/${endpoints.listContent}?path=${currentPath.join("/")}`, {
        headers: {
          Authorization: `Bearer ${session?.accessToken}`,
        },
      });
      const data = await response.json();
      if (response.ok) {
        const newFolders = data
          .filter((item) => item.type === "folder")
          .map((folder) => ({
            name: folder.name,
            subfolders: folder.subfolders || [],
            files: folder.files || [],
            createdAt: folder.createdAt,
          }));
        const newFiles = data
          .filter((item) => item.type === "file")
          .map((file) => ({
            file: { name: file.name, type: file.name.split(".").pop(), size: file.size },
            preview: file.name.match(/\.(jpg|jpeg|png|gif)$/i) ? `${base_url}/${endpoints.downloadFile}?path=${file.path}&token=${session?.accessToken}` : null,
          }));
        setFolders(newFolders);
        setFiles(newFiles);
      } else {
        console.error("Failed to fetch content:", data);
        showAlert(`Failed to load content: ${data.message || "Unknown error"}`, "error");
      }
    } catch (error) {
      console.error("Error fetching content:", error);
      showAlert(`Error fetching content: ${error.message}`, "error");
    }
  };

  useEffect(() => {
    if (session) fetchContent();
  }, [currentPath, session]);

  const getCurrentFolderContent = () => {
    let currentFolders = folders;
    let currentFiles = files;
    for (const pathPart of currentPath) {
      const folder = currentFolders.find((f) => f.name === pathPart);
      if (folder) {
        currentFolders = folder.subfolders || [];
        currentFiles = folder.files || [];
      }
    }
    return { folders: currentFolders, files: currentFiles };
  };

  const addItemToPath = (item, itemType, path = currentPath) => {
    if (path.length === 0) {
      if (itemType === "folder") {
        setFolders((prev) => [...prev, item]);
      } else {
        setFiles((prev) => [...prev, item]);
      }
      return;
    }
    const updateNested = (folderArray, pathIndex = 0) => {
      return folderArray.map((folder) => {
        if (folder.name === path[pathIndex]) {
          if (pathIndex === path.length - 1) {
            if (itemType === "folder") {
              return { ...folder, subfolders: [...(folder.subfolders || []), item] };
            } else {
              return { ...folder, files: [...(folder.files || []), item] };
            }
          } else {
            return { ...folder, subfolders: updateNested(folder.subfolders || [], pathIndex + 1) };
          }
        }
        return folder;
      });
    };
    setFolders((prev) => updateNested(prev));
  };

  const updateFilesInCurrentPath = (updatedFiles) => {
    if (currentPath.length === 0) {
      setFiles([...updatedFiles]);
      return;
    }
    const updateNestedFiles = (folderArray, pathIndex = 0) => {
      return folderArray.map((folder) => {
        if (folder.name === currentPath[pathIndex]) {
          if (pathIndex === currentPath.length - 1) {
            return { ...folder, files: [...updatedFiles] };
          } else {
            return { ...folder, subfolders: updateNestedFiles(folder.subfolders || [], pathIndex + 1) };
          }
        }
        return folder;
      });
    };
    setFolders((prev) => updateNestedFiles(prev));
  };

  const handleMenuOpen = (event, index, itemType) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedFileIndex(index);
    setSelectedItemType(itemType);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDelete = async () => {
    const { folders: currentFolders, files: currentFiles } = getCurrentFolderContent();
    if (selectedFileIndex === null) {
      handleMenuClose();
      return;
    }
    const itemType = selectedItemType;
    const itemName = itemType === "folder" ? currentFolders[selectedFileIndex].name : currentFiles[selectedFileIndex].file.name;
    const itemPath = currentPath.length > 0 ? `${currentPath.join("/")}/${itemName}` : itemName;
    try {
      const response = await fetch(`${base_url}/${endpoints.deleteItem}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session?.accessToken}`,
        },
        body: JSON.stringify({ path: itemPath }),
      });
      const data = await response.json();
      if (response.ok) {
        if (itemType === "folder") {
          console.log("Deleted folder:", itemName);
          setFolders((prev) => {
            const updated = [...prev];
            updated.splice(selectedFileIndex, 1);
            return updated;
          });
        } else {
          console.log("Deleted file:", itemName);
          const updatedFiles = [...currentFiles];
          const deletedFile = updatedFiles.splice(selectedFileIndex, 1)[0];
          if (deletedFile.preview) {
            URL.revokeObjectURL(deletedFile.preview);
          }
          updateFilesInCurrentPath(updatedFiles);
        }
        await fetchContent();
        showAlert(`Successfully deleted ${itemType}: ${itemName}`, "success");
      } else {
        console.error("Failed to delete item:", data);
        showAlert(`Failed to delete item: ${data.message || "Unknown error"}`, "error");
      }
    } catch (error) {
      console.error("Error deleting item:", error);
      showAlert(`Error deleting item: ${error.message}`, "error");
    }
    handleMenuClose();
  };

  const handleCreateFolder = async () => {
    if (newFolderName.trim() && isAdmin) {
      try {
        const response = await fetch(`${base_url}/${endpoints.createFolder}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.accessToken}`,
          },
          body: JSON.stringify({
            path: currentPath.join("/"),
            folderName: newFolderName.trim(),
          }),
        });
        const data = await response.json();
        if (response.ok) {
          const newFolder = {
            name: newFolderName.trim(),
            subfolders: [],
            files: [],
            createdAt: new Date().toISOString(),
          };
          addItemToPath(newFolder, "folder");
          setNewFolderName("");
          setShowCreateDialog(false);
          await fetchContent();
          showAlert(`Folder "${newFolderName.trim()}" created successfully!`, "success");
        } else {
          console.error("Failed to create folder:", data);
          showAlert(`Failed to create folder: ${data.message || "Unknown error"}`, "error");
        }
      } catch (error) {
        console.error("Error creating folder:", error);
        showAlert(`Error creating folder: ${error.message}`, "error");
      }
    }
  };

  const handleRename = async () => {
    const { folders: currentFolders, files: currentFiles } = getCurrentFolderContent();
    if (selectedFileIndex === null || selectedItemType === null) {
      console.warn("Please select a valid file or folder to rename.");
      showAlert("Please select a valid file or folder to rename.", "error");
      handleMenuClose();
      return;
    }
    const itemType = selectedItemType;
    const oldName = itemType === "folder" ? currentFolders[selectedFileIndex]?.name : currentFiles[selectedFileIndex]?.file?.name;
    if (!oldName) {
      console.warn("Selected item does not exist.");
      showAlert("Selected item does not exist.", "error");
      handleMenuClose();
      return;
    }
    setNewItemName(oldName);
    setShowRenameDialog(true);
    setAnchorEl(null);
  };

  const handleRenameSubmit = async () => {
    const { folders: currentFolders, files: currentFiles } = getCurrentFolderContent();
    if (selectedFileIndex === null || selectedItemType === null || !newItemName.trim()) {
      console.error("Invalid state for rename: ", { selectedFileIndex, selectedItemType, newItemName });
      showAlert("Please select a valid item and enter a new name.", "error");
      setShowRenameDialog(false);
      setSelectedFileIndex(null);
      setSelectedItemType(null);
      setNewItemName("");
      return;
    }
    if (!isAdmin) {
      showAlert("Only admins can rename items.", "error");
      setShowRenameDialog(false);
      setSelectedFileIndex(null);
      setSelectedItemType(null);
      setNewItemName("");
      return;
    }
    const itemType = selectedItemType;
    const oldName = itemType === "folder" ? currentFolders[selectedFileIndex]?.name : currentFiles[selectedFileIndex]?.file?.name;
    if (!oldName) {
      console.error("Selected item no longer exists:", { selectedFileIndex, itemType });
      showAlert("The selected item could not be found.", "error");
      setShowRenameDialog(false);
      setSelectedFileIndex(null);
      setSelectedItemType(null);
      setNewItemName("");
      return;
    }
    const itemPath = currentPath.length > 0 ? `${currentPath.join("/").trim()}/${oldName.trim()}` : oldName.trim();
    try {
      const endpoint = itemType === "folder" ? endpoints.renameFolder : endpoints.renameFile;
      const requestBody = {
        path: itemPath,
        newName: newItemName.trim(),
      };
      const response = await fetch(`${base_url}/${endpoint}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session?.accessToken}`,
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();
      if (response.ok) {
        console.log("Renamed", itemType, "from", oldName, "to", newItemName.trim());
        await fetchContent();
        showAlert(`Successfully renamed ${itemType} to "${newItemName.trim()}"`, "success");
      } else {
        console.error("Failed to rename item:", data);
        showAlert(`Failed to rename item: ${data.message || "Unknown error"}`, "error");
      }
    } catch (error) {
      console.error("Error renaming item:", error);
      showAlert(`Error renaming item: ${error.message}`, "error");
    }
    setShowRenameDialog(false);
    setSelectedFileIndex(null);
    setSelectedItemType(null);
    setNewItemName("");
  };

  const handleFolderClick = (folderName) => {
    setCurrentPath((prev) => [...prev, folderName]);
  };

  const navigateBack = () => {
    setCurrentPath((prev) => prev.slice(0, -1));
  };

  const navigateToBreadcrumb = (index) => {
    setCurrentPath((prev) => prev.slice(0, index + 1));
  };

  const handleUpload = async (input) => {
    let acceptedFiles;
    let isFromFileInput = false;

    if (input?.target?.files) {
      acceptedFiles = Array.from(input.target.files);
      isFromFileInput = true;
    } else if (Array.isArray(input)) {
      acceptedFiles = input;
    } else {
      console.warn("Invalid upload input");
      showAlert("Invalid upload input", "error");
      return;
    }

    // Reset file input immediately to close dialog
    if (isFromFileInput && fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (!isAdmin) {
      showAlert("Only admins can upload files.", "error");
      return;
    }

    try {
      const uploadedFiles = [];
      for (const file of acceptedFiles) {
        if (file.size > 50 * 1024 * 1024) {
          showAlert(`File "${file.name}" is too large. Please upload files smaller than 50 MB.`, "error");
          continue;
        }

        const formData = new FormData();
        formData.append("file", file);

        const response = await fetch(`${base_url}/${endpoints.uploadFile}?path=${currentPath.join("/")}`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session?.accessToken}`,
          },
          body: formData,
        });
        const data = await response.json();

        if (response.ok) {
          console.log(`Uploaded file:`, file.name, "Response:", data);
          const fileObj = {
            file: {
              name: file.name,
              type: file.name.split(".").pop(),
              size: file.size,
            },
            preview: file.type.startsWith("image/") ? `${base_url}/${endpoints.downloadFile}?path=${data.path}&token=${session?.accessToken}` : null,
          };
          uploadedFiles.push(fileObj);
        } else {
          console.error(`Upload failed for ${file.name}:`, data);
          showAlert(`Failed to upload "${file.name}": ${data.message || "Unknown error"}`, "error");
        }
      }
      if (uploadedFiles.length > 0) {
        setFiles((prev) => [...prev, ...uploadedFiles]);
        setShowUploadArea(false);
        // Show success message for file input uploads
        if (isFromFileInput) {
          if (uploadedFiles.length === 1) {
            showAlert(`File "${uploadedFiles[0].file.name}" uploaded successfully!`, "success");
          } else {
            showAlert(`${uploadedFiles.length} files uploaded successfully!`, "success");
          }
        }
      }

      await fetchContent();

      // Show success message for drag & drop
      if (!isFromFileInput && uploadedFiles.length > 0) {
        if (uploadedFiles.length === 1) {
          showAlert(`File "${uploadedFiles[0].file.name}" uploaded successfully!`, "success");
        } else {
          showAlert(`${uploadedFiles.length} files uploaded successfully!`, "success");
        }
      }
    } catch (error) {
      console.error("Error uploading:", error);
      showAlert(`Error uploading: ${error.message}`, "error");

      // Reset file input even on error
      if (isFromFileInput && fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleButtonClick = () => {
    if (isAdmin) {
      setShowUploadArea(true);
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    } else {
      showAlert("Only admins can upload files.", "error");
    }
  };

  const handleDoubleClick = () => {
    if (isAdmin) {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    } else {
      showAlert("Only admins can upload files.", "error");
    }
  };

  const handleFileClick = async (fileObj) => {
    const filePath = currentPath.length > 0 ? `${currentPath.join("/")}/${fileObj.file.name}` : fileObj.file.name;
    const fileType = fileObj.file.type.toLowerCase();

    // List of previewable file types
    const previewableTypes = ["jpg", "jpeg", "png", "gif", "pdf"];
    if (!previewableTypes.includes(fileType)) {
      setSelectedFile({ fileObj, filePath });
      setShowZipNotification(true);
      return;
    }

    try {
      if (["docx", "ppt", "pptx"].includes(fileType)) {
        const fileUrl = `${base_url}/${endpoints.downloadFile}?path=${encodeURIComponent(filePath)}&token=${session?.accessToken}`;
        window.open(`https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}`, "_blank");
      } else {
        const response = await fetch(`${base_url}/${endpoints.downloadFile}?path=${encodeURIComponent(filePath)}`, {
          headers: {
            Authorization: `Bearer ${session?.accessToken}`,
          },
        });
        if (response.ok) {
          const contentType = response.headers.get("Content-Type");
          const blob = await response.blob();
          const fileURL = URL.createObjectURL(new Blob([blob], { type: contentType }));
          window.open(fileURL, "_blank");
          setTimeout(() => URL.revokeObjectURL(fileURL), 1000);
        } else {
          const data = await response.json();
          console.error("Failed to fetch file:", data);
          showAlert(`Failed to open file: ${data.message || "Unknown error"}`, "error");
        }
      }
    } catch (error) {
      console.error("Error opening file:", error);
      showAlert(`Error opening file: ${error.message}`, "error");
    }
  };

  const handleDownloadFile = async () => {
    if (!selectedFile) return;

    const { filePath, fileObj } = selectedFile;
    try {
      const response = await fetch(`${base_url}/${endpoints.downloadFile}?path=${encodeURIComponent(filePath)}`, {
        headers: {
          Authorization: `Bearer ${session?.accessToken}`,
        },
      });
      if (response.ok) {
        const blob = await response.blob();
        const fileURL = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = fileURL;
        link.download = fileObj.file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(fileURL);
        showAlert(`File "${fileObj.file.name}" downloaded successfully!`, "success");
      } else {
        const data = await response.json();
        console.error("Failed to download file:", data);
        showAlert(`Failed to download file: ${data.message || "Unknown error"}`, "error");
      }
    } catch (error) {
      console.error("Error downloading file:", error);
      showAlert(`Error downloading file: ${error.message}`, "error");
    }
    setShowZipNotification(false);
    setSelectedFile(null);
  };

  useEffect(() => {
    const { files: currentFiles } = getCurrentFolderContent();
    return () => {
      currentFiles.forEach((fileObj) => {
        if (fileObj.preview) {
          URL.revokeObjectURL(fileObj.preview);
        }
      });
    };
  }, [currentPath]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (!isAdmin) {
        showAlert("Only admins can upload files.", "error");
        return;
      }
      const validFiles = acceptedFiles.filter((file) => file.size >= 0 && file.size <= 50 * 1024 * 1024);
      if (validFiles.length === 0) {
        showAlert("Please upload files between 0 MB and 50 MB.", "error");
        return;
      }

      // Reset file input before upload to ensure dialog closes
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      handleUpload(validFiles);
    },
    noClick: true,
    noKeyboard: true,
  });

  const theme = useTheme();
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);

  if (status === "loading") {
    return (
      <Dashboard>
        <Box sx={{ margin: { xs: 1, sm: 2, md: 3 } }}>
          <Typography variant="h6" textAlign="center">
            Loading session...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  const { folders: currentFolders, files: currentFiles } = getCurrentFolderContent();
  const hasContent = currentFolders.length > 0 || currentFiles.length > 0;

  return (
    <Dashboard>
      <Box
        ref={dropzoneRef}
        {...(isAdmin ? getRootProps() : {})}
        sx={{
          height: "calc(100vh - 80px)",
          overflowY: "auto",
          overflowX: "hidden",
          "&::-webkit-scrollbar": { width: 0 },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
          position: "relative",
          display: "flex",
          flexDirection: "column",
          backgroundColor: isDragActive ? "rgba(166, 101, 225, 0.1)" : "transparent",
          transition: "background-color 0.3s ease",
          scrollBehavior: "smooth",
          touchAction: "pan-y",
        }}
      >
        {isAdmin && <input {...getInputProps()} />}

        {/* Alert messages */}
        {alertMessage && (
          <Alert
            severity={alertSeverity}
            sx={{ mb: 2 }}
            onClose={() => setAlertMessage(null)}
          >
            {alertMessage}
          </Alert>
        )}

        {/* Drag overlay confined to ContentLibrary */}
        {isDragActive && isAdmin && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(166, 101, 225, 0.9)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 9999,
              borderRadius: 2,
              border: "4px dashed #ffffff",
              animation: "pulse 2s infinite",
              "@keyframes pulse": {
                "0%": {
                  borderColor: "#ffffff",
                  backgroundColor: "rgba(166, 101, 225, 0.8)",
                },
                "50%": {
                  borderColor: "#A665E1",
                  backgroundColor: "rgba(166, 101, 225, 0.9)",
                },
                "100%": {
                  borderColor: "#ffffff",
                  backgroundColor: "rgba(166, 101, 225, 0.8)",
                },
              },
            }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "white",
                backgroundColor: "rgba(0, 0, 0, 0.8)",
                padding: { xs: 3, sm: 4 },
                borderRadius: 3,
                boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
                border: "2px solid rgba(255, 255, 255, 0.2)",
              }}
            >
              <FileUpload sx={{ fontSize: { xs: 50, sm: 60 }, mb: 2, animation: "bounce 1s infinite" }} />
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1, fontSize: { xs: "1.5rem", sm: "2rem" } }}>
                Drop files here to upload
              </Typography>
              <Typography variant="body1" sx={{ fontSize: { xs: "0.9rem", sm: "1rem" } }}>
                Release to upload files to {currentPath.length > 0 ? `"${currentPath[currentPath.length - 1]}"` : "the library"}
              </Typography>
            </Box>
          </Box>
        )}
        <Box sx={{
          padding: { xs: 1, sm: 2, md: 3 },
          flex: 1,
          minHeight: 0,
        }}>
          <AppBar position="static" color="transparent" elevation={0} sx={{ padding: { xs: 0, sm: 1 } }}>
            <Toolbar sx={{ flexWrap: { xs: "wrap", sm: "nowrap" }, gap: 1 }}>
              <Typography
                variant="h5"
                sx={{
                  flexGrow: 1,
                  fontWeight: 700,
                  fontSize: { xs: "1.2rem", sm: "1.5rem", md: "1.8rem" },
                  color: theme.palette.text.white,
                }}
              >
                Hassana Library
              </Typography>
              {isAdmin && (
                <Box
                  sx={{
                    display: "flex",
                    gap: { xs: 1, sm: 2 },
                    flexWrap: "wrap",
                    justifyContent: { xs: "center", sm: "flex-end" },
                    width: { xs: "100%", sm: "auto" },
                    mt: { xs: 1, sm: 0 },
                  }}
                >
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleButtonClick}
                    sx={{
                      fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      padding: { xs: "6px 12px", sm: "8px 16px" },
                      minWidth: { xs: "100px", sm: "120px" },
                    }}
                  >
                    Upload
                  </Button>
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => setShowCreateDialog(true)}
                    sx={{
                      backgroundColor: "#A665E1 !important",
                      fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      padding: { xs: "6px 12px", sm: "8px 16px" },
                      minWidth: { xs: "100px", sm: "120px" },
                    }}
                  >
                    Create
                  </Button>
                </Box>
              )}
              {isAdmin && (
                <input type="file" multiple ref={fileInputRef} style={{ display: "none" }} onChange={handleUpload} accept="*" />
              )}
            </Toolbar>
          </AppBar>

          {currentPath.length > 0 && (
            <Box sx={{ mt: { xs: 1, sm: 2 }, mb: { xs: 1, sm: 2 } }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                <IconButton onClick={navigateBack} size="small">
                  <ArrowBackIcon sx={{ fontSize: { xs: "1.2rem", sm: "1.5rem" } }} />
                </IconButton>
                <Breadcrumbs sx={{ fontSize: { xs: "0.8rem", sm: "0.9rem", md: "1rem" } }}>
                  <Link component="button" variant="body1" onClick={() => setCurrentPath([])} sx={{ textDecoration: "none" }}>
                    Home
                  </Link>
                  {currentPath.map((pathPart, index) => (
                    <Link
                      key={index}
                      component="button"
                      variant="body1"
                      onClick={() => navigateToBreadcrumb(index)}
                      sx={{ textDecoration: "none" }}
                    >
                      {pathPart}
                    </Link>
                  ))}
                </Breadcrumbs>
              </Box>
            </Box>
          )}

          {hasContent ? (
            <Box
              sx={{
                flex: 1,
                overflowY: "auto",
                overflowX: "hidden",
                "&::-webkit-scrollbar": { width: 0 },
                scrollbarWidth: "none",
                msOverflowStyle: "none",
                paddingBottom: 2,
                scrollBehavior: "smooth",
                WebkitOverflowScrolling: "touch",
              }}
            >
              {currentFolders.length > 0 && (
                <Box sx={{ mb: { xs: 2, sm: 4 } }}>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 2,
                      fontWeight: 600,
                      color: theme.palette.text.headcolor,
                      borderBottom: "2px solid #A665E1",
                      paddingBottom: 1,
                      display: "inline-block",
                      fontSize: { xs: "1rem", sm: "1.25rem", md: "1.5rem" },
                    }}
                  >
                    <FolderIcon
                      sx={{
                        fontSize: { xs: 20, sm: 35 },
                        marginRight: 2,
                        color: "#A665E1",
                      }}
                    />
                    Folders ({currentFolders.length})
                  </Typography>
                  <Grid container spacing={{ xs: 1, sm: 2 }}>
                    {currentFolders.map((folder, index) => (
                      <Grid item xs={6} sm={4} md={3} lg={2} xl={1.5} key={`folder-${index}`}>
                        <Paper
                          sx={{
                            width: "100%",
                            maxWidth: { xs: 120, sm: 150, md: 150 },
                            height: { xs: 120, sm: 150 },
                            padding: 1,
                            textAlign: "center",
                            borderRadius: 3,
                            cursor: "pointer",
                            position: "relative",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-between",
                            overflow: "hidden",
                            backgroundColor: "#f8f5ff",
                            border: "2px solid #e8d8ff",
                            transition: "all 0.3s ease",
                            "&:hover": {
                              backgroundColor: "#f0e8ff",
                              transform: "translateY(-2px)",
                              boxShadow: "0 4px 12px rgba(166, 101, 225, 0.2)",
                            },
                          }}
                          onClick={() => handleFolderClick(folder.name)}
                        >
                          <IconButton
                            sx={{
                              position: "absolute",
                              top: 0,
                              right: 0,
                              zIndex: 10,
                              padding: { xs: "4px", sm: "8px" },
                            }}
                            onClick={(e) => handleMenuOpen(e, index, "folder")}
                          >
                            <MoreHorizIcon sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }} />
                          </IconButton>
                          <Box sx={{ flex: "0 0 80%", overflow: "hidden" }}>
                            <Box
                              sx={{
                                height: { xs: "80px", sm: "100px" },
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              <Image
                                src={"/folder-open.svg"}
                                alt={folder.name}
                                width={50}
                                height={50}
                                style={{
                                  width: "50%",
                                  height: "auto",
                                  objectFit: "cover",
                                  borderRadius: "8px",
                                }}
                              />
                            </Box>
                          </Box>
                          <Typography
                            variant="body1"
                            sx={{
                              mt: 1,
                              flex: "0 0 20%",
                              color: "#6b46c1",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              fontSize: { xs: "0.7rem", sm: "0.9rem" },
                              fontWeight: 600,
                            }}
                          >
                            {folder.name}
                          </Typography>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {currentFiles.length > 0 && (
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      mb: 2,
                      fontWeight: 600,
                      color: theme.palette.text.white,
                      borderBottom: "2px solid #A665E1",
                      paddingBottom: 1,
                      display: "inline-block",
                      fontSize: { xs: "1rem", sm: "1.25rem", md: "1.5rem" },
                    }}
                  >
                    <FileCopyRounded
                      sx={{
                        fontSize: { xs: 20, sm: 30 },
                        marginRight: 2,
                        color: "#A665E1",
                      }}
                    />
                    Files ({currentFiles.length})
                  </Typography>
                  <Grid container spacing={{ xs: 1, sm: 2 }}>
                    {currentFiles.map((fileObj, index) => (
                      <Grid item xs={6} sm={4} md={3} lg={2} xl={1.5} key={`file-${index}`}>
                        <Paper
                          sx={{
                            width: "100%",
                            maxWidth: { xs: 120, sm: 150, md: 150 },
                            height: { xs: 120, sm: 150 },
                            padding: 1,
                            textAlign: "center",
                            borderRadius: 3,
                            cursor: "pointer",
                            position: "relative",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-between",
                            overflow: "hidden",
                            backgroundColor: "#f0fdf4",
                            border: "2px solid #A665E1",
                            transition: "all 0.3s ease",
                            color: theme.palette.text.white,
                            "&:hover": {
                              backgroundColor: "#dcfce7",
                              transform: "translateY(-2px)",
                              boxShadow: "0 4px 12px #A665E1",
                            },
                          }}
                          onClick={() => handleFileClick(fileObj)}
                        >
                          <IconButton
                            sx={{
                              position: "absolute",
                              top: 0,
                              right: 0,
                              zIndex: 10,
                              padding: { xs: "4px", sm: "8px" },
                            }}
                            onClick={(e) => handleMenuOpen(e, index, "file")}
                          >
                            <MoreHorizIcon sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }} />
                          </IconButton>
                          <Box sx={{ flex: "0 0 80%", overflow: "hidden" }}>
                            {fileObj.preview ? (
                              <Image
                                src={fileObj.preview}
                                alt={fileObj.file.name}
                                width={150}
                                height={100}
                                style={{
                                  width: "100%",
                                  height: "auto",
                                  objectFit: "cover",
                                  borderRadius: "8px",
                                }}
                              />
                            ) : (
                              <Box
                                sx={{
                                  height: { xs: "80px", sm: "100px" },
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <Image
                                  src={getFileIcon(fileObj.file.type)}
                                  alt={`${fileObj.file.type} icon`}
                                  width={45}
                                  height={60}
                                  style={{
                                    width: "30%",
                                    height: "auto",
                                    objectFit: "contain",
                                    borderRadius: "8px",
                                  }}
                                />
                              </Box>
                            )}
                          </Box>
                          <Typography
                            variant="body1"
                            sx={{
                              mt: 1,
                              flex: "0 0 20%",
                              color: "#A665E1",
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              fontSize: { xs: "0.7rem", sm: "0.9rem" },
                              fontWeight: 600,
                            }}
                          >
                            {fileObj.file.name}
                          </Typography>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: "center", mt: 4 }}>
              <Typography variant="h5" color="black" sx={{ fontSize: { xs: "1.2rem", sm: "1.5rem" }, mb: 2 }}>
                No Data Found
              </Typography>
              {isAdmin && (
                <Typography variant="body2" sx={{ color: "#666", fontSize: { xs: "0.8rem", sm: "0.9rem" } }}>
                  Drag and drop files here to upload them
                </Typography>
              )}
            </Box>
          )}

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              sx: { minWidth: { xs: 120, sm: 160 }, zIndex: 10 },
            }}
          >
            <MenuItem onClick={handleDelete} sx={{ fontSize: { xs: "0.8rem", sm: "0.9rem" } }}>
              Delete
            </MenuItem>
            <MenuItem
              onClick={handleRename}
              sx={{ fontSize: { xs: "0.8rem", sm: "0.9rem" } }}
              disabled={!isAdmin}
            >
              Rename
            </MenuItem>
          </Menu>

          <Dialog
            open={showCreateDialog}
            onClose={() => setShowCreateDialog(false)}
            fullWidth
            maxWidth="sm"
            sx={{ zIndex: 10 }}
          >
            <DialogTitle sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}>Create New Folder</DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                label="Folder Name"
                fullWidth
                variant="outlined"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    handleCreateFolder();
                  }
                }}
                sx={{ mt: 1 }}
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setShowCreateDialog(false)} sx={{ fontSize: { xs: "0.8rem", sm: "0.9rem" } }}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateFolder}
                variant="contained"
                sx={{
                  backgroundColor: "#A665E1 !important",
                  fontSize: { xs: "0.8rem", sm: "0.9rem" },
                }}
              >
                Create
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            open={showRenameDialog}
            onClose={() => {
              setShowRenameDialog(false);
              setSelectedFileIndex(null);
              setSelectedItemType(null);
              setNewItemName("");
            }}
            fullWidth
            maxWidth="sm"
            sx={{ zIndex: 10 }}
          >
            <DialogTitle sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}>
              Rename {selectedItemType === "folder" ? "Folder" : "File"}
            </DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                label="New Name"
                fullWidth
                variant="outlined"
                value={newItemName}
                onChange={(e) => setNewItemName(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    handleRenameSubmit();
                  }
                }}
                sx={{ mt: 1 }}
              />
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setShowRenameDialog(false);
                  setSelectedFileIndex(null);
                  setSelectedItemType(null);
                  setNewItemName("");
                }}
                sx={{ fontSize: { xs: "0.8rem", sm: "0.9rem" } }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleRenameSubmit}
                variant="contained"
                sx={{
                  backgroundColor: "#A665E1 !important",
                  fontSize: { xs: "0.8rem", sm: "0.9rem" },
                }}
              >
                Rename
              </Button>
            </DialogActions>
          </Dialog>

          {showZipNotification && (
            <Box
              sx={{
                position: "fixed",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                backgroundColor: "#000",
                display: "flex",
                zIndex: 10000,
              }}
              onClick={() => {
                setShowZipNotification(false);
                setSelectedFile(null);
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  flexGrow: 1,
                }}
              >
                <Box
                  sx={{
                    backgroundColor: "#4a4a4a",
                    color: "#ffffff",
                    padding: "10px 20px",
                    borderRadius: "5px",
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                  }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Typography variant="body1">No preview available</Typography>
                  <Button
                    variant="contained"
                    onClick={handleDownloadFile}
                    sx={{
                      backgroundColor: "#A665E1",
                      color: "#ffffff",
                      padding: "5px 15px",
                      fontSize: "0.9rem",
                      "&:hover": {
                        backgroundColor: "#8d4ec5",
                      },
                    }}
                  >
                    Download
                  </Button>
                </Box>
              </Box>
            </Box>
          )}

          {/* Floating drag & drop indicator */}
          {isAdmin && !isDragActive && (
            <Box
              sx={{
                position: "absolute",
                bottom: 20,
                right: 20,
                backgroundColor: "rgba(166, 101, 225, 0.9)",
                color: "white",
                padding: "8px 12px",
                borderRadius: "20px",
                fontSize: "0.75rem",
                fontWeight: 500,
                zIndex: 100,
                display: "flex",
                alignItems: "center",
                gap: 1,
                boxShadow: "0 4px 12px rgba(166, 101, 225, 0.3)",
                opacity: 0.8,
                transition: "opacity 0.3s ease",
                "&:hover": {
                  opacity: 1,
                },
              }}
            >
              <FileUpload sx={{ fontSize: 16 }} />
              <Typography variant="caption" sx={{ fontSize: "0.75rem" }}>
                Drag files to upload
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Dashboard>
  );
};

export default withAuth(ContentLibrary);