"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "@apollo/client":
/*!*********************************!*\
  !*** external "@apollo/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@apollo/client");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/credentials":
/*!**************************************************!*\
  !*** external "next-auth/providers/credentials" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/credentials");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].js */ \"(api)/./src/pages/api/auth/[...nextauth].js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: () => (/* binding */ baseUrl),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\n// export const baseUrl = \"http://localhost:3001\";\nconst baseUrl = \"https://hassana-api.360xpertsolutions.com\";\n//export const baseUrl = \"https://v2-api.hassana.com.sa\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    link: httpLink,\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache(),\n    onError: ({ operation, networkError, response })=>{\n        console.log(`[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `, response);\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (client);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./src/Data/ApolloClient.js\n");

/***/ }),

/***/ "(api)/./src/Data/Auth.js":
/*!**************************!*\
  !*** ./src/Data/Auth.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LOGIN_USER: () => (/* binding */ LOGIN_USER)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LOGIN_USER = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  query LoginUser($username: String!, $password: String!) {\r\n    loginUser(username: $username, password: $password) {\r\n      username\r\n      role\r\n      token\r\n      id\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvRGF0YS9BdXRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUU5QixNQUFNQyxhQUFhRCwrQ0FBRyxDQUFDOzs7Ozs7Ozs7QUFTOUIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktZGFzaGJvYXJkLy4vc3JjL0RhdGEvQXV0aC5qcz82MDBjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdxbCB9IGZyb20gXCJAYXBvbGxvL2NsaWVudFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IExPR0lOX1VTRVIgPSBncWxgXHJcbiAgcXVlcnkgTG9naW5Vc2VyKCR1c2VybmFtZTogU3RyaW5nISwgJHBhc3N3b3JkOiBTdHJpbmchKSB7XHJcbiAgICBsb2dpblVzZXIodXNlcm5hbWU6ICR1c2VybmFtZSwgcGFzc3dvcmQ6ICRwYXNzd29yZCkge1xyXG4gICAgICB1c2VybmFtZVxyXG4gICAgICByb2xlXHJcbiAgICAgIHRva2VuXHJcbiAgICAgIGlkXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG4iXSwibmFtZXMiOlsiZ3FsIiwiTE9HSU5fVVNFUiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./src/Data/Auth.js\n");

/***/ }),

/***/ "(api)/./src/Data/AuthClientServer.js":
/*!**************************************!*\
  !*** ./src/Data/AuthClientServer.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ApolloClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ApolloClient */ \"(api)/./src/Data/ApolloClient.js\");\n\n\n//export const baseUrl = \"http://10.0.1.16:3001\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n// export const baseUrl = \"http://*************:3001\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.createHttpLink)({\n    uri: _ApolloClient__WEBPACK_IMPORTED_MODULE_1__.baseUrl + \"/graphql\"\n});\nconst authclient = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    link: httpLink,\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache(),\n    onError: ({ operation, networkError, response })=>{\n        console.log(`[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `, response);\n    }\n});\nauthclient.resetCache = async ()=>{\n    await authclient.cache.reset();\n    console.log(\"Apollo Client cache has been reset.\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authclient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./src/Data/AuthClientServer.js\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].js":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Data_AuthClientServer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/Data/AuthClientServer */ \"(api)/./src/Data/AuthClientServer.js\");\n/* harmony import */ var _Data_Auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Data/Auth */ \"(api)/./src/Data/Auth.js\");\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Data/ApolloClient */ \"(api)/./src/Data/ApolloClient.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/credentials */ \"next-auth/providers/credentials\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_3___default()({\n    providers: [\n        next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4___default()({\n            name: \"Credentials\",\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"jsmith\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                try {\n                    const { data, error } = await _Data_AuthClientServer__WEBPACK_IMPORTED_MODULE_0__[\"default\"].query({\n                        query: _Data_Auth__WEBPACK_IMPORTED_MODULE_1__.LOGIN_USER,\n                        variables: {\n                            username,\n                            password\n                        }\n                    });\n                    console.log(\"+++++++data\", data);\n                    console.log(\"+++++++++++++++++++++++++error\", error);\n                    const { loginUser } = data;\n                    console.log(\"loginUser\", loginUser);\n                    if (loginUser) {\n                        console.log(loginUser, \"Login\");\n                        return {\n                            ...loginUser,\n                            accessToken: loginUser.token || \"test\"\n                        };\n                    } else {\n                        console.log(\"No user found\");\n                        return null;\n                    }\n                } catch (error) {\n                    console.log(\"Error:\", error.message);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        jwt: false\n    },\n    jwt: {\n        secret: \"test\",\n        encryption: true\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            console.log(\"JWT----->\", user);\n            if (user) {\n                token.id = user.id;\n                token.role = user.role.toUpperCase();\n                token.name = user.username;\n                token.accessToken = user.accessToken;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            session.user.id = token.id;\n            session.user.role = token.role;\n            session.user.username = token.name;\n            session.accessToken = token.accessToken;\n            console.log(\"JSESSIOWT----->\", session);\n            return session;\n        }\n    }\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpRDtBQUNSO0FBQ0k7QUFFWjtBQUNpQztBQUVsRSxpRUFBZUcsZ0RBQVFBLENBQUM7SUFDdEJFLFdBQVc7UUFDVEQsc0VBQW1CQSxDQUFDO1lBQ2xCRSxNQUFNO1lBQ05DLGFBQWE7Z0JBQ1hDLFVBQVU7b0JBQUVDLE9BQU87b0JBQVlDLE1BQU07b0JBQVFDLGFBQWE7Z0JBQVM7Z0JBQ25FQyxVQUFVO29CQUFFSCxPQUFPO29CQUFZQyxNQUFNO2dCQUFXO1lBQ2xEO1lBQ0EsTUFBTUcsV0FBVU4sV0FBVztnQkFDekIsTUFBTSxFQUFFQyxRQUFRLEVBQUVJLFFBQVEsRUFBRSxHQUFHTDtnQkFFL0IsSUFBSTtvQkFFRixNQUFNLEVBQUVPLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWYsb0VBQWdCLENBQUM7d0JBQzdDZ0IsT0FBT2Ysa0RBQVVBO3dCQUNqQmdCLFdBQVc7NEJBQUVUOzRCQUFVSTt3QkFBUztvQkFDbEM7b0JBQ0FNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlTDtvQkFDM0JJLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NKO29CQUM5QyxNQUFNLEVBQUVLLFNBQVMsRUFBRSxHQUFHTjtvQkFDdEJJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQztvQkFFekIsSUFBSUEsV0FBVzt3QkFDYkYsUUFBUUMsR0FBRyxDQUFDQyxXQUFXO3dCQUN2QixPQUFPOzRCQUNMLEdBQUdBLFNBQVM7NEJBQ1pDLGFBQWFELFVBQVVFLEtBQUssSUFBSTt3QkFDbEM7b0JBQ0YsT0FBTzt3QkFDTEosUUFBUUMsR0FBRyxDQUFDO3dCQUNaLE9BQU87b0JBQ1Q7Z0JBQ0YsRUFBRSxPQUFPSixPQUFPO29CQUNkRyxRQUFRQyxHQUFHLENBQUMsVUFBVUosTUFBTVEsT0FBTztvQkFDbkMsT0FBTztnQkFDVDtZQUNGO1FBQ0Y7S0FDRDtJQUNEQyxTQUFTO1FBQ1BDLEtBQUs7SUFDUDtJQUNBQSxLQUFLO1FBQ0hDLFFBQVE7UUFDUkMsWUFBWTtJQUNkO0lBQ0FDLFdBQVc7UUFDVCxNQUFNSCxLQUFJLEVBQUVILEtBQUssRUFBRU8sSUFBSSxFQUFFO1lBQ3ZCWCxRQUFRQyxHQUFHLENBQUMsYUFBWVU7WUFDeEIsSUFBSUEsTUFBTTtnQkFDUlAsTUFBTVEsRUFBRSxHQUFHRCxLQUFLQyxFQUFFO2dCQUNsQlIsTUFBTVMsSUFBSSxHQUFHRixLQUFLRSxJQUFJLENBQUNDLFdBQVc7Z0JBQ2xDVixNQUFNaEIsSUFBSSxHQUFHdUIsS0FBS3JCLFFBQVE7Z0JBQzFCYyxNQUFNRCxXQUFXLEdBQUdRLEtBQUtSLFdBQVc7WUFDdEM7WUFDQSxPQUFPQztRQUNUO1FBQ0EsTUFBTUUsU0FBUSxFQUFFQSxPQUFPLEVBQUVGLEtBQUssRUFBRTtZQUM5QkUsUUFBUUssSUFBSSxDQUFDQyxFQUFFLEdBQUdSLE1BQU1RLEVBQUU7WUFDMUJOLFFBQVFLLElBQUksQ0FBQ0UsSUFBSSxHQUFHVCxNQUFNUyxJQUFJO1lBQzlCUCxRQUFRSyxJQUFJLENBQUNyQixRQUFRLEdBQUdjLE1BQU1oQixJQUFJO1lBQ2xDa0IsUUFBUUgsV0FBVyxHQUFHQyxNQUFNRCxXQUFXO1lBQ3ZDSCxRQUFRQyxHQUFHLENBQUMsbUJBQWtCSztZQUM5QixPQUFPQTtRQUNUO0lBQ0Y7QUFDRixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS1kYXNoYm9hcmQvLi9zcmMvcGFnZXMvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS5qcz83OGFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBhdXRoY2xpZW50IGZyb20gXCJAL0RhdGEvQXV0aENsaWVudFNlcnZlclwiO1xyXG5pbXBvcnQgeyBMT0dJTl9VU0VSIH0gZnJvbSBcIkAvRGF0YS9BdXRoXCI7XHJcbmltcG9ydCB7IGNsaWVudCB9IGZyb20gXCJAL0RhdGEvQXBvbGxvQ2xpZW50XCI7XHJcblxyXG5pbXBvcnQgTmV4dEF1dGggZnJvbSBcIm5leHQtYXV0aFwiO1xyXG5pbXBvcnQgQ3JlZGVudGlhbHNQcm92aWRlciBmcm9tIFwibmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFsc1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTmV4dEF1dGgoe1xyXG4gIHByb3ZpZGVyczogW1xyXG4gICAgQ3JlZGVudGlhbHNQcm92aWRlcih7XHJcbiAgICAgIG5hbWU6IFwiQ3JlZGVudGlhbHNcIixcclxuICAgICAgY3JlZGVudGlhbHM6IHtcclxuICAgICAgICB1c2VybmFtZTogeyBsYWJlbDogXCJVc2VybmFtZVwiLCB0eXBlOiBcInRleHRcIiwgcGxhY2Vob2xkZXI6IFwianNtaXRoXCIgfSxcclxuICAgICAgICBwYXNzd29yZDogeyBsYWJlbDogXCJQYXNzd29yZFwiLCB0eXBlOiBcInBhc3N3b3JkXCIgfSxcclxuICAgICAgfSxcclxuICAgICAgYXN5bmMgYXV0aG9yaXplKGNyZWRlbnRpYWxzKSB7XHJcbiAgICAgICAgY29uc3QgeyB1c2VybmFtZSwgcGFzc3dvcmQgfSA9IGNyZWRlbnRpYWxzO1xyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgIFxyXG4gICAgICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgYXV0aGNsaWVudC5xdWVyeSh7XHJcbiAgICAgICAgICAgIHF1ZXJ5OiBMT0dJTl9VU0VSLFxyXG4gICAgICAgICAgICB2YXJpYWJsZXM6IHsgdXNlcm5hbWUsIHBhc3N3b3JkIH0sXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwiKysrKysrK2RhdGFcIiwgZGF0YSk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIisrKysrKysrKysrKysrKysrKysrKysrKytlcnJvclwiLCBlcnJvcik7XHJcbiAgICAgICAgICBjb25zdCB7IGxvZ2luVXNlciB9ID0gZGF0YTtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKFwibG9naW5Vc2VyXCIsIGxvZ2luVXNlcik7XHJcblxyXG4gICAgICAgICAgaWYgKGxvZ2luVXNlcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhsb2dpblVzZXIsIFwiTG9naW5cIik7XHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgLi4ubG9naW5Vc2VyLFxyXG4gICAgICAgICAgICAgIGFjY2Vzc1Rva2VuOiBsb2dpblVzZXIudG9rZW4gfHwgXCJ0ZXN0XCIsIC8vIEVuc3VyZSB0b2tlbiBpcyByZXR1cm5lZCBmcm9tIGxvZ2luVXNlclxyXG4gICAgICAgICAgICB9O1xyXG4gICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJObyB1c2VyIGZvdW5kXCIpO1xyXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coXCJFcnJvcjpcIiwgZXJyb3IubWVzc2FnZSk7XHJcbiAgICAgICAgICByZXR1cm4gbnVsbDtcclxuICAgICAgICB9XHJcbiAgICAgIH0sXHJcbiAgICB9KSxcclxuICBdLFxyXG4gIHNlc3Npb246IHtcclxuICAgIGp3dDogZmFsc2UsIC8vIFlvdSdyZSB1c2luZyBzZXNzaW9uLWJhc2VkIGF1dGgsIG5vdCBKV1RcclxuICB9LFxyXG4gIGp3dDoge1xyXG4gICAgc2VjcmV0OiBcInRlc3RcIixcclxuICAgIGVuY3J5cHRpb246IHRydWUsXHJcbiAgfSxcclxuICBjYWxsYmFja3M6IHtcclxuICAgIGFzeW5jIGp3dCh7IHRva2VuLCB1c2VyIH0pIHtcclxuICAgICAgY29uc29sZS5sb2coJ0pXVC0tLS0tPicsdXNlcik7XHJcbiAgICAgIGlmICh1c2VyKSB7XHJcbiAgICAgICAgdG9rZW4uaWQgPSB1c2VyLmlkO1xyXG4gICAgICAgIHRva2VuLnJvbGUgPSB1c2VyLnJvbGUudG9VcHBlckNhc2UoKTtcclxuICAgICAgICB0b2tlbi5uYW1lID0gdXNlci51c2VybmFtZTtcclxuICAgICAgICB0b2tlbi5hY2Nlc3NUb2tlbiA9IHVzZXIuYWNjZXNzVG9rZW47IFxyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiB0b2tlbjtcclxuICAgIH0sXHJcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfSkge1xyXG4gICAgICBzZXNzaW9uLnVzZXIuaWQgPSB0b2tlbi5pZDtcclxuICAgICAgc2Vzc2lvbi51c2VyLnJvbGUgPSB0b2tlbi5yb2xlO1xyXG4gICAgICBzZXNzaW9uLnVzZXIudXNlcm5hbWUgPSB0b2tlbi5uYW1lO1xyXG4gICAgICBzZXNzaW9uLmFjY2Vzc1Rva2VuID0gdG9rZW4uYWNjZXNzVG9rZW47XHJcbiAgICAgIGNvbnNvbGUubG9nKCdKU0VTU0lPV1QtLS0tLT4nLHNlc3Npb24pOyBcclxuICAgICAgcmV0dXJuIHNlc3Npb247XHJcbiAgICB9LFxyXG4gIH0sXHJcbn0pOyJdLCJuYW1lcyI6WyJhdXRoY2xpZW50IiwiTE9HSU5fVVNFUiIsImNsaWVudCIsIk5leHRBdXRoIiwiQ3JlZGVudGlhbHNQcm92aWRlciIsInByb3ZpZGVycyIsIm5hbWUiLCJjcmVkZW50aWFscyIsInVzZXJuYW1lIiwibGFiZWwiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJwYXNzd29yZCIsImF1dGhvcml6ZSIsImRhdGEiLCJlcnJvciIsInF1ZXJ5IiwidmFyaWFibGVzIiwiY29uc29sZSIsImxvZyIsImxvZ2luVXNlciIsImFjY2Vzc1Rva2VuIiwidG9rZW4iLCJtZXNzYWdlIiwic2Vzc2lvbiIsImp3dCIsInNlY3JldCIsImVuY3J5cHRpb24iLCJjYWxsYmFja3MiLCJ1c2VyIiwiaWQiLCJyb2xlIiwidG9VcHBlckNhc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();