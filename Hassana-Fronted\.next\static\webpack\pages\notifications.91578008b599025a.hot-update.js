"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject2());\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notification-details/\".concat(notificationData.id),\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        onClick: ()=>removeHandler(notificationData.id),\n                                                        sx: {\n                                                            p: 2,\n                                                            borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                                cursor: \"pointer\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\",\n                                                            position: \"relative\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 2,\n                                                                    alignItems: \"flex-start\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            width: 40,\n                                                                            height: 40,\n                                                                            borderRadius: \"50%\",\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            justifyContent: \"center\",\n                                                                            flexShrink: 0,\n                                                                            boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                                            sx: {\n                                                                                color: \"white\",\n                                                                                fontSize: \"20px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1,\n                                                                            minWidth: 0\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"body1\",\n                                                                                sx: {\n                                                                                    fontSize: \"14px\",\n                                                                                    fontWeight: 500,\n                                                                                    lineHeight: \"20px\",\n                                                                                    color: \"#333\",\n                                                                                    mb: 0.5,\n                                                                                    wordBreak: \"break-word\"\n                                                                                },\n                                                                                children: notificationData.notification\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"caption\",\n                                                                                sx: {\n                                                                                    fontSize: \"12px\",\n                                                                                    color: \"#666\",\n                                                                                    display: \"flex\",\n                                                                                    alignItems: \"center\",\n                                                                                    gap: 0.5\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                        sx: {\n                                                                                            fontSize: \"4px\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                        lineNumber: 335,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    position: \"absolute\",\n                                                                    left: 0,\n                                                                    top: 0,\n                                                                    bottom: 0,\n                                                                    width: \"3px\",\n                                                                    borderRadius: \"0 2px 2px 0\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, notificationData.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 434,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getUnseenNotificationsCount, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 10000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\"\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    const [markAllAsSeen] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationMarkAllNotificationsAsSeen);\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.notifications);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.notifications)) {\n            const allNotifications = data.notifications;\n            const currentCount = allNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"All notifications received:\", allNotifications);\n            console.log(\"Count:\", currentCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 1000);\n            }\n            setNotifications(allNotifications);\n            setPreviousNotificationCount(currentCount);\n            console.log(\"Notification count updated to: \".concat(currentCount));\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 585,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = async ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        if (!wasOpen && userId) {\n            try {\n                await markAllAsSeen({\n                    variables: {\n                        userId\n                    }\n                });\n                refetchUnseenCount();\n                console.log(\"All notifications marked as seen\");\n            } catch (error) {\n                console.error(\"Error marking notifications as seen:\", error);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        setSelectedNotification(null); // Reset selected notification when closing\n    };\n    const handleNotificationClick = (notification)=>{\n        setSelectedNotification(notification);\n    };\n    const handleBackToList = ()=>{\n        setSelectedNotification(null);\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 699,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 706,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 717,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 716,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 709,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: \"''\",\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 756,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.blue.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.blue.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.green.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.green.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.purple.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.purple.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 784,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 783,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 774,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications} notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"8px\",\n                                                    sm: \"10px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: (unseenCountData === null || unseenCountData === void 0 ? void 0 : unseenCountData.unseenNotificationsCount) || 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 847,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 831,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme,\n                                    selectedNotification: selectedNotification,\n                                    onNotificationClick: handleNotificationClick,\n                                    onBackToList: handleBackToList\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 869,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 726,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 679,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid white\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 911,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 902,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 941,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 940,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 945,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 950,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 969,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 968,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 980,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 991,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1001,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1014,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1013,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 913,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 894,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 886,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1024,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1019,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Bd4QzAVOel/6dhCABCMIh6zDEbc=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvSGVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDcUM7QUFDa0M7QUFDZjtBQUNYO0FBS2pCO0FBQ1E7QUFDb0I7QUFDWDtBQUNEO0FBQ047QUFDWTtBQUNBO0FBWXJCO0FBQ21DO0FBQ047QUFDVjtBQUNRO0FBQ0E7QUFDQTtBQUNFO0FBQ047QUFDckI7QUFDUztBQUNYO0FBZU47QUFDb0I7QUFDSztBQUNQO0FBQ2M7QUFNMUI7QUFDcUM7QUFDckI7QUFDOEI7QUFDVjtBQUNoQztBQUNPO0FBRXhDLE1BQU13RSxjQUFjO0FBRXBCLHdDQUF3QztBQUN4QyxNQUFNQyxRQUFRckIsZ05BQVNBO0FBTXZCLE1BQU1zQixRQUFRdEIsZ05BQVNBO0FBTXZCLE1BQU11QixPQUFPdkIsZ05BQVNBO0FBTXRCLE1BQU13QixnQkFBZ0J0RSw2REFBTUEsQ0FBQ2dDLDREQUFLQSxFQUFFO1FBQUMsRUFBRXVDLEtBQUssRUFBRUMsbUJBQW1CLEVBQUU7V0FBTTtRQUN2RSxxQkFBcUI7WUFDbkJDLGlCQUFpQjtZQUNqQnJFLE9BQU87WUFDUHNFLFlBQVk7WUFDWkMsVUFBVTtZQUNWQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxRQUFRO1lBQ1JDLFdBQVdSLHNCQUNQLEdBQXlCSCxPQUF0QkYsT0FBTSxrQkFBcUIsT0FBTEUsTUFBSyxrQkFDOUI7WUFDSlksV0FBVztRQUNiO0lBQ0Y7QUFBQTtLQWZNWDtBQWlCTixNQUFNWSwyQkFBMkJsRiw2REFBTUEsQ0FBQ00sMERBQUdBLEVBQUU7UUFBQyxFQUFFaUUsS0FBSyxFQUFFQyxtQkFBbUIsRUFBRTtXQUFNO1FBQ2hGUSxXQUFXUixzQkFBc0IsR0FBUyxPQUFOSixPQUFNLHVCQUFxQjtRQUMvRCxXQUFXO1lBQ1RlLFdBQVc7WUFDWEMsWUFBWTtRQUNkO0lBQ0Y7QUFBQTtNQU5NRjtBQVFOLE1BQU1HLFNBQVNyRiw2REFBTUEsQ0FBQ1EsNkRBQVNBLEVBQUU7SUFDL0I4RSxtQkFBbUIsQ0FBQ0MsT0FBU0EsU0FBUztBQUN4QyxHQUFHO1FBQUMsRUFBRWhCLEtBQUssRUFBRWlCLElBQUksRUFBRTtXQUFNO1FBQ3ZCQyxRQUFRbEIsTUFBTWtCLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHO1FBQzlCTixZQUFZYixNQUFNb0IsV0FBVyxDQUFDQyxNQUFNLENBQUM7WUFBQztZQUFTO1NBQVMsRUFBRTtZQUN4REMsUUFBUXRCLE1BQU1vQixXQUFXLENBQUNFLE1BQU0sQ0FBQ0MsS0FBSztZQUN0Q0MsVUFBVXhCLE1BQU1vQixXQUFXLENBQUNJLFFBQVEsQ0FBQ0MsYUFBYTtRQUNwRDtRQUNBQyxZQUFZVCxPQUFPdEIsY0FBYztRQUNqQ2dDLE9BQU9WLE9BQU8sZUFBMkIsT0FBWnRCLGFBQVksT0FBSztRQUM5QyxDQUFDSyxNQUFNNEIsV0FBVyxDQUFDQyxFQUFFLENBQUMsTUFBTSxFQUFFO1lBQzVCSCxZQUFZVCxPQUFPdEIsY0FBY0ssTUFBTThCLE9BQU8sQ0FBQztZQUMvQ0gsT0FBT1YsT0FDSCxlQUEyQixPQUFadEIsYUFBWSxPQUMzQixlQUFnQyxPQUFqQkssTUFBTThCLE9BQU8sQ0FBQyxJQUFHO1FBQ3RDO1FBQ0EsQ0FBQzlCLE1BQU00QixXQUFXLENBQUNHLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDOUJMLFlBQVk7WUFDWkMsT0FBTztRQUNUO1FBQ0EsR0FBSVYsUUFBUTtZQUNWSixZQUFZYixNQUFNb0IsV0FBVyxDQUFDQyxNQUFNLENBQUM7Z0JBQUM7Z0JBQVM7YUFBUyxFQUFFO2dCQUN4REMsUUFBUXRCLE1BQU1vQixXQUFXLENBQUNFLE1BQU0sQ0FBQ0MsS0FBSztnQkFDdENDLFVBQVV4QixNQUFNb0IsV0FBVyxDQUFDSSxRQUFRLENBQUNRLGNBQWM7WUFDckQ7UUFDRixDQUFDO0lBQ0g7QUFBQTtNQTFCTWxCO0FBNEJOLE1BQU1tQixTQUFTeEcsNkRBQU1BLENBQUNFLDZEQUFTQSxFQUFFO0lBQy9Cb0YsbUJBQW1CLENBQUNDLE9BQVNBLFNBQVM7QUFDeEMsR0FBRztRQUFDLEVBQUVoQixLQUFLLEVBQUVpQixJQUFJLEVBQUU7V0FBTTtRQUN2QixzQkFBc0I7WUFDcEJmLGlCQUFpQkYsTUFBTWtDLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDQyxTQUFTO1lBQ25EQyxVQUFVO1lBQ1ZDLFlBQVk7WUFDWlgsT0FBT1YsT0FBT3RCLGNBQWNLLE1BQU04QixPQUFPLENBQUM7WUFDMUNqQixZQUFZYixNQUFNb0IsV0FBVyxDQUFDQyxNQUFNLENBQUMsU0FBUztnQkFDNUNDLFFBQVF0QixNQUFNb0IsV0FBVyxDQUFDRSxNQUFNLENBQUNDLEtBQUs7Z0JBQ3RDQyxVQUFVeEIsTUFBTW9CLFdBQVcsQ0FBQ0ksUUFBUSxDQUFDZSxPQUFPO1lBQzlDO1lBQ0FDLFdBQVc7WUFDWCxHQUFJLENBQUN2QixRQUFRO2dCQUNYd0IsV0FBVztnQkFDWDVCLFlBQVliLE1BQU1vQixXQUFXLENBQUNDLE1BQU0sQ0FBQyxTQUFTO29CQUM1Q0MsUUFBUXRCLE1BQU1vQixXQUFXLENBQUNFLE1BQU0sQ0FBQ0MsS0FBSztvQkFDdENDLFVBQVV4QixNQUFNb0IsV0FBVyxDQUFDSSxRQUFRLENBQUNDLGFBQWE7Z0JBQ3BEO2dCQUNBRSxPQUFPM0IsTUFBTThCLE9BQU8sQ0FBQztnQkFDckIsQ0FBQzlCLE1BQU00QixXQUFXLENBQUNDLEVBQUUsQ0FBQyxNQUFNLEVBQUU7b0JBQzVCRixPQUFPM0IsTUFBTThCLE9BQU8sQ0FBQztnQkFDdkI7Z0JBQ0EsQ0FBQzlCLE1BQU00QixXQUFXLENBQUNHLElBQUksQ0FBQyxNQUFNLEVBQUU7b0JBQzlCSixPQUFPO2dCQUNUO1lBQ0YsQ0FBQztRQUNIO0lBQ0Y7QUFBQTtNQTVCTU07QUE4Qk4sa0RBQWtEO0FBQ2xELE1BQU1TLDJCQUEyQjtRQUFDLEVBQ2hDekIsSUFBSSxFQUNKMEIsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLGFBQWEsRUFDYkMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLGFBQWEsRUFDYmhELEtBQUssRUFDTjtXQUNDLDhEQUFDckMseU1BQU1BO1FBQ0xzRCxNQUFNQTtRQUNOMEIsVUFBVUE7UUFDVk0sTUFBTUM7UUFDTnJDLFVBQVU7UUFDVnNDLElBQUk7WUFDRkMsV0FBV1AsY0FBY1EsTUFBTSxHQUFHLElBQUksU0FBUztZQUMvQ0MsV0FBV1QsY0FBY1EsTUFBTSxHQUFHLElBQUksU0FBUztZQUMvQ25DLFFBQVE7WUFDUlMsT0FBTztZQUNQNEIsVUFBVTtRQUNaO1FBQ0FDLGFBQWE7UUFDYkMsZUFBZTtZQUNiQyxXQUFXO2dCQUNUO29CQUFFQyxNQUFNO29CQUFVQyxTQUFTO3dCQUFFQyxRQUFROzRCQUFDOzRCQUFHO3lCQUFHO29CQUFDO2dCQUFFO2dCQUMvQztvQkFBRUYsTUFBTTtvQkFBbUJDLFNBQVM7d0JBQUVFLFNBQVM7b0JBQUc7Z0JBQUU7YUFDckQ7UUFDSDtrQkFFQztnQkFBQyxFQUFFQyxlQUFlLEVBQUU7bUJBQ25CLDhEQUFDekYsdU1BQUlBO2dCQUFFLEdBQUd5RixlQUFlO2dCQUFFQyxTQUFTOzBCQUNsQyw0RUFBQ3BHLHdNQUFLQTtvQkFDSnFHLFdBQVc7b0JBQ1hkLElBQUk7d0JBQ0Y1QyxjQUFjO3dCQUNkMkQsVUFBVTt3QkFDVjFELFFBQVE7d0JBQ1IyRCxnQkFBZ0I7d0JBQ2hCaEMsWUFDRTt3QkFDRnpCLFdBQ0U7b0JBQ0o7OEJBRUEsNEVBQUM3QyxvTkFBaUJBO3dCQUFDdUcsYUFBYXhCO2tDQUM5Qiw0RUFBQzdHLDBEQUFHQTs0QkFBQ29ILElBQUk7Z0NBQUVJLFVBQVU7Z0NBQVNsRCxVQUFVOzRCQUFROzs4Q0FDOUMsOERBQUN0RSwwREFBR0E7b0NBQ0ZvSCxJQUFJO3dDQUNGa0IsR0FBRzt3Q0FDSEMsY0FBYzt3Q0FDZG5DLFlBQVluQyxNQUFNa0MsT0FBTyxDQUFDQyxVQUFVLENBQUNvQyxNQUFNO3dDQUMzQzFJLE9BQU9tRSxNQUFNa0MsT0FBTyxDQUFDc0MsSUFBSSxDQUFDQyxLQUFLO29DQUNqQzs4Q0FFQSw0RUFBQ3JJLGlFQUFVQTt3Q0FDVHNJLFNBQVE7d0NBQ1J2QixJQUFJOzRDQUNGaEQsWUFBWTs0Q0FDWkMsVUFBVTs0Q0FDVnVFLFNBQVM7NENBQ1RDLFlBQVk7NENBQ1pDLEtBQUs7d0NBQ1A7OzBEQUVBLDhEQUFDOUgsNE5BQW1CQTtnREFBQ29HLElBQUk7b0RBQUUvQyxVQUFVO2dEQUFPOzs7Ozs7NENBQUs7NENBRWhEeUMsY0FBY1EsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ3RILDBEQUFHQTtnREFDRm9ILElBQUk7b0RBQ0ZqRCxpQkFBaUI7b0RBQ2pCSyxjQUFjO29EQUNkdUUsSUFBSTtvREFDSkMsSUFBSTtvREFDSjNFLFVBQVU7b0RBQ1ZELFlBQVk7Z0RBQ2Q7MERBRUMwQyxjQUFjUSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLN0IsOERBQUN0SCwwREFBR0E7b0NBQUNvSCxJQUFJO3dDQUFFQyxXQUFXO3dDQUFTRSxXQUFXO29DQUFPOzhDQUM5Q1Isd0JBQ0MsOERBQUMvRywwREFBR0E7d0NBQUNvSCxJQUFJOzRDQUFFd0IsU0FBUzs0Q0FBUUssZ0JBQWdCOzRDQUFVWCxHQUFHO3dDQUFFO2tEQUN6RCw0RUFBQ2xGLDBIQUFnQkE7NENBQUN0RCxPQUFNOzs7Ozs7Ozs7O2tFQUcxQjs7NENBQ0dnSCxjQUFjUSxNQUFNLEdBQUcsSUFDdEJSLGNBQWNvQyxHQUFHLENBQUMsQ0FBQ0Msa0JBQWtCQyxzQkFDbkMsOERBQUN6SCxrREFBSUE7b0RBRUgwSCxNQUFNLHlCQUE2QyxPQUFwQkYsaUJBQWlCRyxFQUFFO29EQUNsREMsUUFBUTs4REFFUiw0RUFBQ3ZKLDBEQUFHQTt3REFDRndKLFNBQVMsSUFBTXhDLGNBQWNtQyxpQkFBaUJHLEVBQUU7d0RBQ2hEbEMsSUFBSTs0REFDRmtCLEdBQUc7NERBQ0hDLGNBQ0VhLFFBQVF0QyxjQUFjUSxNQUFNLEdBQUcsSUFDM0IsK0JBQ0E7NERBQ04sV0FBVztnRUFDVG5ELGlCQUFpQjtnRUFDakJzRixRQUFROzREQUNWOzREQUNBM0UsWUFBWTs0REFDWndCLFVBQVU7d0RBQ1o7OzBFQUVBLDhEQUFDdEcsMERBQUdBO2dFQUNGb0gsSUFBSTtvRUFBRXdCLFNBQVM7b0VBQVFFLEtBQUs7b0VBQUdELFlBQVk7Z0VBQWE7O2tGQUV4RCw4REFBQzdJLDBEQUFHQTt3RUFDRm9ILElBQUk7NEVBQ0Z4QixPQUFPOzRFQUNQckIsUUFBUTs0RUFDUkMsY0FBYzs0RUFDZG9FLFNBQVM7NEVBQ1RDLFlBQVk7NEVBQ1pJLGdCQUFnQjs0RUFDaEJTLFlBQVk7NEVBQ1ovRSxXQUNFO3dFQUNKO2tGQUVBLDRFQUFDM0QsNE5BQW1CQTs0RUFDbEJvRyxJQUFJO2dGQUFFdEgsT0FBTztnRkFBU3VFLFVBQVU7NEVBQU87Ozs7Ozs7Ozs7O2tGQUczQyw4REFBQ3JFLDBEQUFHQTt3RUFBQ29ILElBQUk7NEVBQUV1QyxNQUFNOzRFQUFHckYsVUFBVTt3RUFBRTs7MEZBQzlCLDhEQUFDakUsaUVBQVVBO2dGQUNUc0ksU0FBUTtnRkFDUnZCLElBQUk7b0ZBQ0YvQyxVQUFVO29GQUNWRCxZQUFZO29GQUNad0YsWUFBWTtvRkFDWjlKLE9BQU87b0ZBQ1ArSixJQUFJO29GQUNKQyxXQUFXO2dGQUNiOzBGQUVDWCxpQkFBaUJZLFlBQVk7Ozs7OzswRkFFaEMsOERBQUMxSixpRUFBVUE7Z0ZBQ1RzSSxTQUFRO2dGQUNSdkIsSUFBSTtvRkFDRi9DLFVBQVU7b0ZBQ1Z2RSxPQUFPO29GQUNQOEksU0FBUztvRkFDVEMsWUFBWTtvRkFDWkMsS0FBSztnRkFDUDs7a0dBRUEsOERBQUN0SCxtRUFBVUE7d0ZBQUM0RixJQUFJOzRGQUFFL0MsVUFBVTt3RkFBTTs7Ozs7O29GQUNqQ3RFLG1FQUFpQkEsQ0FBQ29KLGlCQUFpQmEsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0REFJbERaLFVBQVUsbUJBQ1QsOERBQUNwSiwwREFBR0E7Z0VBQ0ZvSCxJQUFJO29FQUNGZCxVQUFVO29FQUNWMkQsTUFBTTtvRUFDTkMsS0FBSztvRUFDTEMsUUFBUTtvRUFDUnZFLE9BQU87b0VBQ1BwQixjQUFjO2dFQUNoQjs7Ozs7Ozs7Ozs7O21EQTlFRDJFLGlCQUFpQkcsRUFBRTs7OzsrRUFxRjVCLDhEQUFDdEosMERBQUdBO2dEQUNGb0gsSUFBSTtvREFDRndCLFNBQVM7b0RBQ1R3QixlQUFlO29EQUNmdkIsWUFBWTtvREFDWkksZ0JBQWdCO29EQUNoQlgsR0FBRztvREFDSCtCLFdBQVc7Z0RBQ2I7O2tFQUVBLDhEQUFDckosNE5BQW1CQTt3REFDbEJvRyxJQUFJOzREQUFFL0MsVUFBVTs0REFBUXZFLE9BQU87NERBQVErSixJQUFJO3dEQUFFOzs7Ozs7a0VBRS9DLDhEQUFDeEosaUVBQVVBO3dEQUFDc0ksU0FBUTt3REFBS3ZCLElBQUk7NERBQUV0SCxPQUFPOzREQUFRK0osSUFBSTt3REFBRTtrRUFBRzs7Ozs7O2tFQUd2RCw4REFBQ3hKLGlFQUFVQTt3REFBQ3NJLFNBQVE7d0RBQVF2QixJQUFJOzREQUFFdEgsT0FBTzt3REFBTztrRUFBRzs7Ozs7Ozs7Ozs7OzRDQUt0RGdILGNBQWNRLE1BQU0sR0FBRyxtQkFDdEIsOERBQUN0SCwwREFBR0E7Z0RBQ0ZvSCxJQUFJO29EQUNGa0IsR0FBRztvREFDSGdDLFdBQVc7b0RBQ1hsRSxZQUFZO2dEQUNkOzBEQUVBLDRFQUFDekUsa0RBQUlBO29EQUFDMEgsTUFBSztvREFBaUJFLFFBQVE7OERBQ2xDLDRFQUFDbEosaUVBQVVBO3dEQUNUa0ssV0FBVTt3REFDVjVCLFNBQVE7d0RBQ1J2QixJQUFJOzREQUNGd0IsU0FBUzs0REFDVEMsWUFBWTs0REFDWkksZ0JBQWdCOzREQUNoQjVFLFVBQVU7NERBQ1ZELFlBQVk7NERBQ1pvRyxnQkFBZ0I7NERBQ2hCMUssT0FBTzs0REFDUCxXQUFXO2dFQUNUQSxPQUFPO2dFQUNQK0UsV0FBVzs0REFDYjs0REFDQUMsWUFBWTt3REFDZDs7NERBQ0Q7MEVBRUMsOERBQUM3RCx5RUFBZ0JBO2dFQUFDbUcsSUFBSTtvRUFBRS9DLFVBQVU7Z0VBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBV3pEOzs7Ozs7QUFFRjtNQWhQSnNDO0FBbVBTLFNBQVM4RDtRQU9OQyxlQXFCREEsZ0JBQXFCQTs7SUEzQnBDLE1BQU0sRUFBRXhGLElBQUksRUFBRXlGLE9BQU8sRUFBRSxHQUFHcEwsaURBQVVBLENBQUNtRCx5REFBYUE7SUFDbEQsTUFBTSxFQUFFa0ksSUFBSSxFQUFFQyxPQUFPLEVBQUUsR0FBR2xJLHFEQUFPQTtJQUNqQyxNQUFNLEVBQUVtSSxjQUFjLEVBQUUsR0FBR3JJLHVEQUFRQTtJQUNuQyxNQUFNd0IsUUFBUXRFLCtEQUFRQTtJQUN0QixNQUFNb0wsV0FBVzlLLHdFQUFhQSxDQUFDZ0UsTUFBTTRCLFdBQVcsQ0FBQ0csSUFBSSxDQUFDO0lBQ3RELE1BQU0sRUFBRWdGLE1BQU1OLE9BQU8sRUFBRU8sTUFBTSxFQUFFLEdBQUc5SCw0REFBVUE7SUFDNUMsTUFBTStILFVBQVVSLENBQUFBLG9CQUFBQSwrQkFBQUEsZ0JBQUFBLFFBQVNTLElBQUksY0FBYlQsb0NBQUFBLGNBQWV4RCxJQUFJLE1BQUs7SUFDeEMsTUFBTWtFLHdCQUF3QjVMLDZDQUFNQSxDQUFDO0lBRXJDLE1BQU0sQ0FBQzZMLFVBQVVDLFlBQVksR0FBR2hNLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2lNLGtCQUFrQkMsb0JBQW9CLEdBQUdsTSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNzSCxVQUFVNkUsWUFBWSxHQUFHbk0sK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDb00sY0FBY0MsZ0JBQWdCLEdBQUdyTSwrQ0FBUUEsZUFBQyw4REFBQ29CLGtOQUFTQTs7Ozs7SUFDM0QsTUFBTSxDQUFDb0csZUFBZThFLGlCQUFpQixHQUFHdE0sK0NBQVFBLENBQUMsRUFBRTtJQUNyRCxNQUFNLENBQUN1TSxjQUFjQyxnQkFBZ0IsR0FBR3hNLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3lNLGlCQUFpQkMsbUJBQW1CLEdBQUcxTSwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMyTSxrQkFBa0JDLG9CQUFvQixHQUFHNU0sK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDNEUscUJBQXFCaUksdUJBQXVCLEdBQUc3TSwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUM4TSwyQkFBMkJDLDZCQUE2QixHQUFHL00sK0NBQVFBLENBQUM7SUFDM0UsTUFBTSxDQUFDZ04sc0JBQXNCQyx3QkFBd0IsR0FBR2pOLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ2tOLGVBQWVDLGlCQUFpQixHQUFHbk4sK0NBQVFBLENBQUM7SUFHbkQsTUFBTW9OLGFBQWE7SUFDbkIsTUFBTUMsZ0JBQWdCNUIsWUFBWSxDQUFDN0YsT0FBTyxjQUFjO0lBQ3hELE1BQU0rQixnQkFBZ0JwSCxrRUFBZ0JBLENBQUNDLG1EQUFLQTtJQUU1QyxNQUFNOE0sU0FBU2xDLENBQUFBLG9CQUFBQSwrQkFBQUEsaUJBQUFBLFFBQVNTLElBQUksY0FBYlQscUNBQUFBLGVBQWVwQixFQUFFLE1BQUlvQixvQkFBQUEsK0JBQUFBLGlCQUFBQSxRQUFTUyxJQUFJLGNBQWJULHFDQUFBQSxlQUFlbUMsT0FBTztJQUUxRCxNQUFNLEVBQUU5RixPQUFPLEVBQUUrRixLQUFLLEVBQUU5QixJQUFJLEVBQUUsR0FBR25JLHlEQUFRQSxDQUFDRSxnRUFBZ0JBLEVBQUU7UUFDMURnSyxXQUFXO1lBQUVILFFBQVFBLFNBQVNBLFNBQVN6RjtRQUFVO1FBQ2pENkYsTUFBTSxDQUFDSixVQUFVM0IsV0FBVztRQUM1QmdDLGNBQWNULGdCQUFnQixRQUFRO1FBQ3RDVSxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsNkJBQTZCO0lBQy9CO0lBRUEsTUFBTSxFQUFFcEMsTUFBTXFDLGVBQWUsRUFBRXRHLFNBQVN1RyxrQkFBa0IsRUFBRUMsU0FBU0Msa0JBQWtCLEVBQUUsR0FBRzNLLHlEQUFRQSxDQUFDRywyRUFBMkJBLEVBQUU7UUFDaEkrSixXQUFXO1lBQUVILFFBQVFBLFNBQVNBLFNBQVN6RjtRQUFVO1FBQ2pENkYsTUFBTSxDQUFDSixVQUFVM0IsV0FBVztRQUM1QmdDLGNBQWNULGdCQUFnQixRQUFRO1FBQ3RDVSxhQUFhO1FBQ2JDLGFBQWE7SUFDZjtJQUVBLE1BQU0sQ0FBQ00sb0JBQW9CLEdBQUc3Syw0REFBV0EsQ0FBQ00sNEVBQTJCQTtJQUNyRSxNQUFNLENBQUN3SyxjQUFjLEdBQUc5Syw0REFBV0EsQ0FBQ0ssa0ZBQWtDQTtJQUV0RSxNQUFNMEssd0JBQXdCO1FBQzVCLElBQUk7WUFDRixNQUFNQyxRQUFRLElBQUlDLE1BQU07WUFDeEJELE1BQU1FLE1BQU0sR0FBRztZQUNmRixNQUFNRyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxDQUFDQyxJQUFNQyxRQUFRQyxHQUFHLENBQUMsc0NBQXNDRjtRQUM5RSxFQUFFLE9BQU9uQixPQUFPO1lBQ2RvQixRQUFRQyxHQUFHLENBQUMscUNBQXFDckI7UUFDbkQ7SUFDRjtJQUVBLE1BQU1zQiwwQkFBMEIsQ0FBQ0M7UUFDL0IsSUFBSSxrQkFBa0JDLFVBQVVDLGFBQWFDLFVBQVUsS0FBSyxXQUFXO1lBQ3JFLElBQUlELGFBQWEsa0JBQWtCO2dCQUNqQ0UsTUFBTUo7Z0JBQ05LLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLEtBQUs7Z0JBQ0xDLG9CQUFvQjtnQkFDcEJDLFFBQVE7WUFDVjtRQUNGO0lBQ0Y7SUFFQXJQLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxrQkFBa0I2TyxVQUFVQyxhQUFhQyxVQUFVLEtBQUssV0FBVztZQUNyRUQsYUFBYVEsaUJBQWlCO1FBQ2hDO0lBQ0YsR0FBRyxFQUFFO0lBRUx0UCxnREFBU0EsQ0FBQztRQUNSLElBQUl3TCxXQUFXLG1CQUFtQixDQUFDMkIsUUFBUTtZQUN6Q3NCLFFBQVFjLElBQUksQ0FBQyxnREFBZ0R0RSxvQkFBQUEsOEJBQUFBLFFBQVNTLElBQUk7UUFDNUU7UUFDQStDLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CbEQ7UUFDL0JpRCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCekQsb0JBQUFBLDhCQUFBQSxRQUFTUyxJQUFJO1FBQ3pDK0MsUUFBUUMsR0FBRyxDQUFDLFlBQVl2QjtJQUMxQixHQUFHO1FBQUNsQztRQUFTTztRQUFRMkI7S0FBTztJQUU1Qm5OLGdEQUFTQSxDQUFDO1FBQ1J5TyxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLFlBQVlwSDtRQUN4Qm1ILFFBQVFDLEdBQUcsQ0FBQyxVQUFVckI7UUFDdEJvQixRQUFRQyxHQUFHLENBQUMsU0FBU25ELGlCQUFBQSwyQkFBQUEsS0FBTWxFLGFBQWE7UUFDeENvSCxRQUFRQyxHQUFHLENBQUMsWUFBWXZCO1FBRXhCLElBQUksQ0FBQzdGLFdBQVcsQ0FBQytGLFVBQVM5QixpQkFBQUEsMkJBQUFBLEtBQU1sRSxhQUFhLEdBQUU7WUFDN0MsTUFBTW1JLG1CQUFtQmpFLEtBQUtsRSxhQUFhO1lBQzNDLE1BQU1vSSxlQUFlRCxpQkFBaUIzSCxNQUFNO1lBRTVDNEcsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JjO1lBQzNDZixRQUFRQyxHQUFHLENBQUMsVUFBVWU7WUFFdEJ6QyxpQkFBaUI7WUFFakIsSUFDRXlDLGVBQWU5Qyw2QkFDZkEsNEJBQTRCLEdBQzVCO2dCQUNBRCx1QkFBdUI7Z0JBQ3ZCSSx3QkFBd0I0QyxLQUFLQyxHQUFHO2dCQUVoQ3pCO2dCQUVBLElBQUl1QixlQUFlOUMsMkJBQTJCO29CQUM1QyxNQUFNaUQsdUJBQXVCSCxlQUFlOUM7b0JBQzVDLE1BQU1pQyxVQUNKZ0IseUJBQXlCLElBQ3JCLGlDQUNBLFlBQWlDLE9BQXJCQSxzQkFBcUI7b0JBRXZDakIsd0JBQXdCQztvQkFFeEJyQyxtQkFBbUJxQztvQkFDbkJuQyxvQkFBb0I7b0JBQ3BCSixnQkFBZ0I7Z0JBQ2xCO2dCQUVBd0QsV0FBVztvQkFDVG5ELHVCQUF1QjtnQkFDekIsR0FBRztZQUNMO1lBRUFQLGlCQUFpQnFEO1lBQ2pCNUMsNkJBQTZCNkM7WUFDN0JoQixRQUFRQyxHQUFHLENBQUMsa0NBQStDLE9BQWJlO1FBQ2hELE9BQU8sSUFBSXBDLE9BQU87WUFDaEJvQixRQUFRcEIsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsSUFBSUEsTUFBTXlDLGFBQWEsRUFBRTtnQkFDdkJyQixRQUFRcEIsS0FBSyxDQUFDLG1CQUFtQkEsTUFBTXlDLGFBQWEsQ0FBQ3JHLEdBQUcsQ0FBQyxDQUFDK0UsSUFBTUEsRUFBRUksT0FBTztZQUMzRTtZQUNBLElBQUl2QixNQUFNMEMsWUFBWSxFQUFFO2dCQUN0QnRCLFFBQVFwQixLQUFLLENBQUMsa0JBQWtCQSxNQUFNMEMsWUFBWTtZQUNwRDtZQUVBL0MsaUJBQWlCO1lBRWpCVCxtQkFBbUI7WUFDbkJFLG9CQUFvQjtZQUNwQkosZ0JBQWdCO1lBRWhCd0QsV0FBVztnQkFDVDdDLGlCQUFpQjtZQUNuQixHQUFHO1FBQ0wsT0FBTyxJQUFJLENBQUNHLFFBQVE7WUFDbEJzQixRQUFRYyxJQUFJLENBQUM7UUFDZixPQUFPLElBQUksQ0FBQ2pJLFdBQVcsQ0FBQ2lFLE1BQU07WUFDNUJrRCxRQUFRYyxJQUFJLENBQUM7UUFDZjtJQUNGLEdBQUc7UUFBQ2pJO1FBQVMrRjtRQUFPOUI7UUFBTW9CO1FBQTJCUTtLQUFPO0lBRTVELElBQUkzQixXQUFXLFdBQVc7UUFDeEIscUJBQ0UsOERBQUNqTCwwREFBR0E7WUFBQ29ILElBQUk7Z0JBQUV3QixTQUFTO2dCQUFRSyxnQkFBZ0I7Z0JBQVVYLEdBQUc7WUFBRTtzQkFDekQsNEVBQUNsRiwwSEFBZ0JBO2dCQUFDdEQsT0FBTTs7Ozs7Ozs7Ozs7SUFHOUI7SUFFQSxNQUFNMlAsZUFBZSxJQUFNOUUsUUFBUSxDQUFDekY7SUFDcEMsTUFBTXdLLHlCQUF5QixJQUFNcEUsWUFBWSxDQUFDRDtJQUNsRCxNQUFNc0UsY0FBYyxDQUFDQyxRQUFVbkUsWUFBWW1FLE1BQU1DLGFBQWE7SUFDOUQsTUFBTUMsY0FBYyxJQUFNckUsWUFBWTtJQUN0QyxNQUFNc0UsMkJBQTJCO1FBQy9CLE1BQU1DLFVBQVV6RTtRQUNoQkMsb0JBQW9CLENBQUN5RSxPQUFTLENBQUNBO1FBRS9CLElBQUksQ0FBQ0QsV0FBV3BELFFBQVE7WUFDdEIsSUFBSTtnQkFDRixNQUFNYyxjQUFjO29CQUNsQlgsV0FBVzt3QkFBRUg7b0JBQU87Z0JBQ3RCO2dCQUVBWTtnQkFFQVUsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPckIsT0FBTztnQkFDZG9CLFFBQVFwQixLQUFLLENBQUMsd0NBQXdDQTtZQUN4RDtRQUNGO0lBQ0Y7SUFFQSxNQUFNb0QsMEJBQTBCLENBQUNOO1lBQzNCeEU7UUFBSixLQUFJQSxpQ0FBQUEsc0JBQXNCK0UsT0FBTyxjQUE3Qi9FLHFEQUFBQSwrQkFBK0JnRixRQUFRLENBQUNSLE1BQU1TLE1BQU0sR0FBRztRQUMzRDdFLG9CQUFvQjtRQUNwQjhFLHdCQUF3QixPQUFPLDJDQUEyQztJQUM1RTtJQUVBLE1BQU1DLDBCQUEwQixDQUFDeEc7UUFDL0J1Ryx3QkFBd0J2RztJQUMxQjtJQUVBLE1BQU15RyxtQkFBbUI7UUFDdkJGLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU1HLG9CQUFvQixDQUFDeE0sT0FBT3lLO1FBQ2hDN0QsUUFBUTVHO1FBQ1IwSCxnQkFBZ0IrQztRQUNoQm9CO0lBQ0Y7SUFFQSxNQUFNWSxvQkFBb0IsQ0FBQzVRLE9BQU80TztRQUNoQzVELGVBQWVoTDtRQUNmNkwsZ0JBQWdCK0M7UUFDaEJvQjtJQUNGO0lBRUEsTUFBTWEsNEJBQTRCLE9BQU9DO1FBQ3ZDLElBQUksQ0FBQ2hFLFFBQVE7WUFDWFosbUJBQW1CO1lBQ25CRSxvQkFBb0I7WUFDcEJKLGdCQUFnQjtZQUNoQjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU0rRSxXQUFXLE1BQU1wRCxvQkFBb0I7Z0JBQ3pDVixXQUFXO29CQUNUNkQsZ0JBQWdCQTtvQkFDaEJoRSxRQUFRQTtnQkFDVjtZQUNGO1lBRUEsSUFBSWlFLFNBQVM3RixJQUFJLENBQUN5QyxtQkFBbUIsRUFBRTtnQkFDckMsTUFBTXFELHVCQUF1QmhLLGNBQWNpSyxNQUFNLENBQy9DLENBQUNDLElBQU1BLEVBQUUxSCxFQUFFLEtBQUtzSDtnQkFFbEJoRixpQkFBaUJrRjtnQkFDakJ6RSw2QkFBNkJ5RSxxQkFBcUJ4SixNQUFNO2dCQUV4RDBFLG1CQUFtQjtnQkFDbkJFLG9CQUFvQjtnQkFDcEJKLGdCQUFnQjtZQUNsQjtRQUNGLEVBQUUsT0FBT2dCLE9BQU87WUFDZG9CLFFBQVFwQixLQUFLLENBQUMsK0JBQStCQTtZQUM3Q2QsbUJBQW1CO1lBQ25CRSxvQkFBb0I7WUFDcEJKLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEscUJBQ0U7O1lBQ0lsQixDQUFBQSxTQUFTLFdBQVdBLFNBQVMsTUFBSyxtQkFDbEMsOERBQUM3RjtnQkFBT3VCLFVBQVM7Z0JBQVFwQixNQUFNQTswQkFDN0IsNEVBQUMvRSw4REFBT0E7b0JBQ05pSCxJQUFJO3dCQUNGZCxVQUFVO3dCQUNWVixPQUFPO3dCQUNQekIsaUJBQWlCRixNQUFNa0MsT0FBTyxDQUFDQyxVQUFVLENBQUNvQyxNQUFNO3dCQUNoRHJELFFBQVE7d0JBQ1JtRixXQUFXLGFBQXVDLE9BQTFCckcsTUFBTWtDLE9BQU8sQ0FBQ3NDLElBQUksQ0FBQ3dJLE1BQU07d0JBQ2pEMUksY0FBYyxhQUF1QyxPQUExQnRFLE1BQU1rQyxPQUFPLENBQUNzQyxJQUFJLENBQUN3SSxNQUFNO3dCQUNwRCxDQUFDaE4sTUFBTTRCLFdBQVcsQ0FBQ0csSUFBSSxDQUFDLE1BQU0sRUFBRTs0QkFDOUJvRSxlQUFlO3dCQUNqQjtvQkFDRjs7c0NBRUEsOERBQUM5SixpRUFBVUE7NEJBQ1Q0USxNQUFLOzRCQUNMQyxjQUFXOzRCQUNYM0gsU0FBU2lHOzRCQUNUckksSUFBSTtnQ0FBRWdLLGFBQWE7NEJBQU87c0NBRXpCbE0scUJBQ0MsOERBQUN6RCxtREFBS0E7Z0NBQ0o0UCxLQUFJO2dDQUNKQyxLQUFJO2dDQUNKMUwsT0FBTztnQ0FDUHJCLFFBQVE7Ozs7O3FEQUdWLDhEQUFDcEQsaUVBQVFBO2dDQUFDaUcsSUFBSTtvQ0FBRXRILE9BQU9tRSxNQUFNa0MsT0FBTyxDQUFDc0MsSUFBSSxDQUFDQyxLQUFLO2dDQUFDOzs7Ozs7Ozs7OztzQ0FHcEQsOERBQUNySSxpRUFBVUE7NEJBQ1RrSyxXQUFVOzRCQUNWNUIsU0FBUTs0QkFDUjdJLE9BQU07NEJBQ055UixNQUFNOzRCQUNObkssSUFBSTtnQ0FBRW9LLFVBQVU7NEJBQUU7c0NBRWxCLDRFQUFDN1Asa0RBQUlBO2dDQUFDMEgsTUFBSzswQ0FDVCw0RUFBQzVILG1EQUFLQTtvQ0FDSjRQLEtBQUszRTtvQ0FDTDRFLEtBQUk7b0NBQ0p2SyxTQUFRO29DQUNSbkIsT0FBTztvQ0FDUHJCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSWQsOERBQUN2RSwwREFBR0E7NEJBQ0ZvSCxJQUFJO2dDQUNGd0IsU0FBUztnQ0FDVEMsWUFBWTtnQ0FDWkMsS0FBSztvQ0FBRTJJLElBQUk7b0NBQUtDLElBQUk7b0NBQUdDLElBQUk7Z0NBQUk7Z0NBQy9CakksWUFBWTtnQ0FDWixDQUFDekYsTUFBTTRCLFdBQVcsQ0FBQ0csSUFBSSxDQUFDLE1BQU0sRUFBRTtvQ0FDOUJvRSxlQUFlO29DQUNmdEIsS0FBSztnQ0FDUDs0QkFDRjs7OENBRUEsOERBQUM5SSwwREFBR0E7b0NBQ0ZvSCxJQUFJO3dDQUNGZCxVQUFVO3dDQUNWLFlBQVk7NENBQ1ZzTCxTQUFTOzRDQUNUdEwsVUFBVTs0Q0FDVnVMLE9BQU87NENBQ1AzSCxLQUFLOzRDQUNMckYsV0FBVzs0Q0FDWGUsT0FBTzs0Q0FDUHJCLFFBQVE7NENBQ1JKLGlCQUFpQjs0Q0FDakIsQ0FBQ0YsTUFBTTRCLFdBQVcsQ0FBQ0csSUFBSSxDQUFDLE1BQU0sRUFBRTtnREFDOUI0QyxTQUFTOzRDQUNYO3dDQUNGO29DQUNGOzhDQUVBLDRFQUFDdEksaUVBQVVBO3dDQUNUNlEsY0FBVzt3Q0FDWFcsaUJBQWM7d0NBQ2RDLGlCQUFjO3dDQUNkdkksU0FBU21HO3dDQUNUdkksSUFBSTs0Q0FDRnRILE9BQU87NENBQ1BpSSxTQUFTO2dEQUFFMEosSUFBSTtnREFBT0MsSUFBSTs0Q0FBTTs0Q0FDaEMsV0FBVztnREFDVHZOLGlCQUFpQjtnREFDakJVLFdBQVc7NENBQ2I7NENBQ0FDLFlBQVk7d0NBQ2Q7a0RBRUM0Rzs7Ozs7Ozs7Ozs7OENBR0wsOERBQUM5Six5TUFBTUE7b0NBQ0wwSCxJQUFHO29DQUNIcEUsTUFBTThNLFFBQVFwTDtvQ0FDZEEsVUFBVUE7b0NBQ1ZxTCxXQUFVO29DQUNWbk4sVUFBVTtvQ0FDVnNDLElBQUk7d0NBQUVqQyxRQUFRO29DQUFNOzhDQUVuQjs0Q0FBQyxFQUFFNkMsZUFBZSxFQUFFOytDQUNuQiw4REFBQzFGLHdNQUFLQTs0Q0FBRSxHQUFHMEYsZUFBZTs0Q0FBRWtLLFdBQVU7NENBQU9qSyxTQUFTO3NEQUNwRCw0RUFBQ3BHLHdNQUFLQTtnREFDSnVGLElBQUk7b0RBQ0ZoQixZQUFZbkMsTUFBTWtDLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDQyxTQUFTO29EQUM5QzdCLGNBQWM7Z0RBQ2hCOzBEQUVBLDRFQUFDMUMsb05BQWlCQTtvREFBQ3VHLGFBQWF5SDs4REFDOUIsNEVBQUM5TiwyTUFBUUE7d0RBQUNtUSxlQUFlSCxRQUFRcEw7OzBFQUMvQiw4REFBQzdFLDJNQUFRQTtnRUFDUHlILFNBQVMsSUFBTWlILGtCQUFrQix1QkFBUyw4REFBQy9QLGtOQUFTQTswRUFFcEQsNEVBQUNBLGtOQUFTQTs7Ozs7Ozs7OzswRUFFWiw4REFBQ3FCLDJNQUFRQTtnRUFDUHlILFNBQVMsSUFBTWlILGtCQUFrQixzQkFBUSw4REFBQ2hRLGlOQUFRQTswRUFFbEQsNEVBQUNBLGlOQUFRQTs7Ozs7Ozs7OzswRUFFWCw4REFBQ3NCLDJNQUFRQTtnRUFDUHlILFNBQVMsSUFDUGtILGtCQUFrQixzQkFBUSw4REFBQ2xQLG1FQUFVQTt3RUFBQzRGLElBQUk7NEVBQUVnTCxNQUFNbk8sTUFBTWtDLE9BQU8sQ0FBQ2tNLElBQUksQ0FBQ0MsSUFBSTt3RUFBQzs7MEVBRzVFLDRFQUFDOVEsbUVBQVVBO29FQUFDNEYsSUFBSTt3RUFBRWdMLE1BQU1uTyxNQUFNa0MsT0FBTyxDQUFDa00sSUFBSSxDQUFDQyxJQUFJO29FQUFDOzs7Ozs7Ozs7OzswRUFFbEQsOERBQUN2USwyTUFBUUE7Z0VBQ1B5SCxTQUFTLElBQ1BrSCxrQkFBa0IsdUJBQVMsOERBQUNsUCxtRUFBVUE7d0VBQUM0RixJQUFJOzRFQUFFZ0wsTUFBTW5PLE1BQU1rQyxPQUFPLENBQUNvTSxLQUFLLENBQUNELElBQUk7d0VBQUM7OzBFQUc5RSw0RUFBQzlRLG1FQUFVQTtvRUFBQzRGLElBQUk7d0VBQUVnTCxNQUFNbk8sTUFBTWtDLE9BQU8sQ0FBQ29NLEtBQUssQ0FBQ0QsSUFBSTtvRUFBQzs7Ozs7Ozs7Ozs7MEVBRW5ELDhEQUFDdlEsMk1BQVFBO2dFQUNQeUgsU0FBUyxJQUNQa0gsa0JBQWtCLHdCQUFVLDhEQUFDbFAsbUVBQVVBO3dFQUFDNEYsSUFBSTs0RUFBRWdMLE1BQU1uTyxNQUFNa0MsT0FBTyxDQUFDOEssTUFBTSxDQUFDcUIsSUFBSTt3RUFBQzs7MEVBR2hGLDRFQUFDOVEsbUVBQVVBO29FQUFDNEYsSUFBSTt3RUFBRWdMLE1BQU1uTyxNQUFNa0MsT0FBTyxDQUFDOEssTUFBTSxDQUFDcUIsSUFBSTtvRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBS3JEOzs7Ozs7OENBR1gsOERBQUN0UywwREFBR0E7b0NBQUNvSCxJQUFJO3dDQUFFZCxVQUFVO29DQUFXOzhDQUM5Qiw0RUFBQzFCO3dDQUF5QlYscUJBQXFCQTtrREFDN0MsNEVBQUM1RCxpRUFBVUE7NENBQ1RSLE9BQU07NENBQ04wUyxLQUFLcEg7NENBQ0w1QixTQUFTdUc7NENBQ1RvQixjQUFZLFFBQTZCLE9BQXJCckssY0FBY1EsTUFBTSxFQUFDOzRDQUN6Q0YsSUFBSTtnREFDRmQsVUFBVTtnREFDVnhHLE9BQU87Z0RBQ1BpSSxTQUFTO29EQUFFMEosSUFBSTtvREFBT0MsSUFBSTtnREFBTztnREFDakMsV0FBVztvREFDVHZOLGlCQUFpQjtvREFDakJVLFdBQVc7Z0RBQ2I7Z0RBQ0FDLFlBQVk7NENBQ2Q7c0RBRUEsNEVBQUNkO2dEQUNDeU8sY0FBY3BGLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCcUYsd0JBQXdCLEtBQUk7Z0RBQzNEeE8scUJBQXFCQTtnREFDckJ5TyxLQUFLOzBEQUVKN0wsY0FBY1EsTUFBTSxHQUFHLGtCQUN0Qiw4REFBQ3RHLDROQUFtQkE7b0RBQ2xCb0csSUFBSTt3REFDRnRILE9BQU9vRSxzQkFBc0IsWUFBWTt3REFDekNHLFVBQVU7NERBQUVvTixJQUFJOzREQUFRQyxJQUFJO3dEQUFPO3dEQUNuQ1gsUUFBUTdNLHNCQUNKLGdEQUNBO29EQUNOOzs7Ozt5RUFHRiw4REFBQzFELHNOQUFhQTtvREFBQzRHLElBQUk7d0RBQUUvQyxVQUFVOzREQUFFb04sSUFBSTs0REFBUUMsSUFBSTt3REFBTztvREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTXBFLDhEQUFDL0s7b0NBQ0N6QixNQUFNcUc7b0NBQ04zRSxVQUFVd0Usc0JBQXNCK0UsT0FBTztvQ0FDdkN0SixTQUFTcUo7b0NBQ1RwSixlQUFlQTtvQ0FDZkMsU0FBU0E7b0NBQ1RDLGVBQWUySjtvQ0FDZjFKLGVBQWVBO29DQUNmaEQsT0FBT0E7b0NBQ1AyTyxzQkFBc0JBO29DQUN0QkMscUJBQXFCdEM7b0NBQ3JCdUMsY0FBY3RDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNeEIsOERBQUN0SztnQkFDQ3lDLFNBQVNnRTtnQkFDVHpILE1BQU1BO2dCQUNOa0MsSUFBSTtvQkFDRmpDLFFBQVE7b0JBQ1I0TixhQUFhbkksU0FBUyxVQUFVLG9CQUFvQjtnQkFDdEQ7MEJBRUEsNEVBQUM1SywwREFBR0E7b0JBQ0ZvSCxJQUFJO3dCQUNGakQsaUJBQWlCRixNQUFNa0MsT0FBTyxDQUFDQyxVQUFVLENBQUM0TSxPQUFPO3dCQUNqREMsUUFBUTt3QkFDUnpPLGNBQWM7d0JBQ2RELFFBQVE7b0JBQ1Y7O3NDQUVBLDhEQUFDcEUsOERBQU9BOzRCQUNOaUgsSUFBSTtnQ0FDRndCLFNBQVM7Z0NBQ1RDLFlBQVk7Z0NBQ1pxSyxXQUFXO2dDQUNYakssZ0JBQWdCO2dDQUNoQkYsSUFBSTtvQ0FBQztpQ0FBRTs0QkFDVDtzQ0FFQSw0RUFBQ3JGLGlEQUFPQTs7Ozs7Ozs7OztzQ0FFViw4REFBQ3RELDJEQUFJQTs0QkFDSG1LLFdBQVU7NEJBQ1ZuRCxJQUFJO2dDQUNGd0IsU0FBUztnQ0FDVHdCLGVBQWU7Z0NBQ2ZuQixnQkFBZ0I7Z0NBQ2hCMUUsUUFBUTtnQ0FDUjJPLFdBQVc7NEJBQ2I7OzhDQUVBLDhEQUFDbFQsMERBQUdBOztzREFDRiw4REFBQ3dELHNEQUFhQTs7Ozs7d0NBQ2IwSCx5QkFDQzs7OERBQ0UsOERBQUM3SSxpTkFBY0E7b0RBQUNtSCxTQUFTa0c7O3NFQUN2Qiw4REFBQ3ROLCtNQUFZQTtzRUFDWCw0RUFBQ2QscUVBQVlBO2dFQUNYOEYsSUFBSTtvRUFBRXRILE9BQU84SyxTQUFTLFVBQVUsVUFBVTtnRUFBVTs7Ozs7Ozs7Ozs7c0VBR3hELDhEQUFDMUksK01BQVlBOzREQUFDOFEsU0FBUTs7Ozs7O3dEQUNyQjNILHlCQUFXLDhEQUFDakssdUVBQVVBOzs7O2lGQUFNLDhEQUFDQyx1RUFBVUE7Ozs7Ozs7Ozs7OzhEQUUxQyw4REFBQ2MsMk1BQVFBO29EQUFDZ1IsSUFBSTlIO29EQUFVcEQsU0FBUTtvREFBT21MLGFBQWE7OERBQ2xELDRFQUFDaFQsMkRBQUlBO3dEQUFDbUssV0FBVTt3REFBTThJLGNBQWM7OzBFQUNsQyw4REFBQzFSLGtEQUFJQTtnRUFBQzBILE1BQUs7Z0VBQWNFLFFBQVE7MEVBQy9CLDRFQUFDbEgsaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUcrTyxNQUFLOztzRkFDdkMsOERBQUNsUiwrTUFBWUE7c0ZBQ1gsNEVBQUNiLHNFQUFhQTtnRkFDWjZGLElBQUk7b0ZBQUV0SCxPQUFPOEssU0FBUyxVQUFVLFVBQVU7Z0ZBQVU7Ozs7Ozs7Ozs7O3NGQUd4RCw4REFBQzFJLCtNQUFZQTs0RUFBQzhRLFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUcxQiw4REFBQ3JSLGtEQUFJQTtnRUFBQzBILE1BQUs7Z0VBQXVCRSxRQUFROzBFQUN4Qyw0RUFBQ2xILGlOQUFjQTtvRUFBQytFLElBQUk7d0VBQUU3QyxRQUFRO29FQUFHO29FQUFHK08sTUFBSzs7c0ZBQ3ZDLDhEQUFDbFIsK01BQVlBO3NGQUNYLDRFQUFDekIsaU5BQVFBO2dGQUNQeUcsSUFBSTtvRkFBRXRILE9BQU84SyxTQUFTLFVBQVUsVUFBVTtnRkFBVTs7Ozs7Ozs7Ozs7c0ZBR3hELDhEQUFDMUksK01BQVlBOzRFQUFDOFEsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzFCLDhEQUFDclIsa0RBQUlBO2dFQUFDMEgsTUFBSztnRUFBZ0JFLFFBQVE7MEVBQ2pDLDRFQUFDbEgsaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUcrTyxNQUFLOztzRkFDdkMsOERBQUNsUiwrTUFBWUE7c0ZBQ1gsNEVBQUN4QixvTkFBV0E7Z0ZBQ1Z3RyxJQUFJO29GQUFFdEgsT0FBTzhLLFNBQVMsVUFBVSxVQUFVO2dGQUFVOzs7Ozs7Ozs7OztzRkFHeEQsOERBQUMxSSwrTUFBWUE7NEVBQUM4USxTQUFROzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHMUIsOERBQUNyUixrREFBSUE7Z0VBQUMwSCxNQUFLO2dFQUFnQkUsUUFBUTswRUFDakMsNEVBQUNsSCxpTkFBY0E7b0VBQUMrRSxJQUFJO3dFQUFFN0MsUUFBUTtvRUFBRztvRUFBRytPLE1BQUs7O3NGQUN2Qyw4REFBQ2xSLCtNQUFZQTtzRkFDWCw0RUFBQ3ZCLG9OQUFXQTtnRkFDVnVHLElBQUk7b0ZBQUV0SCxPQUFPOEssU0FBUyxVQUFVLFVBQVU7Z0ZBQVU7Ozs7Ozs7Ozs7O3NGQUd4RCw4REFBQzFJLCtNQUFZQTs0RUFBQzhRLFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUcxQiw4REFBQ3JSLGtEQUFJQTtnRUFBQzBILE1BQUs7Z0VBQWNFLFFBQVE7MEVBQy9CLDRFQUFDbEgsaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUcrTyxNQUFLOztzRkFDdkMsOERBQUNsUiwrTUFBWUE7c0ZBQ1gsNEVBQUNyQixtTkFBVUE7Z0ZBQ1RxRyxJQUFJO29GQUFFdEgsT0FBTzhLLFNBQVMsVUFBVSxVQUFVO2dGQUFVOzs7Ozs7Ozs7OztzRkFHeEQsOERBQUMxSSwrTUFBWUE7NEVBQUM4USxTQUFROzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHMUIsOERBQUNyUixrREFBSUE7Z0VBQUMwSCxNQUFLO2dFQUF1QkUsUUFBUTswRUFDeEMsNEVBQUNsSCxpTkFBY0E7b0VBQUMrRSxJQUFJO3dFQUFFN0MsUUFBUTtvRUFBRztvRUFBRytPLE1BQUs7O3NGQUN2Qyw4REFBQ2xSLCtNQUFZQTtzRkFDWCw0RUFBQ3RCLG1PQUEwQkE7Z0ZBQ3pCc0csSUFBSTtvRkFBRXRILE9BQU84SyxTQUFTLFVBQVUsVUFBVTtnRkFBVTs7Ozs7Ozs7Ozs7c0ZBR3hELDhEQUFDMUksK01BQVlBOzRFQUFDOFEsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzFCLDhEQUFDclIsa0RBQUlBO2dFQUFDMEgsTUFBSztnRUFBZ0JFLFFBQVE7MEVBQ2pDLDRFQUFDbEgsaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUcrTyxNQUFLOztzRkFDdkMsOERBQUNsUiwrTUFBWUE7c0ZBQ1gsNEVBQUM3Qiw2TUFBSUE7Z0ZBQ0g2RyxJQUFJO29GQUFFdEgsT0FBTzhLLFNBQVMsVUFBVSxVQUFVO2dGQUFVOzs7Ozs7Ozs7OztzRkFHeEQsOERBQUMxSSwrTUFBWUE7NEVBQUM4USxTQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUXBDLDhEQUFDaFQsMERBQUdBOzhDQUNGLDRFQUFDeUQsMkRBQWtCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUszQiw4REFBQ0osa0hBQVFBO2dCQUNQNkIsTUFBTTJHO2dCQUNOMEgsa0JBQWtCO2dCQUNsQjFNLFNBQVMsSUFBTWlGLGdCQUFnQjswQkFFL0IsNEVBQUN4SSwrR0FBS0E7b0JBQ0prUSxVQUFVdkg7b0JBQ1ZwRixTQUFTLElBQU1pRixnQkFBZ0I7b0JBQy9CMUUsSUFBSTt3QkFBRXhCLE9BQU87b0JBQU87OEJBRW5CbUc7Ozs7Ozs7Ozs7Ozs7QUFLWDtHQXJtQndCdEI7O1FBRUk5SCxpREFBT0E7UUFDTkYsbURBQVFBO1FBQ3JCOUMsMkRBQVFBO1FBQ0xNLG9FQUFhQTtRQUNJa0Qsd0RBQVVBO1FBb0J0QnRELDhEQUFnQkE7UUFJTGdELHFEQUFRQTtRQVNtREEscURBQVFBO1FBUXRFRCx3REFBV0E7UUFDakJBLHdEQUFXQTs7O01BaERiNkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvSGVhZGVyL0hlYWRlci5qcz85NTMyIl0sInNvdXJjZXNDb250ZW50IjpbIlxyXG5pbXBvcnQgeyBncWwgfSBmcm9tIFwiQGFwb2xsby9jbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VDb250ZXh0LCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBzdHlsZWQsIHVzZVRoZW1lIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvc3R5bGVzXCI7XHJcbmltcG9ydCBNdWlEcmF3ZXIgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvRHJhd2VyXCI7XHJcbmltcG9ydCB7XHJcbiAgdXNlU2VsZWN0ZWRDb2xvcixcclxuICBjb2xvcixcclxuICBmb3JtYXREYXRlVGltZVVUQyxcclxufSBmcm9tIFwiLi4vSGVscGVyRnVuY3Rpb25zXCI7XHJcbmltcG9ydCBCb3ggZnJvbSBcIkBtdWkvbWF0ZXJpYWwvQm94XCI7XHJcbmltcG9ydCB1c2VNZWRpYVF1ZXJ5IGZyb20gXCJAbXVpL21hdGVyaWFsL3VzZU1lZGlhUXVlcnlcIjtcclxuaW1wb3J0IE11aUFwcEJhciBmcm9tIFwiQG11aS9tYXRlcmlhbC9BcHBCYXJcIjtcclxuaW1wb3J0IFRvb2xiYXIgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvVG9vbGJhclwiO1xyXG5pbXBvcnQgTGlzdCBmcm9tIFwiQG11aS9tYXRlcmlhbC9MaXN0XCI7XHJcbmltcG9ydCBUeXBvZ3JhcGh5IGZyb20gXCJAbXVpL21hdGVyaWFsL1R5cG9ncmFwaHlcIjtcclxuaW1wb3J0IEljb25CdXR0b24gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvSWNvbkJ1dHRvblwiO1xyXG5pbXBvcnQge1xyXG4gIFRhc2ssXHJcbiAgTm90aWZpY2F0aW9ucyxcclxuICBEYXJrTW9kZSxcclxuICBMaWdodE1vZGUsXHJcbiAgQ2FtcGFpZ24sXHJcbiAgQ2VsZWJyYXRpb24sXHJcbiAgRm9ybWF0UXVvdGUsXHJcbiAgTm90aWZpY2F0aW9uc0FjdGl2ZVJvdW5kZWQsXHJcbiAgTG9jYWxPZmZlcixcclxuICBOb3RpZmljYXRpb25zQWN0aXZlLFxyXG59IGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsXCI7XHJcbmltcG9ydCBBcnJvd0ZvcndhcmRJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0Fycm93Rm9yd2FyZFwiO1xyXG5pbXBvcnQgQXJyb3dCYWNrSWNvbiBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9BcnJvd0JhY2tcIjtcclxuaW1wb3J0IE1lbnVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL01lbnVcIjtcclxuaW1wb3J0IEV4cGFuZExlc3MgZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvRXhwYW5kTGVzc1wiO1xyXG5pbXBvcnQgRXhwYW5kTW9yZSBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9FeHBhbmRNb3JlXCI7XHJcbmltcG9ydCBTZXR0aW5nc0ljb24gZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvU2V0dGluZ3NcIjtcclxuaW1wb3J0IE5ld3NwYXBlckljb24gZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvTmV3c3BhcGVyXCI7XHJcbmltcG9ydCBDaXJjbGVJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0NpcmNsZVwiO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuaW1wb3J0IEJhZGdlIGZyb20gXCJAbXVpL21hdGVyaWFsL0JhZGdlXCI7XHJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuaW1wb3J0IHtcclxuICBQb3BwZXIsXHJcbiAgUGFwZXIsXHJcbiAgQ2xpY2tBd2F5TGlzdGVuZXIsXHJcbiAgTWVudUl0ZW0sXHJcbiAgTWVudUxpc3QsXHJcbiAgTGlzdEl0ZW0sXHJcbiAgTGlzdEl0ZW1UZXh0LFxyXG4gIENvbGxhcHNlLFxyXG4gIExpc3RJdGVtSWNvbixcclxuICBMaXN0SXRlbUJ1dHRvbixcclxuICBTbGlkZSxcclxuICBab29tLFxyXG4gIGtleWZyYW1lcyxcclxufSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VDb2xvciB9IGZyb20gXCIuLi9Db2xvckNvbnRleHRcIjtcclxuaW1wb3J0IHsgRHJhd2VyQ29udGV4dCB9IGZyb20gXCIuL0RyYXdlckNvbnRleHRcIjtcclxuaW1wb3J0IHsgdXNlTW9kZSB9IGZyb20gXCIuLi9Nb2RlQ29udGV4dFwiO1xyXG5pbXBvcnQgeyB1c2VNdXRhdGlvbiwgdXNlUXVlcnkgfSBmcm9tIFwiQGFwb2xsby9jbGllbnRcIjtcclxuaW1wb3J0IHtcclxuICBnZXROZXdOb3RpZmljYXRpb25zLFxyXG4gIGdldE5vdGlmaWNhdGlvbnMsXHJcbiAgZ2V0VW5zZWVuTm90aWZpY2F0aW9uc0NvdW50LFxyXG4gIG11dGF0aW9uTWFya0FsbE5vdGlmaWNhdGlvbnNBc1NlZW5cclxufSBmcm9tIFwiQC9EYXRhL05vdGlmaWNhdGlvblwiO1xyXG5pbXBvcnQgeyBtdXRhdGlvbkFkZE5vdGlmaWNhdGlvblZpZXcgfSBmcm9tIFwiQC9EYXRhL0Fubm91bmNlbWVudFwiO1xyXG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xyXG5pbXBvcnQgeyBDaXJjdWxhclByb2dyZXNzLCBTbmFja2JhciwgQWxlcnQsIERpdmlkZXIgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyBNYWluTGlzdEl0ZW1zLCBTZWNvbmRhcnlMaXN0SXRlbXMgfSBmcm9tIFwiLi4vTGlzdEl0ZW1zXCI7XHJcbmltcG9ydCBQcm9maWxlIGZyb20gXCIuLi9Qcm9maWxlXCI7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L3JvdXRlclwiO1xyXG5cclxuY29uc3QgZHJhd2VyV2lkdGggPSBcIjE3cmVtXCI7XHJcblxyXG4vLyBLZXlmcmFtZXMgZm9yIG5vdGlmaWNhdGlvbiBhbmltYXRpb25zXHJcbmNvbnN0IHB1bHNlID0ga2V5ZnJhbWVzYFxyXG4gIDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgfVxyXG4gIDUwJSB7IHRyYW5zZm9ybTogc2NhbGUoMS4xKTsgfVxyXG4gIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEpOyB9XHJcbmA7XHJcblxyXG5jb25zdCBzaGFrZSA9IGtleWZyYW1lc2BcclxuICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTsgfVxyXG4gIDEwJSwgMzAlLCA1MCUsIDcwJSwgOTAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC0ycHgpOyB9XHJcbiAgMjAlLCA0MCUsIDYwJSwgODAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDJweCk7IH1cclxuYDtcclxuXHJcbmNvbnN0IGdsb3cgPSBrZXlmcmFtZXNgXHJcbiAgMCUgeyBib3gtc2hhZG93OiAwIDAgNXB4ICNmZjQ0NDQ7IH1cclxuICA1MCUgeyBib3gtc2hhZG93OiAwIDAgMjBweCAjZmY0NDQ0LCAwIDAgMzBweCAjZmY0NDQ0OyB9XHJcbiAgMTAwJSB7IGJveC1zaGFkb3c6IDAgMCA1cHggI2ZmNDQ0NDsgfVxyXG5gO1xyXG5cclxuY29uc3QgQW5pbWF0ZWRCYWRnZSA9IHN0eWxlZChCYWRnZSkoKHsgdGhlbWUsIGhhc05ld05vdGlmaWNhdGlvbnMgfSkgPT4gKHtcclxuICBcIiYgLk11aUJhZGdlLWJhZGdlXCI6IHtcclxuICAgIGJhY2tncm91bmRDb2xvcjogXCIjZmY0NDQ0XCIsXHJcbiAgICBjb2xvcjogXCJ3aGl0ZVwiLFxyXG4gICAgZm9udFdlaWdodDogXCJib2xkXCIsXHJcbiAgICBmb250U2l6ZTogXCIxMnB4XCIsXHJcbiAgICBtaW5XaWR0aDogXCIyMHB4XCIsXHJcbiAgICBoZWlnaHQ6IFwiMjBweFwiLFxyXG4gICAgYm9yZGVyUmFkaXVzOiBcIjEwcHhcIixcclxuICAgIGJvcmRlcjogXCIycHggc29saWQgd2hpdGVcIixcclxuICAgIGFuaW1hdGlvbjogaGFzTmV3Tm90aWZpY2F0aW9uc1xyXG4gICAgICA/IGAke3B1bHNlfSAycyBpbmZpbml0ZSwgJHtnbG93fSAycyBpbmZpbml0ZWBcclxuICAgICAgOiBcIm5vbmVcIixcclxuICAgIGJveFNoYWRvdzogXCIwIDJweCA4cHggcmdiYSgyNTUsIDY4LCA2OCwgMC4zKVwiLFxyXG4gIH0sXHJcbn0pKTtcclxuXHJcbmNvbnN0IEFuaW1hdGVkTm90aWZpY2F0aW9uSWNvbiA9IHN0eWxlZChCb3gpKCh7IHRoZW1lLCBoYXNOZXdOb3RpZmljYXRpb25zIH0pID0+ICh7XHJcbiAgYW5pbWF0aW9uOiBoYXNOZXdOb3RpZmljYXRpb25zID8gYCR7c2hha2V9IDAuNXMgZWFzZS1pbi1vdXRgIDogXCJub25lXCIsXHJcbiAgXCImOmhvdmVyXCI6IHtcclxuICAgIHRyYW5zZm9ybTogXCJzY2FsZSgxLjEpXCIsXHJcbiAgICB0cmFuc2l0aW9uOiBcInRyYW5zZm9ybSAwLjJzIGVhc2UtaW4tb3V0XCIsXHJcbiAgfSxcclxufSkpO1xyXG5cclxuY29uc3QgQXBwQmFyID0gc3R5bGVkKE11aUFwcEJhciwge1xyXG4gIHNob3VsZEZvcndhcmRQcm9wOiAocHJvcCkgPT4gcHJvcCAhPT0gXCJvcGVuXCIsXHJcbn0pKCh7IHRoZW1lLCBvcGVuIH0pID0+ICh7XHJcbiAgekluZGV4OiB0aGVtZS56SW5kZXguZHJhd2VyICsgMSxcclxuICB0cmFuc2l0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5jcmVhdGUoW1wid2lkdGhcIiwgXCJtYXJnaW5cIl0sIHtcclxuICAgIGVhc2luZzogdGhlbWUudHJhbnNpdGlvbnMuZWFzaW5nLnNoYXJwLFxyXG4gICAgZHVyYXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmR1cmF0aW9uLmxlYXZpbmdTY3JlZW4sXHJcbiAgfSksXHJcbiAgbWFyZ2luTGVmdDogb3BlbiA/IGRyYXdlcldpZHRoIDogMCxcclxuICB3aWR0aDogb3BlbiA/IGBjYWxjKDEwMCUgLSAke2RyYXdlcldpZHRofSlgIDogXCIxMDAlXCIsXHJcbiAgW3RoZW1lLmJyZWFrcG9pbnRzLnVwKFwic21cIildOiB7XHJcbiAgICBtYXJnaW5MZWZ0OiBvcGVuID8gZHJhd2VyV2lkdGggOiB0aGVtZS5zcGFjaW5nKDkpLFxyXG4gICAgd2lkdGg6IG9wZW5cclxuICAgICAgPyBgY2FsYygxMDAlIC0gJHtkcmF3ZXJXaWR0aH0pYFxyXG4gICAgICA6IGBjYWxjKDEwMCUgLSAke3RoZW1lLnNwYWNpbmcoOSl9KWAsXHJcbiAgfSxcclxuICBbdGhlbWUuYnJlYWtwb2ludHMuZG93bihcInhzXCIpXToge1xyXG4gICAgbWFyZ2luTGVmdDogMCxcclxuICAgIHdpZHRoOiBcIjEwMCVcIixcclxuICB9LFxyXG4gIC4uLihvcGVuICYmIHtcclxuICAgIHRyYW5zaXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmNyZWF0ZShbXCJ3aWR0aFwiLCBcIm1hcmdpblwiXSwge1xyXG4gICAgICBlYXNpbmc6IHRoZW1lLnRyYW5zaXRpb25zLmVhc2luZy5zaGFycCxcclxuICAgICAgZHVyYXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmR1cmF0aW9uLmVudGVyaW5nU2NyZWVuLFxyXG4gICAgfSksXHJcbiAgfSksXHJcbn0pKTtcclxuXHJcbmNvbnN0IERyYXdlciA9IHN0eWxlZChNdWlEcmF3ZXIsIHtcclxuICBzaG91bGRGb3J3YXJkUHJvcDogKHByb3ApID0+IHByb3AgIT09IFwib3BlblwiLFxyXG59KSgoeyB0aGVtZSwgb3BlbiB9KSA9PiAoe1xyXG4gIFwiJiAuTXVpRHJhd2VyLXBhcGVyXCI6IHtcclxuICAgIGJhY2tncm91bmRDb2xvcjogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnNlY29uZGFyeSxcclxuICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXHJcbiAgICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxyXG4gICAgd2lkdGg6IG9wZW4gPyBkcmF3ZXJXaWR0aCA6IHRoZW1lLnNwYWNpbmcoNyksXHJcbiAgICB0cmFuc2l0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5jcmVhdGUoXCJ3aWR0aFwiLCB7XHJcbiAgICAgIGVhc2luZzogdGhlbWUudHJhbnNpdGlvbnMuZWFzaW5nLnNoYXJwLFxyXG4gICAgICBkdXJhdGlvbjogdGhlbWUudHJhbnNpdGlvbnMuZHVyYXRpb24uY29tcGxleCxcclxuICAgIH0pLFxyXG4gICAgYm94U2l6aW5nOiBcImJvcmRlci1ib3hcIixcclxuICAgIC4uLighb3BlbiAmJiB7XHJcbiAgICAgIG92ZXJmbG93WDogXCJoaWRkZW5cIixcclxuICAgICAgdHJhbnNpdGlvbjogdGhlbWUudHJhbnNpdGlvbnMuY3JlYXRlKFwid2lkdGhcIiwge1xyXG4gICAgICAgIGVhc2luZzogdGhlbWUudHJhbnNpdGlvbnMuZWFzaW5nLnNoYXJwLFxyXG4gICAgICAgIGR1cmF0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5kdXJhdGlvbi5sZWF2aW5nU2NyZWVuLFxyXG4gICAgICB9KSxcclxuICAgICAgd2lkdGg6IHRoZW1lLnNwYWNpbmcoNyksXHJcbiAgICAgIFt0aGVtZS5icmVha3BvaW50cy51cChcInNtXCIpXToge1xyXG4gICAgICAgIHdpZHRoOiB0aGVtZS5zcGFjaW5nKDkpLFxyXG4gICAgICB9LFxyXG4gICAgICBbdGhlbWUuYnJlYWtwb2ludHMuZG93bihcInhzXCIpXToge1xyXG4gICAgICAgIHdpZHRoOiBcIjEwMCVcIixcclxuICAgICAgfSxcclxuICAgIH0pLFxyXG4gIH0sXHJcbn0pKTtcclxuXHJcbi8vIEVuaGFuY2VkIFNvY2lhbCBNZWRpYSBTdHlsZSBOb3RpZmljYXRpb24gUG9wcGVyXHJcbmNvbnN0IFNvY2lhbE5vdGlmaWNhdGlvblBvcHBlciA9ICh7XHJcbiAgb3BlbixcclxuICBhbmNob3JFbCxcclxuICBvbkNsb3NlLFxyXG4gIG5vdGlmaWNhdGlvbnMsXHJcbiAgbG9hZGluZyxcclxuICByZW1vdmVIYW5kbGVyLFxyXG4gIHNlbGVjdGVkQ29sb3IsXHJcbiAgdGhlbWUsXHJcbn0pID0+IChcclxuICA8UG9wcGVyXHJcbiAgICBvcGVuPXtvcGVufVxyXG4gICAgYW5jaG9yRWw9e2FuY2hvckVsfVxyXG4gICAgcm9sZT17dW5kZWZpbmVkfVxyXG4gICAgdHJhbnNpdGlvblxyXG4gICAgc3g9e3tcclxuICAgICAgbWF4SGVpZ2h0OiBub3RpZmljYXRpb25zLmxlbmd0aCA+IDQgPyBcIjcwdmhcIiA6IFwiYXV0b1wiLFxyXG4gICAgICBvdmVyZmxvd1k6IG5vdGlmaWNhdGlvbnMubGVuZ3RoID4gNCA/IFwiYXV0b1wiIDogXCJ2aXNpYmxlXCIsXHJcbiAgICAgIHpJbmRleDogOTk5OSxcclxuICAgICAgd2lkdGg6IFwiNDAwcHhcIixcclxuICAgICAgbWF4V2lkdGg6IFwiOTB2d1wiLFxyXG4gICAgfX1cclxuICAgIGRpc2FibGVQb3J0YWxcclxuICAgIHBvcHBlck9wdGlvbnM9e3tcclxuICAgICAgbW9kaWZpZXJzOiBbXHJcbiAgICAgICAgeyBuYW1lOiBcIm9mZnNldFwiLCBvcHRpb25zOiB7IG9mZnNldDogWzAsIDE1XSB9IH0sXHJcbiAgICAgICAgeyBuYW1lOiBcInByZXZlbnRPdmVyZmxvd1wiLCBvcHRpb25zOiB7IHBhZGRpbmc6IDIwIH0gfSxcclxuICAgICAgXSxcclxuICAgIH19XHJcbiAgPlxyXG4gICAgeyh7IFRyYW5zaXRpb25Qcm9wcyB9KSA9PiAoXHJcbiAgICAgIDxab29tIHsuLi5UcmFuc2l0aW9uUHJvcHN9IHRpbWVvdXQ9ezMwMH0+XHJcbiAgICAgICAgPFBhcGVyXHJcbiAgICAgICAgICBlbGV2YXRpb249ezI0fVxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjE2cHhcIixcclxuICAgICAgICAgICAgb3ZlcmZsb3c6IFwiaGlkZGVuXCIsXHJcbiAgICAgICAgICAgIGJvcmRlcjogXCIxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpXCIsXHJcbiAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiBcImJsdXIoMTBweClcIixcclxuICAgICAgICAgICAgYmFja2dyb3VuZDpcclxuICAgICAgICAgICAgICBcImxpbmVhci1ncmFkaWVudCgxNDVkZWcsIHJnYmEoMjU1LDI1NSwyNTUsMC45NSkgMCUsIHJnYmEoMjU1LDI1NSwyNTUsMC45KSAxMDAlKVwiLFxyXG4gICAgICAgICAgICBib3hTaGFkb3c6XHJcbiAgICAgICAgICAgICAgXCIwIDIwcHggNDBweCByZ2JhKDAsMCwwLDAuMTUpLCAwIDAgMCAxcHggcmdiYSgyNTUsMjU1LDI1NSwwLjEpXCIsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxDbGlja0F3YXlMaXN0ZW5lciBvbkNsaWNrQXdheT17b25DbG9zZX0+XHJcbiAgICAgICAgICAgIDxCb3ggc3g9e3sgbWF4V2lkdGg6IFwiNDAwcHhcIiwgbWluV2lkdGg6IFwiMzIwcHhcIiB9fT5cclxuICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICBwOiAyLFxyXG4gICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b206IFwiMXB4IHNvbGlkIHJnYmEoMCwwLDAsMC4xKVwiLFxyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuaGVhZGVyLFxyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogdGhlbWUucGFsZXR0ZS50ZXh0LndoaXRlLFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDZcIlxyXG4gICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxNnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICBnYXA6IDEsXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxOb3RpZmljYXRpb25zQWN0aXZlIHN4PXt7IGZvbnRTaXplOiBcIjIwcHhcIiB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICBOb3RpZmljYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25zLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDI1NSwyNTUsMjU1LDAuMilcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgcHg6IDEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHB5OiAwLjUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogXCJib2xkXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25zLmxlbmd0aH1cclxuICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICA8Qm94IHN4PXt7IG1heEhlaWdodDogXCI0MDBweFwiLCBvdmVyZmxvd1k6IFwiYXV0b1wiIH19PlxyXG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogXCJmbGV4XCIsIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLCBwOiA0IH19PlxyXG4gICAgICAgICAgICAgICAgICAgIDxDaXJjdWxhclByb2dyZXNzIGNvbG9yPVwicHJpbWFyeVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgbm90aWZpY2F0aW9ucy5tYXAoKG5vdGlmaWNhdGlvbkRhdGEsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtub3RpZmljYXRpb25EYXRhLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2Avbm90aWZpY2F0aW9uLWRldGFpbHMvJHtub3RpZmljYXRpb25EYXRhLmlkfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGFzc0hyZWZcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUhhbmRsZXIobm90aWZpY2F0aW9uRGF0YS5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b206XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggPCBub3RpZmljYXRpb25zLmxlbmd0aCAtIDFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCIxcHggc29saWQgcmdiYSgwLDAsMCwwLjA1KVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIiY6aG92ZXJcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDUpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBcInBvaW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4ycyBlYXNlLWluLW91dFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGRpc3BsYXk6IFwiZmxleFwiLCBnYXA6IDIsIGFsaWduSXRlbXM6IFwiZmxleC1zdGFydFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDQwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiA0MCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCI1MCVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmxleFNocmluazogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCIwIDRweCAxMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IFwid2hpdGVcIiwgZm9udFNpemU6IFwiMjBweFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZmxleDogMSwgbWluV2lkdGg6IDAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogXCIyMHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiMzMzNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWI6IDAuNSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd29yZEJyZWFrOiBcImJyZWFrLXdvcmRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbkRhdGEubm90aWZpY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNhcHRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjNjY2XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuNSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmNsZUljb24gc3g9e3sgZm9udFNpemU6IFwiNHB4XCIgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlVGltZVVUQyhub3RpZmljYXRpb25EYXRhLmNyZWF0ZWRBdCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2luZGV4ID09PSAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvcDogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvdHRvbTogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiBcIjNweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjAgMnB4IDJweCAwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiBcImNvbHVtblwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcDogNCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QWxpZ246IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxOb3RpZmljYXRpb25zQWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgZm9udFNpemU6IFwiNDhweFwiLCBjb2xvcjogXCIjY2NjXCIsIG1iOiAyIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiIHN4PXt7IGNvbG9yOiBcIiM2NjZcIiwgbWI6IDEgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgTm8gbmV3IG5vdGlmaWNhdGlvbnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17eyBjb2xvcjogXCIjOTk5XCIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgWW91JmFwb3M7cmUgYWxsIGNhdWdodCB1cCFcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcDogXCIxcHggc29saWQgcmdiYSgwLDAsMCwwLjEpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogXCJyZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMDIpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbm90aWZpY2F0aW9uc1wiIHBhc3NIcmVmPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wb25lbnQ9XCJhXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE0cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogNjAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogXCJub25lXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM2NjdlZWFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCImOmhvdmVyXCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjNzY0YmEyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBcInRyYW5zbGF0ZVgoMnB4KVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAwLjJzIGVhc2UtaW4tb3V0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgQWxsIE5vdGlmaWNhdGlvbnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd0ZvcndhcmRJY29uIHN4PXt7IGZvbnRTaXplOiBcIjE2cHhcIiB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgIDwvQ2xpY2tBd2F5TGlzdGVuZXI+XHJcbiAgICAgICAgPC9QYXBlcj5cclxuICAgICAgPC9ab29tPlxyXG4gICAgKX1cclxuICA8L1BvcHBlcj5cclxuKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWRlcigpIHtcclxuICBjb25zdCB7IG9wZW4sIHNldE9wZW4gfSA9IHVzZUNvbnRleHQoRHJhd2VyQ29udGV4dCk7XHJcbiAgY29uc3QgeyBtb2RlLCBzZXRNb2RlIH0gPSB1c2VNb2RlKCk7XHJcbiAgY29uc3QgeyBzZXRHbG9iYWxDb2xvciB9ID0gdXNlQ29sb3IoKTtcclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcbiAgY29uc3QgaXNNb2JpbGUgPSB1c2VNZWRpYVF1ZXJ5KHRoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJzbVwiKSk7XHJcbiAgY29uc3QgeyBkYXRhOiBzZXNzaW9uLCBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKTtcclxuICBjb25zdCBpc0FkbWluID0gc2Vzc2lvbj8udXNlcj8ucm9sZSA9PT0gXCJBRE1JTlwiO1xyXG4gIGNvbnN0IG5vdGlmaWNhdGlvbkFuY2hvclJlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgY29uc3QgW21vcmVJdGVtLCBzZXRNb3JlSXRlbV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW25vdGlmaWNhdGlvbk9wZW4sIHNldE5vdGlmaWNhdGlvbk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFthbmNob3JFbCwgc2V0QW5jaG9yRWxdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW3NlbGVjdGVkSWNvbiwgc2V0U2VsZWN0ZWRJY29uXSA9IHVzZVN0YXRlKDxMaWdodE1vZGUgLz4pO1xyXG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbc25hY2tiYXJPcGVuLCBzZXRTbmFja2Jhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzbmFja2Jhck1lc3NhZ2UsIHNldFNuYWNrYmFyTWVzc2FnZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc25hY2tiYXJTZXZlcml0eSwgc2V0U25hY2tiYXJTZXZlcml0eV0gPSB1c2VTdGF0ZShcInN1Y2Nlc3NcIik7XHJcbiAgY29uc3QgW2hhc05ld05vdGlmaWNhdGlvbnMsIHNldEhhc05ld05vdGlmaWNhdGlvbnNdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50LCBzZXRQcmV2aW91c05vdGlmaWNhdGlvbkNvdW50XSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFtsYXN0Tm90aWZpY2F0aW9uVGltZSwgc2V0TGFzdE5vdGlmaWNhdGlvblRpbWVdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2VuYWJsZVBvbGxpbmcsIHNldEVuYWJsZVBvbGxpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcblxyXG5cclxuICBjb25zdCBsb2dvU291cmNlID0gXCIvSGFzc2FuYUxvZ29ELnBuZ1wiO1xyXG4gIGNvbnN0IGRyYXdlclZhcmlhbnQgPSBpc01vYmlsZSAmJiAhb3BlbiA/IFwidGVtcG9yYXJ5XCIgOiBcInBlcm1hbmVudFwiO1xyXG4gIGNvbnN0IHNlbGVjdGVkQ29sb3IgPSB1c2VTZWxlY3RlZENvbG9yKGNvbG9yKTtcclxuXHJcbiAgY29uc3QgdXNlcklkID0gc2Vzc2lvbj8udXNlcj8uaWQgfHwgc2Vzc2lvbj8udXNlcj8udXNlcl9pZDtcclxuXHJcbiAgY29uc3QgeyBsb2FkaW5nLCBlcnJvciwgZGF0YSB9ID0gdXNlUXVlcnkoZ2V0Tm90aWZpY2F0aW9ucywge1xyXG4gICAgdmFyaWFibGVzOiB7IHVzZXJJZDogdXNlcklkID8gdXNlcklkIDogdW5kZWZpbmVkIH0sXHJcbiAgICBza2lwOiAhdXNlcklkIHx8IHN0YXR1cyAhPT0gXCJhdXRoZW50aWNhdGVkXCIsXHJcbiAgICBwb2xsSW50ZXJ2YWw6IGVuYWJsZVBvbGxpbmcgPyAzMDAwMCA6IDAsXHJcbiAgICBmZXRjaFBvbGljeTogXCJjYWNoZS1hbmQtbmV0d29ya1wiLFxyXG4gICAgZXJyb3JQb2xpY3k6IFwiYWxsXCIsXHJcbiAgICBub3RpZnlPbk5ldHdvcmtTdGF0dXNDaGFuZ2U6IHRydWUsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IHsgZGF0YTogdW5zZWVuQ291bnREYXRhLCBsb2FkaW5nOiB1bnNlZW5Db3VudExvYWRpbmcsIHJlZmV0Y2g6IHJlZmV0Y2hVbnNlZW5Db3VudCB9ID0gdXNlUXVlcnkoZ2V0VW5zZWVuTm90aWZpY2F0aW9uc0NvdW50LCB7XHJcbiAgICB2YXJpYWJsZXM6IHsgdXNlcklkOiB1c2VySWQgPyB1c2VySWQgOiB1bmRlZmluZWQgfSxcclxuICAgIHNraXA6ICF1c2VySWQgfHwgc3RhdHVzICE9PSBcImF1dGhlbnRpY2F0ZWRcIixcclxuICAgIHBvbGxJbnRlcnZhbDogZW5hYmxlUG9sbGluZyA/IDEwMDAwIDogMCxcclxuICAgIGZldGNoUG9saWN5OiBcImNhY2hlLWFuZC1uZXR3b3JrXCIsXHJcbiAgICBlcnJvclBvbGljeTogXCJhbGxcIixcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2FkZE5vdGlmaWNhdGlvblZpZXddID0gdXNlTXV0YXRpb24obXV0YXRpb25BZGROb3RpZmljYXRpb25WaWV3KTtcclxuICBjb25zdCBbbWFya0FsbEFzU2Vlbl0gPSB1c2VNdXRhdGlvbihtdXRhdGlvbk1hcmtBbGxOb3RpZmljYXRpb25zQXNTZWVuKTtcclxuXHJcbiAgY29uc3QgcGxheU5vdGlmaWNhdGlvblNvdW5kID0gKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgYXVkaW8gPSBuZXcgQXVkaW8oXCIvc291bmRzL25vdGlmaWNhdGlvbi5tcDNcIik7XHJcbiAgICAgIGF1ZGlvLnZvbHVtZSA9IDAuNTtcclxuICAgICAgYXVkaW8ucGxheSgpLmNhdGNoKChlKSA9PiBjb25zb2xlLmxvZyhcIkNvdWxkIG5vdCBwbGF5IG5vdGlmaWNhdGlvbiBzb3VuZDpcIiwgZSkpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5sb2coXCJOb3RpZmljYXRpb24gc291bmQgbm90IGF2YWlsYWJsZTpcIiwgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNob3dCcm93c2VyTm90aWZpY2F0aW9uID0gKG1lc3NhZ2UpID0+IHtcclxuICAgIGlmIChcIk5vdGlmaWNhdGlvblwiIGluIHdpbmRvdyAmJiBOb3RpZmljYXRpb24ucGVybWlzc2lvbiA9PT0gXCJncmFudGVkXCIpIHtcclxuICAgICAgbmV3IE5vdGlmaWNhdGlvbihcIkhhc3NhbmEgUG9ydGFsXCIsIHtcclxuICAgICAgICBib2R5OiBtZXNzYWdlLFxyXG4gICAgICAgIGljb246IFwiL2Zhdmljb24uaWNvXCIsXHJcbiAgICAgICAgYmFkZ2U6IFwiL2Zhdmljb24uaWNvXCIsXHJcbiAgICAgICAgdGFnOiBcImhhc3NhbmEtbm90aWZpY2F0aW9uXCIsXHJcbiAgICAgICAgcmVxdWlyZUludGVyYWN0aW9uOiBmYWxzZSxcclxuICAgICAgICBzaWxlbnQ6IGZhbHNlLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKFwiTm90aWZpY2F0aW9uXCIgaW4gd2luZG93ICYmIE5vdGlmaWNhdGlvbi5wZXJtaXNzaW9uID09PSBcImRlZmF1bHRcIikge1xyXG4gICAgICBOb3RpZmljYXRpb24ucmVxdWVzdFBlcm1pc3Npb24oKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc3RhdHVzID09PSBcImF1dGhlbnRpY2F0ZWRcIiAmJiAhdXNlcklkKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybihcIlVzZXIgSUQgaXMgbWlzc2luZyBpbiBhdXRoZW50aWNhdGVkIHNlc3Npb246XCIsIHNlc3Npb24/LnVzZXIpO1xyXG4gICAgfVxyXG4gICAgY29uc29sZS5sb2coXCI9PT0gU2Vzc2lvbiBEZWJ1ZyA9PT1cIik7XHJcbiAgICBjb25zb2xlLmxvZyhcIlNlc3Npb24gU3RhdHVzOlwiLCBzdGF0dXMpO1xyXG4gICAgY29uc29sZS5sb2coXCJVc2VyIE9iamVjdDpcIiwgc2Vzc2lvbj8udXNlcik7XHJcbiAgICBjb25zb2xlLmxvZyhcIlVzZXIgSUQ6XCIsIHVzZXJJZCk7XHJcbiAgfSwgW3Nlc3Npb24sIHN0YXR1cywgdXNlcklkXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZyhcIj09PSBOb3RpZmljYXRpb24gQmFja2VuZCBEZWJ1ZyA9PT1cIik7XHJcbiAgICBjb25zb2xlLmxvZyhcIkxvYWRpbmc6XCIsIGxvYWRpbmcpO1xyXG4gICAgY29uc29sZS5sb2coXCJFcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgY29uc29sZS5sb2coXCJEYXRhOlwiLCBkYXRhPy5ub3RpZmljYXRpb25zKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVXNlciBJRDpcIiwgdXNlcklkKTtcclxuXHJcbiAgICBpZiAoIWxvYWRpbmcgJiYgIWVycm9yICYmIGRhdGE/Lm5vdGlmaWNhdGlvbnMpIHtcclxuICAgICAgY29uc3QgYWxsTm90aWZpY2F0aW9ucyA9IGRhdGEubm90aWZpY2F0aW9ucztcclxuICAgICAgY29uc3QgY3VycmVudENvdW50ID0gYWxsTm90aWZpY2F0aW9ucy5sZW5ndGg7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkJhY2tlbmQgQ29ubmVjdGVkIFN1Y2Nlc3NmdWxseSFcIik7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQWxsIG5vdGlmaWNhdGlvbnMgcmVjZWl2ZWQ6XCIsIGFsbE5vdGlmaWNhdGlvbnMpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkNvdW50OlwiLCBjdXJyZW50Q291bnQpO1xyXG5cclxuICAgICAgc2V0RW5hYmxlUG9sbGluZyh0cnVlKTtcclxuXHJcbiAgICAgIGlmIChcclxuICAgICAgICBjdXJyZW50Q291bnQgPiBwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50ICYmXHJcbiAgICAgICAgcHJldmlvdXNOb3RpZmljYXRpb25Db3VudCA+IDBcclxuICAgICAgKSB7XHJcbiAgICAgICAgc2V0SGFzTmV3Tm90aWZpY2F0aW9ucyh0cnVlKTtcclxuICAgICAgICBzZXRMYXN0Tm90aWZpY2F0aW9uVGltZShEYXRlLm5vdygpKTtcclxuXHJcbiAgICAgICAgcGxheU5vdGlmaWNhdGlvblNvdW5kKCk7XHJcblxyXG4gICAgICAgIGlmIChjdXJyZW50Q291bnQgPiBwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50KSB7XHJcbiAgICAgICAgICBjb25zdCBuZXdOb3RpZmljYXRpb25Db3VudCA9IGN1cnJlbnRDb3VudCAtIHByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQ7XHJcbiAgICAgICAgICBjb25zdCBtZXNzYWdlID1cclxuICAgICAgICAgICAgbmV3Tm90aWZpY2F0aW9uQ291bnQgPT09IDFcclxuICAgICAgICAgICAgICA/IFwiWW91IGhhdmUgYSBuZXcgbm90aWZpY2F0aW9uIVwiXHJcbiAgICAgICAgICAgICAgOiBgWW91IGhhdmUgJHtuZXdOb3RpZmljYXRpb25Db3VudH0gbmV3IG5vdGlmaWNhdGlvbnMhYDtcclxuXHJcbiAgICAgICAgICBzaG93QnJvd3Nlck5vdGlmaWNhdGlvbihtZXNzYWdlKTtcclxuXHJcbiAgICAgICAgICBzZXRTbmFja2Jhck1lc3NhZ2UobWVzc2FnZSk7XHJcbiAgICAgICAgICBzZXRTbmFja2JhclNldmVyaXR5KFwiaW5mb1wiKTtcclxuICAgICAgICAgIHNldFNuYWNrYmFyT3Blbih0cnVlKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgc2V0SGFzTmV3Tm90aWZpY2F0aW9ucyhmYWxzZSk7XHJcbiAgICAgICAgfSwgMTAwMCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMoYWxsTm90aWZpY2F0aW9ucyk7XHJcbiAgICAgIHNldFByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQoY3VycmVudENvdW50KTtcclxuICAgICAgY29uc29sZS5sb2coYE5vdGlmaWNhdGlvbiBjb3VudCB1cGRhdGVkIHRvOiAke2N1cnJlbnRDb3VudH1gKTtcclxuICAgIH0gZWxzZSBpZiAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkJhY2tlbmQgQ29ubmVjdGlvbiBFcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgICBpZiAoZXJyb3IuZ3JhcGhRTEVycm9ycykge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJHcmFwaFFMIEVycm9yczpcIiwgZXJyb3IuZ3JhcGhRTEVycm9ycy5tYXAoKGUpID0+IGUubWVzc2FnZSkpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChlcnJvci5uZXR3b3JrRXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiTmV0d29yayBFcnJvcjpcIiwgZXJyb3IubmV0d29ya0Vycm9yKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc2V0RW5hYmxlUG9sbGluZyhmYWxzZSk7XHJcblxyXG4gICAgICBzZXRTbmFja2Jhck1lc3NhZ2UoXCJGYWlsZWQgdG8gbG9hZCBub3RpZmljYXRpb25zLiBSZXRyeWluZyBpbiAzMCBzZWNvbmRzLi4uXCIpO1xyXG4gICAgICBzZXRTbmFja2JhclNldmVyaXR5KFwiZXJyb3JcIik7XHJcbiAgICAgIHNldFNuYWNrYmFyT3Blbih0cnVlKTtcclxuXHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNldEVuYWJsZVBvbGxpbmcodHJ1ZSk7XHJcbiAgICAgIH0sIDMwMDAwKTtcclxuICAgIH0gZWxzZSBpZiAoIXVzZXJJZCkge1xyXG4gICAgICBjb25zb2xlLndhcm4oXCJObyB1c2VyIElEIGZvdW5kIGluIHNlc3Npb25cIik7XHJcbiAgICB9IGVsc2UgaWYgKCFsb2FkaW5nICYmICFkYXRhKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybihcIk5vIGRhdGEgcmVjZWl2ZWQgZnJvbSBiYWNrZW5kXCIpO1xyXG4gICAgfVxyXG4gIH0sIFtsb2FkaW5nLCBlcnJvciwgZGF0YSwgcHJldmlvdXNOb3RpZmljYXRpb25Db3VudCwgdXNlcklkXSk7XHJcblxyXG4gIGlmIChzdGF0dXMgPT09IFwibG9hZGluZ1wiKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6IFwiZmxleFwiLCBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIiwgcDogNCB9fT5cclxuICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyBjb2xvcj1cInByaW1hcnlcIiAvPlxyXG4gICAgICA8L0JveD5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBjb25zdCB0b2dnbGVEcmF3ZXIgPSAoKSA9PiBzZXRPcGVuKCFvcGVuKTtcclxuICBjb25zdCBoYW5kbGVTZXRNb3JlSXRlbUNsaWNrID0gKCkgPT4gc2V0TW9yZUl0ZW0oIW1vcmVJdGVtKTtcclxuICBjb25zdCBoYW5kbGVDbGljayA9IChldmVudCkgPT4gc2V0QW5jaG9yRWwoZXZlbnQuY3VycmVudFRhcmdldCk7XHJcbiAgY29uc3QgaGFuZGxlQ2xvc2UgPSAoKSA9PiBzZXRBbmNob3JFbChudWxsKTtcclxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25Ub2dnbGUgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB3YXNPcGVuID0gbm90aWZpY2F0aW9uT3BlbjtcclxuICAgIHNldE5vdGlmaWNhdGlvbk9wZW4oKHByZXYpID0+ICFwcmV2KTtcclxuXHJcbiAgICBpZiAoIXdhc09wZW4gJiYgdXNlcklkKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgYXdhaXQgbWFya0FsbEFzU2Vlbih7XHJcbiAgICAgICAgICB2YXJpYWJsZXM6IHsgdXNlcklkIH1cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgcmVmZXRjaFVuc2VlbkNvdW50KCk7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiQWxsIG5vdGlmaWNhdGlvbnMgbWFya2VkIGFzIHNlZW5cIik7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIG1hcmtpbmcgbm90aWZpY2F0aW9ucyBhcyBzZWVuOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25DbG9zZSA9IChldmVudCkgPT4ge1xyXG4gICAgaWYgKG5vdGlmaWNhdGlvbkFuY2hvclJlZi5jdXJyZW50Py5jb250YWlucyhldmVudC50YXJnZXQpKSByZXR1cm47XHJcbiAgICBzZXROb3RpZmljYXRpb25PcGVuKGZhbHNlKTtcclxuICAgIHNldFNlbGVjdGVkTm90aWZpY2F0aW9uKG51bGwpOyAvLyBSZXNldCBzZWxlY3RlZCBub3RpZmljYXRpb24gd2hlbiBjbG9zaW5nXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2sgPSAobm90aWZpY2F0aW9uKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZE5vdGlmaWNhdGlvbihub3RpZmljYXRpb24pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJhY2tUb0xpc3QgPSAoKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZE5vdGlmaWNhdGlvbihudWxsKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVUaGVtZUNoYW5nZSA9ICh0aGVtZSwgaWNvbikgPT4ge1xyXG4gICAgc2V0TW9kZSh0aGVtZSk7XHJcbiAgICBzZXRTZWxlY3RlZEljb24oaWNvbik7XHJcbiAgICBoYW5kbGVDbG9zZSgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNvbG9yQ2hhbmdlID0gKGNvbG9yLCBpY29uKSA9PiB7XHJcbiAgICBzZXRHbG9iYWxDb2xvcihjb2xvcik7XHJcbiAgICBzZXRTZWxlY3RlZEljb24oaWNvbik7XHJcbiAgICBoYW5kbGVDbG9zZSgpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHJlbW92ZUFubm91bmNlbWVudEhhbmRsZXIgPSBhc3luYyAobm90aWZpY2F0aW9uSWQpID0+IHtcclxuICAgIGlmICghdXNlcklkKSB7XHJcbiAgICAgIHNldFNuYWNrYmFyTWVzc2FnZShcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWRcIik7XHJcbiAgICAgIHNldFNuYWNrYmFyU2V2ZXJpdHkoXCJlcnJvclwiKTtcclxuICAgICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhZGROb3RpZmljYXRpb25WaWV3KHtcclxuICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbklkOiBub3RpZmljYXRpb25JZCxcclxuICAgICAgICAgIHVzZXJJZDogdXNlcklkLFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuYWRkTm90aWZpY2F0aW9uVmlldykge1xyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWROb3RpZmljYXRpb25zID0gbm90aWZpY2F0aW9ucy5maWx0ZXIoXHJcbiAgICAgICAgICAobikgPT4gbi5pZCAhPT0gbm90aWZpY2F0aW9uSWRcclxuICAgICAgICApO1xyXG4gICAgICAgIHNldE5vdGlmaWNhdGlvbnModXBkYXRlZE5vdGlmaWNhdGlvbnMpO1xyXG4gICAgICAgIHNldFByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQodXBkYXRlZE5vdGlmaWNhdGlvbnMubGVuZ3RoKTtcclxuXHJcbiAgICAgICAgc2V0U25hY2tiYXJNZXNzYWdlKFwiTm90aWZpY2F0aW9uIG1hcmtlZCBhcyB2aWV3ZWRcIik7XHJcbiAgICAgICAgc2V0U25hY2tiYXJTZXZlcml0eShcInN1Y2Nlc3NcIik7XHJcbiAgICAgICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbWFya2luZyBub3RpZmljYXRpb246XCIsIGVycm9yKTtcclxuICAgICAgc2V0U25hY2tiYXJNZXNzYWdlKFwiRmFpbGVkIHRvIG1hcmsgbm90aWZpY2F0aW9uXCIpO1xyXG4gICAgICBzZXRTbmFja2JhclNldmVyaXR5KFwiZXJyb3JcIik7XHJcbiAgICAgIHNldFNuYWNrYmFyT3Blbih0cnVlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgeyhtb2RlID09PSBcImxpZ2h0XCIgfHwgbW9kZSA9PT0gXCJkYXJrXCIpICYmIChcclxuICAgICAgICA8QXBwQmFyIHBvc2l0aW9uPVwiZml4ZWRcIiBvcGVuPXtvcGVufT5cclxuICAgICAgICAgIDxUb29sYmFyXHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcclxuICAgICAgICAgICAgICB3aWR0aDogXCIxMDAlXCIsXHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuaGVhZGVyLFxyXG4gICAgICAgICAgICAgIHpJbmRleDogMSxcclxuICAgICAgICAgICAgICBib3JkZXJUb3A6IGA0cHggc29saWQgJHt0aGVtZS5wYWxldHRlLnRleHQucHVycGxlfWAsXHJcbiAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOiBgNHB4IHNvbGlkICR7dGhlbWUucGFsZXR0ZS50ZXh0LnB1cnBsZX1gLFxyXG4gICAgICAgICAgICAgIFt0aGVtZS5icmVha3BvaW50cy5kb3duKFwieHNcIildOiB7XHJcbiAgICAgICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiBcImNvbHVtblwiLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgZWRnZT1cInN0YXJ0XCJcclxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiVG9nZ2xlIGRyYXdlclwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlRHJhd2VyfVxyXG4gICAgICAgICAgICAgIHN4PXt7IG1hcmdpblJpZ2h0OiBcIjE1cHhcIiB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge29wZW4gPyAoXHJcbiAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgc3JjPVwiL05hdkljb25zL2xlZnRfaGFtYnVyZ2VyLnN2Z1wiXHJcbiAgICAgICAgICAgICAgICAgIGFsdD1cIkNsb3NlIGRyYXdlclwiXHJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPXsyNH1cclxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PXsyNH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxNZW51SWNvbiBzeD17eyBjb2xvcjogdGhlbWUucGFsZXR0ZS50ZXh0LndoaXRlIH19IC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgIGNvbXBvbmVudD1cImgxXCJcclxuICAgICAgICAgICAgICB2YXJpYW50PVwiaDZcIlxyXG4gICAgICAgICAgICAgIGNvbG9yPVwiaW5oZXJpdFwiXHJcbiAgICAgICAgICAgICAgbm9XcmFwXHJcbiAgICAgICAgICAgICAgc3g9e3sgZmxleEdyb3c6IDEgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCI+XHJcbiAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgc3JjPXtsb2dvU291cmNlfVxyXG4gICAgICAgICAgICAgICAgICBhbHQ9XCJIYXNzYW5hIExvZ29cIlxyXG4gICAgICAgICAgICAgICAgICBsb2FkaW5nPVwibGF6eVwiXHJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPXsxODB9XHJcbiAgICAgICAgICAgICAgICAgIGhlaWdodD17NDJ9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgIGdhcDogeyB4czogMC41LCBzbTogMSwgbWQ6IDEuNSB9LFxyXG4gICAgICAgICAgICAgICAgZmxleFNocmluazogMCxcclxuICAgICAgICAgICAgICAgIFt0aGVtZS5icmVha3BvaW50cy5kb3duKFwieHNcIildOiB7XHJcbiAgICAgICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246IFwicm93XCIsXHJcbiAgICAgICAgICAgICAgICAgIGdhcDogMC4yNSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXHJcbiAgICAgICAgICAgICAgICAgIFwiJjo6YWZ0ZXJcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IFwiJydcIixcclxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHJpZ2h0OiBcIi04cHhcIixcclxuICAgICAgICAgICAgICAgICAgICB0b3A6IFwiNTAlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBcInRyYW5zbGF0ZVkoLTUwJSlcIixcclxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogXCIxcHhcIixcclxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiMjRweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMilcIixcclxuICAgICAgICAgICAgICAgICAgICBbdGhlbWUuYnJlYWtwb2ludHMuZG93bihcInNtXCIpXToge1xyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJub25lXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNoYW5nZSB0aGVtZSBvciBjb2xvclwiXHJcbiAgICAgICAgICAgICAgICAgIGFyaWEtY29udHJvbHM9XCJ0aGVtZS1tZW51XCJcclxuICAgICAgICAgICAgICAgICAgYXJpYS1oYXNwb3B1cD1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cclxuICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCJpbmhlcml0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogeyB4czogXCI2cHhcIiwgc206IFwiOHB4XCIgfSxcclxuICAgICAgICAgICAgICAgICAgICBcIiY6aG92ZXJcIjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBcInNjYWxlKDEuMDUpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAwLjJzIGVhc2UtaW4tb3V0XCIsXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEljb259XHJcbiAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgPFBvcHBlclxyXG4gICAgICAgICAgICAgICAgaWQ9XCJ0aGVtZS1tZW51XCJcclxuICAgICAgICAgICAgICAgIG9wZW49e0Jvb2xlYW4oYW5jaG9yRWwpfVxyXG4gICAgICAgICAgICAgICAgYW5jaG9yRWw9e2FuY2hvckVsfVxyXG4gICAgICAgICAgICAgICAgcGxhY2VtZW50PVwiYm90dG9tLWVuZFwiXHJcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uXHJcbiAgICAgICAgICAgICAgICBzeD17eyB6SW5kZXg6IDEwMDAwIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgeyh7IFRyYW5zaXRpb25Qcm9wcyB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxTbGlkZSB7Li4uVHJhbnNpdGlvblByb3BzfSBkaXJlY3Rpb249XCJkb3duXCIgdGltZW91dD17MzUwfT5cclxuICAgICAgICAgICAgICAgICAgICA8UGFwZXJcclxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHRoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5zZWNvbmRhcnksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIyNXB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxDbGlja0F3YXlMaXN0ZW5lciBvbkNsaWNrQXdheT17aGFuZGxlQ2xvc2V9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TWVudUxpc3QgYXV0b0ZvY3VzSXRlbT17Qm9vbGVhbihhbmNob3JFbCl9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVGhlbWVDaGFuZ2UoXCJsaWdodFwiLCA8TGlnaHRNb2RlIC8+KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlnaHRNb2RlIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRoZW1lQ2hhbmdlKFwiZGFya1wiLCA8RGFya01vZGUgLz4pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEYXJrTW9kZSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDb2xvckNoYW5nZShcImJsdWVcIiwgPENpcmNsZUljb24gc3g9e3sgZmlsbDogdGhlbWUucGFsZXR0ZS5ibHVlLm1haW4gfX0gLz4pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmNsZUljb24gc3g9e3sgZmlsbDogdGhlbWUucGFsZXR0ZS5ibHVlLm1haW4gfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ29sb3JDaGFuZ2UoXCJncmVlblwiLCA8Q2lyY2xlSWNvbiBzeD17eyBmaWxsOiB0aGVtZS5wYWxldHRlLmdyZWVuLm1haW4gfX0gLz4pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmNsZUljb24gc3g9e3sgZmlsbDogdGhlbWUucGFsZXR0ZS5ncmVlbi5tYWluIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNvbG9yQ2hhbmdlKFwicHVycGxlXCIsIDxDaXJjbGVJY29uIHN4PXt7IGZpbGw6IHRoZW1lLnBhbGV0dGUucHVycGxlLm1haW4gfX0gLz4pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmNsZUljb24gc3g9e3sgZmlsbDogdGhlbWUucGFsZXR0ZS5wdXJwbGUubWFpbiB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUxpc3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0NsaWNrQXdheUxpc3RlbmVyPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgICAgICAgICAgIDwvU2xpZGU+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvUG9wcGVyPlxyXG4gICAgICAgICAgICAgIDxCb3ggc3g9e3sgcG9zaXRpb246IFwicmVsYXRpdmVcIiB9fT5cclxuICAgICAgICAgICAgICAgIDxBbmltYXRlZE5vdGlmaWNhdGlvbkljb24gaGFzTmV3Tm90aWZpY2F0aW9ucz17aGFzTmV3Tm90aWZpY2F0aW9uc30+XHJcbiAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJpbmhlcml0XCJcclxuICAgICAgICAgICAgICAgICAgICByZWY9e25vdGlmaWNhdGlvbkFuY2hvclJlZn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVOb3RpZmljYXRpb25Ub2dnbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD17YFNob3cgJHtub3RpZmljYXRpb25zLmxlbmd0aH0gbm90aWZpY2F0aW9uc30gbm90aWZpY2F0aW9uc2B9XHJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCJpbmhlcml0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiB7IHhzOiBcIjhweFwiLCBzbTogXCIxMHB4XCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSlcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBcInNjYWxlKDEuMDUpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4ycyBlYXNlLWluLW91dFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8QW5pbWF0ZWRCYWRnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgYmFkZ2VDb250ZW50PXt1bnNlZW5Db3VudERhdGE/LnVuc2Vlbk5vdGlmaWNhdGlvbnNDb3VudCB8fCAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgaGFzTmV3Tm90aWZpY2F0aW9ucz17aGFzTmV3Tm90aWZpY2F0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAgIG1heD17OTl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvbnNBY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGhhc05ld05vdGlmaWNhdGlvbnMgPyBcIiNmZjQ0NDRcIiA6IFwiaW5oZXJpdFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IHsgeHM6IFwiMjBweFwiLCBzbTogXCIyNHB4XCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogaGFzTmV3Tm90aWZpY2F0aW9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiZHJvcC1zaGFkb3coMCAwIDhweCByZ2JhKDI1NSwgNjgsIDcwLCAwLjUpKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJub25lXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxOb3RpZmljYXRpb25zIHN4PXt7IGZvbnRTaXplOiB7IHhzOiBcIjIwcHhcIiwgc206IFwiMjRweFwiIH0gfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9BbmltYXRlZEJhZGdlPlxyXG4gICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L0FuaW1hdGVkTm90aWZpY2F0aW9uSWNvbj5cclxuICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICA8U29jaWFsTm90aWZpY2F0aW9uUG9wcGVyXHJcbiAgICAgICAgICAgICAgICBvcGVuPXtub3RpZmljYXRpb25PcGVufVxyXG4gICAgICAgICAgICAgICAgYW5jaG9yRWw9e25vdGlmaWNhdGlvbkFuY2hvclJlZi5jdXJyZW50fVxyXG4gICAgICAgICAgICAgICAgb25DbG9zZT17aGFuZGxlTm90aWZpY2F0aW9uQ2xvc2V9XHJcbiAgICAgICAgICAgICAgICBub3RpZmljYXRpb25zPXtub3RpZmljYXRpb25zfVxyXG4gICAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHJlbW92ZUhhbmRsZXI9e3JlbW92ZUFubm91bmNlbWVudEhhbmRsZXJ9XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZENvbG9yPXtzZWxlY3RlZENvbG9yfVxyXG4gICAgICAgICAgICAgICAgdGhlbWU9e3RoZW1lfVxyXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWROb3RpZmljYXRpb249e3NlbGVjdGVkTm90aWZpY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgb25Ob3RpZmljYXRpb25DbGljaz17aGFuZGxlTm90aWZpY2F0aW9uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgICBvbkJhY2tUb0xpc3Q9e2hhbmRsZUJhY2tUb0xpc3R9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICA8L1Rvb2xiYXI+XHJcbiAgICAgICAgPC9BcHBCYXI+XHJcbiAgICAgICl9XHJcbiAgICAgIDxEcmF3ZXJcclxuICAgICAgICB2YXJpYW50PXtkcmF3ZXJWYXJpYW50fVxyXG4gICAgICAgIG9wZW49e29wZW59XHJcbiAgICAgICAgc3g9e3tcclxuICAgICAgICAgIHpJbmRleDogMixcclxuICAgICAgICAgIGJvcmRlclJpZ2h0OiBtb2RlID09PSBcImxpZ2h0XCIgPyBcIjFweCBzb2xpZCB3aGl0ZVwiIDogXCJub25lXCIsXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxCb3hcclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnByaW1hcnksXHJcbiAgICAgICAgICAgIG1hcmdpbjogXCIxMHB4XCIsXHJcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIwLjYyNXJlbVwiLFxyXG4gICAgICAgICAgICBoZWlnaHQ6IFwiMTAwJVwiLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8VG9vbGJhclxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgbWFyZ2luVG9wOiBcImF1dG9cIixcclxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJmbGV4LWVuZFwiLFxyXG4gICAgICAgICAgICAgIHB4OiBbMV0sXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxQcm9maWxlIC8+XHJcbiAgICAgICAgICA8L1Rvb2xiYXI+XHJcbiAgICAgICAgICA8TGlzdFxyXG4gICAgICAgICAgICBjb21wb25lbnQ9XCJuYXZcIlxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246IFwiY29sdW1uXCIsXHJcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYmV0d2VlblwiLFxyXG4gICAgICAgICAgICAgIGhlaWdodDogXCI4MHZoXCIsXHJcbiAgICAgICAgICAgICAgbWFyZ2luVG9wOiBcIjIwcHhcIixcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICA8TWFpbkxpc3RJdGVtcyAvPlxyXG4gICAgICAgICAgICAgIHtpc0FkbWluICYmIChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTZXRNb3JlSXRlbUNsaWNrfT5cclxuICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFNldHRpbmdzSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiQWRtaW4gU2V0dGluZ3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIHttb3JlSXRlbSA/IDxFeHBhbmRMZXNzIC8+IDogPEV4cGFuZE1vcmUgLz59XHJcbiAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDxDb2xsYXBzZSBpbj17bW9yZUl0ZW19IHRpbWVvdXQ9XCJhdXRvXCIgdW5tb3VudE9uRXhpdD5cclxuICAgICAgICAgICAgICAgICAgICA8TGlzdCBjb21wb25lbnQ9XCJkaXZcIiBkaXNhYmxlUGFkZGluZz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vbmV3c1wiIHBhc3NIcmVmPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1CdXR0b24gc3g9e3sgaGVpZ2h0OiAzMCB9fSBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE5ld3NwYXBlckljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIk5ld3NcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9hbm5vdW5jZW1lbnRzXCIgcGFzc0hyZWY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUJ1dHRvbiBzeD17eyBoZWlnaHQ6IDMwIH19IHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FtcGFpZ25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIkFubm91bmNlbWVudHNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9ldmVudHNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDZWxlYnJhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiRXZlbnRzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vcXVvdGVzXCIgcGFzc0hyZWY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUJ1dHRvbiBzeD17eyBoZWlnaHQ6IDMwIH19IHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybWF0UXVvdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIlF1b3Rlc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluT2ZmZXJcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMb2NhbE9mZmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiBtb2RlICE9PSBcImxpZ2h0XCIgPyBcIndoaXRlXCIgOiBcImluaGVyaXRcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1UZXh0IHByaW1hcnk9XCJPZmZlcnNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9ub3RpZmljYXRpb25zXCIgcGFzc0hyZWY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUJ1dHRvbiBzeD17eyBoZWlnaHQ6IDMwIH19IHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uc0FjdGl2ZVJvdW5kZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIk5vdGlmaWNhdGlvbnNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9sZWF2ZXNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYXNrXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiBtb2RlICE9PSBcImxpZ2h0XCIgPyBcIndoaXRlXCIgOiBcImluaGVyaXRcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1UZXh0IHByaW1hcnk9XCJMZWF2ZXNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGlzdD5cclxuICAgICAgICAgICAgICAgICAgPC9Db2xsYXBzZT5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgIDxTZWNvbmRhcnlMaXN0SXRlbXMgLz5cclxuICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICA8L0xpc3Q+XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgIDwvRHJhd2VyPlxyXG4gICAgICA8U25hY2tiYXJcclxuICAgICAgICBvcGVuPXtzbmFja2Jhck9wZW59XHJcbiAgICAgICAgYXV0b0hpZGVEdXJhdGlvbj17NjAwMH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTbmFja2Jhck9wZW4oZmFsc2UpfVxyXG4gICAgICA+XHJcbiAgICAgICAgPEFsZXJ0XHJcbiAgICAgICAgICBzZXZlcml0eT17c25hY2tiYXJTZXZlcml0eX1cclxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNuYWNrYmFyT3BlbihmYWxzZSl9XHJcbiAgICAgICAgICBzeD17eyB3aWR0aDogXCIxMDAlXCIgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7c25hY2tiYXJNZXNzYWdlfVxyXG4gICAgICAgIDwvQWxlcnQ+XHJcbiAgICAgIDwvU25hY2tiYXI+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJncWwiLCJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ29udGV4dCIsInVzZVJlZiIsInVzZUVmZmVjdCIsInN0eWxlZCIsInVzZVRoZW1lIiwiTXVpRHJhd2VyIiwidXNlU2VsZWN0ZWRDb2xvciIsImNvbG9yIiwiZm9ybWF0RGF0ZVRpbWVVVEMiLCJCb3giLCJ1c2VNZWRpYVF1ZXJ5IiwiTXVpQXBwQmFyIiwiVG9vbGJhciIsIkxpc3QiLCJUeXBvZ3JhcGh5IiwiSWNvbkJ1dHRvbiIsIlRhc2siLCJOb3RpZmljYXRpb25zIiwiRGFya01vZGUiLCJMaWdodE1vZGUiLCJDYW1wYWlnbiIsIkNlbGVicmF0aW9uIiwiRm9ybWF0UXVvdGUiLCJOb3RpZmljYXRpb25zQWN0aXZlUm91bmRlZCIsIkxvY2FsT2ZmZXIiLCJOb3RpZmljYXRpb25zQWN0aXZlIiwiQXJyb3dGb3J3YXJkSWNvbiIsIkFycm93QmFja0ljb24iLCJNZW51SWNvbiIsIkV4cGFuZExlc3MiLCJFeHBhbmRNb3JlIiwiU2V0dGluZ3NJY29uIiwiTmV3c3BhcGVySWNvbiIsIkNpcmNsZUljb24iLCJJbWFnZSIsIkJhZGdlIiwiTGluayIsIlBvcHBlciIsIlBhcGVyIiwiQ2xpY2tBd2F5TGlzdGVuZXIiLCJNZW51SXRlbSIsIk1lbnVMaXN0IiwiTGlzdEl0ZW0iLCJMaXN0SXRlbVRleHQiLCJDb2xsYXBzZSIsIkxpc3RJdGVtSWNvbiIsIkxpc3RJdGVtQnV0dG9uIiwiU2xpZGUiLCJab29tIiwia2V5ZnJhbWVzIiwidXNlQ29sb3IiLCJEcmF3ZXJDb250ZXh0IiwidXNlTW9kZSIsInVzZU11dGF0aW9uIiwidXNlUXVlcnkiLCJnZXROZXdOb3RpZmljYXRpb25zIiwiZ2V0Tm90aWZpY2F0aW9ucyIsImdldFVuc2Vlbk5vdGlmaWNhdGlvbnNDb3VudCIsIm11dGF0aW9uTWFya0FsbE5vdGlmaWNhdGlvbnNBc1NlZW4iLCJtdXRhdGlvbkFkZE5vdGlmaWNhdGlvblZpZXciLCJ1c2VTZXNzaW9uIiwiQ2lyY3VsYXJQcm9ncmVzcyIsIlNuYWNrYmFyIiwiQWxlcnQiLCJEaXZpZGVyIiwiTWFpbkxpc3RJdGVtcyIsIlNlY29uZGFyeUxpc3RJdGVtcyIsIlByb2ZpbGUiLCJ1c2VSb3V0ZXIiLCJkcmF3ZXJXaWR0aCIsInB1bHNlIiwic2hha2UiLCJnbG93IiwiQW5pbWF0ZWRCYWRnZSIsInRoZW1lIiwiaGFzTmV3Tm90aWZpY2F0aW9ucyIsImJhY2tncm91bmRDb2xvciIsImZvbnRXZWlnaHQiLCJmb250U2l6ZSIsIm1pbldpZHRoIiwiaGVpZ2h0IiwiYm9yZGVyUmFkaXVzIiwiYm9yZGVyIiwiYW5pbWF0aW9uIiwiYm94U2hhZG93IiwiQW5pbWF0ZWROb3RpZmljYXRpb25JY29uIiwidHJhbnNmb3JtIiwidHJhbnNpdGlvbiIsIkFwcEJhciIsInNob3VsZEZvcndhcmRQcm9wIiwicHJvcCIsIm9wZW4iLCJ6SW5kZXgiLCJkcmF3ZXIiLCJ0cmFuc2l0aW9ucyIsImNyZWF0ZSIsImVhc2luZyIsInNoYXJwIiwiZHVyYXRpb24iLCJsZWF2aW5nU2NyZWVuIiwibWFyZ2luTGVmdCIsIndpZHRoIiwiYnJlYWtwb2ludHMiLCJ1cCIsInNwYWNpbmciLCJkb3duIiwiZW50ZXJpbmdTY3JlZW4iLCJEcmF3ZXIiLCJwYWxldHRlIiwiYmFja2dyb3VuZCIsInNlY29uZGFyeSIsInBvc2l0aW9uIiwid2hpdGVTcGFjZSIsImNvbXBsZXgiLCJib3hTaXppbmciLCJvdmVyZmxvd1giLCJTb2NpYWxOb3RpZmljYXRpb25Qb3BwZXIiLCJhbmNob3JFbCIsIm9uQ2xvc2UiLCJub3RpZmljYXRpb25zIiwibG9hZGluZyIsInJlbW92ZUhhbmRsZXIiLCJzZWxlY3RlZENvbG9yIiwicm9sZSIsInVuZGVmaW5lZCIsInN4IiwibWF4SGVpZ2h0IiwibGVuZ3RoIiwib3ZlcmZsb3dZIiwibWF4V2lkdGgiLCJkaXNhYmxlUG9ydGFsIiwicG9wcGVyT3B0aW9ucyIsIm1vZGlmaWVycyIsIm5hbWUiLCJvcHRpb25zIiwib2Zmc2V0IiwicGFkZGluZyIsIlRyYW5zaXRpb25Qcm9wcyIsInRpbWVvdXQiLCJlbGV2YXRpb24iLCJvdmVyZmxvdyIsImJhY2tkcm9wRmlsdGVyIiwib25DbGlja0F3YXkiLCJwIiwiYm9yZGVyQm90dG9tIiwiaGVhZGVyIiwidGV4dCIsIndoaXRlIiwidmFyaWFudCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwiZ2FwIiwicHgiLCJweSIsImp1c3RpZnlDb250ZW50IiwibWFwIiwibm90aWZpY2F0aW9uRGF0YSIsImluZGV4IiwiaHJlZiIsImlkIiwicGFzc0hyZWYiLCJvbkNsaWNrIiwiY3Vyc29yIiwiZmxleFNocmluayIsImZsZXgiLCJsaW5lSGVpZ2h0IiwibWIiLCJ3b3JkQnJlYWsiLCJub3RpZmljYXRpb24iLCJjcmVhdGVkQXQiLCJsZWZ0IiwidG9wIiwiYm90dG9tIiwiZmxleERpcmVjdGlvbiIsInRleHRBbGlnbiIsImJvcmRlclRvcCIsImNvbXBvbmVudCIsInRleHREZWNvcmF0aW9uIiwiSGVhZGVyIiwic2Vzc2lvbiIsInNldE9wZW4iLCJtb2RlIiwic2V0TW9kZSIsInNldEdsb2JhbENvbG9yIiwiaXNNb2JpbGUiLCJkYXRhIiwic3RhdHVzIiwiaXNBZG1pbiIsInVzZXIiLCJub3RpZmljYXRpb25BbmNob3JSZWYiLCJtb3JlSXRlbSIsInNldE1vcmVJdGVtIiwibm90aWZpY2F0aW9uT3BlbiIsInNldE5vdGlmaWNhdGlvbk9wZW4iLCJzZXRBbmNob3JFbCIsInNlbGVjdGVkSWNvbiIsInNldFNlbGVjdGVkSWNvbiIsInNldE5vdGlmaWNhdGlvbnMiLCJzbmFja2Jhck9wZW4iLCJzZXRTbmFja2Jhck9wZW4iLCJzbmFja2Jhck1lc3NhZ2UiLCJzZXRTbmFja2Jhck1lc3NhZ2UiLCJzbmFja2JhclNldmVyaXR5Iiwic2V0U25hY2tiYXJTZXZlcml0eSIsInNldEhhc05ld05vdGlmaWNhdGlvbnMiLCJwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50Iiwic2V0UHJldmlvdXNOb3RpZmljYXRpb25Db3VudCIsImxhc3ROb3RpZmljYXRpb25UaW1lIiwic2V0TGFzdE5vdGlmaWNhdGlvblRpbWUiLCJlbmFibGVQb2xsaW5nIiwic2V0RW5hYmxlUG9sbGluZyIsImxvZ29Tb3VyY2UiLCJkcmF3ZXJWYXJpYW50IiwidXNlcklkIiwidXNlcl9pZCIsImVycm9yIiwidmFyaWFibGVzIiwic2tpcCIsInBvbGxJbnRlcnZhbCIsImZldGNoUG9saWN5IiwiZXJyb3JQb2xpY3kiLCJub3RpZnlPbk5ldHdvcmtTdGF0dXNDaGFuZ2UiLCJ1bnNlZW5Db3VudERhdGEiLCJ1bnNlZW5Db3VudExvYWRpbmciLCJyZWZldGNoIiwicmVmZXRjaFVuc2VlbkNvdW50IiwiYWRkTm90aWZpY2F0aW9uVmlldyIsIm1hcmtBbGxBc1NlZW4iLCJwbGF5Tm90aWZpY2F0aW9uU291bmQiLCJhdWRpbyIsIkF1ZGlvIiwidm9sdW1lIiwicGxheSIsImNhdGNoIiwiZSIsImNvbnNvbGUiLCJsb2ciLCJzaG93QnJvd3Nlck5vdGlmaWNhdGlvbiIsIm1lc3NhZ2UiLCJ3aW5kb3ciLCJOb3RpZmljYXRpb24iLCJwZXJtaXNzaW9uIiwiYm9keSIsImljb24iLCJiYWRnZSIsInRhZyIsInJlcXVpcmVJbnRlcmFjdGlvbiIsInNpbGVudCIsInJlcXVlc3RQZXJtaXNzaW9uIiwid2FybiIsImFsbE5vdGlmaWNhdGlvbnMiLCJjdXJyZW50Q291bnQiLCJEYXRlIiwibm93IiwibmV3Tm90aWZpY2F0aW9uQ291bnQiLCJzZXRUaW1lb3V0IiwiZ3JhcGhRTEVycm9ycyIsIm5ldHdvcmtFcnJvciIsInRvZ2dsZURyYXdlciIsImhhbmRsZVNldE1vcmVJdGVtQ2xpY2siLCJoYW5kbGVDbGljayIsImV2ZW50IiwiY3VycmVudFRhcmdldCIsImhhbmRsZUNsb3NlIiwiaGFuZGxlTm90aWZpY2F0aW9uVG9nZ2xlIiwid2FzT3BlbiIsInByZXYiLCJoYW5kbGVOb3RpZmljYXRpb25DbG9zZSIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsInNldFNlbGVjdGVkTm90aWZpY2F0aW9uIiwiaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2siLCJoYW5kbGVCYWNrVG9MaXN0IiwiaGFuZGxlVGhlbWVDaGFuZ2UiLCJoYW5kbGVDb2xvckNoYW5nZSIsInJlbW92ZUFubm91bmNlbWVudEhhbmRsZXIiLCJub3RpZmljYXRpb25JZCIsInJlc3BvbnNlIiwidXBkYXRlZE5vdGlmaWNhdGlvbnMiLCJmaWx0ZXIiLCJuIiwicHVycGxlIiwiZWRnZSIsImFyaWEtbGFiZWwiLCJtYXJnaW5SaWdodCIsInNyYyIsImFsdCIsIm5vV3JhcCIsImZsZXhHcm93IiwieHMiLCJzbSIsIm1kIiwiY29udGVudCIsInJpZ2h0IiwiYXJpYS1jb250cm9scyIsImFyaWEtaGFzcG9wdXAiLCJCb29sZWFuIiwicGxhY2VtZW50IiwiZGlyZWN0aW9uIiwiYXV0b0ZvY3VzSXRlbSIsImZpbGwiLCJibHVlIiwibWFpbiIsImdyZWVuIiwicmVmIiwiYmFkZ2VDb250ZW50IiwidW5zZWVuTm90aWZpY2F0aW9uc0NvdW50IiwibWF4Iiwic2VsZWN0ZWROb3RpZmljYXRpb24iLCJvbk5vdGlmaWNhdGlvbkNsaWNrIiwib25CYWNrVG9MaXN0IiwiYm9yZGVyUmlnaHQiLCJwcmltYXJ5IiwibWFyZ2luIiwibWFyZ2luVG9wIiwiaW4iLCJ1bm1vdW50T25FeGl0IiwiZGlzYWJsZVBhZGRpbmciLCJzaXplIiwiYXV0b0hpZGVEdXJhdGlvbiIsInNldmVyaXR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});