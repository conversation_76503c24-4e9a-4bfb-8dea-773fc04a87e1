"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/news",{

/***/ "./src/components/AnnouncementCard.js":
/*!********************************************!*\
  !*** ./src/components/AnnouncementCard.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/Close */ \"./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/imageUtils */ \"./src/utils/imageUtils.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AnnouncementCard = (param)=>{\n    let { title, details, isSelected, onClick, borderLeft, isCurrentAnnouncement, showDate, date, image } = param;\n    _s();\n    const [openImageModal, setOpenImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useMediaQuery)(\"(max-width: 1200px)\");\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    // Use the utility function to get proper image URL\n    const imgUpdate = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.getAnnouncementImageUrl)(image);\n    const handleCardClick = (e)=>{\n        if (image && !e.target.closest(\"a, button\")) {\n            setOpenImageModal(true);\n        }\n        if (onClick) onClick(e);\n    };\n    const handleCloseModal = (e)=>{\n        e.stopPropagation();\n        setOpenImageModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n            gap: \"15px\",\n            height: \"100%\",\n            backgroundColor: \"blue\",\n            cursor: image ? \"pointer\" : \"default\"\n        },\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                sx: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n                    gap: \"7.5px\",\n                    width: \"100%\",\n                    padding: \"0 16px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        sx: {\n                            fontWeight: 700,\n                            fontSize: \"16px\",\n                            fontFamily: \"Urbanist\",\n                            wordWrap: \"break-word\",\n                            width: \"100%\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Divider, {\n                        sx: {\n                            width: \"122px\",\n                            border: isCurrentAnnouncement ? \"1px solid #A665E1\" : \"1px solid #00BC82\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                variant: \"body2\",\n                sx: {\n                    color: theme.palette.text.primary,\n                    fontWeight: 500,\n                    fontFamily: \"Inter\",\n                    wordWrap: \"normal\",\n                    fontSize: \"14px\",\n                    wordWrap: \"break-word\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: details\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined),\n            showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                variant: \"caption\",\n                sx: {\n                    color: theme.palette.text.secondary,\n                    fontFamily: \"Inter\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: new Date(date).toLocaleDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined),\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                open: openImageModal,\n                onClose: handleCloseModal,\n                \"aria-labelledby\": \"image-modal\",\n                \"aria-describedby\": \"image-modal-description\",\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backdropFilter: \"blur(5px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    sx: {\n                        position: \"relative\",\n                        width: isMediumScreen ? \"90%\" : \"70%\",\n                        height: isMediumScreen ? \"auto\" : \"80%\",\n                        bgcolor: \"background.paper\",\n                        boxShadow: 24,\n                        p: 2,\n                        display: \"flex\",\n                        flexDirection: isMediumScreen ? \"column\" : \"row\",\n                        borderRadius: \"8px\",\n                        overflow: \"hidden\"\n                    },\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                            onClick: handleCloseModal,\n                            sx: {\n                                position: \"absolute\",\n                                right: 10,\n                                top: 10,\n                                zIndex: 1,\n                                backgroundColor: \"rgba(0,0,0,0.5)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(0,0,0,0.7)\"\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                height: isMediumScreen ? \"300px\" : \"100%\",\n                                overflow: \"hidden\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                backgroundColor: \"#f5f5f5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imgUpdate,\n                                alt: title,\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                padding: \"20px\",\n                                overflowY: \"auto\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"h5\",\n                                    gutterBottom: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"subtitle1\",\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    children: new Date(date).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Divider, {\n                                    sx: {\n                                        my: 0\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"body1\",\n                                    children: details\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 142,\n                columnNumber: 15\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementCard, \"KuuCFbfhsAlXP8yulIWuHTzVnBM=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useMediaQuery,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = AnnouncementCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementCard);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementCard.js\n"));

/***/ })

});