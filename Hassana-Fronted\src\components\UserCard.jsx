import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { useMediaQuery } from "@mui/material";
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Link from 'next/link';
import { Box, fontWeight } from '@mui/system';
import Image from 'next/image';
import { Divider, Grid, Typography, useTheme } from '@mui/material';

export default function UserCard({ open, setOpen, data }) {
    // console.log("data in card: ", data);
    const theme = useTheme();
    const Baseurls = 'https://hassana-api.360xpertsolutions.com/v1/'

    console.log(data ,'datadatadatadata')

    const handleClose = () => {
        setOpen(false);
    };
    const isXSmallScreen = useMediaQuery("(max-width:400px)");
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
    const isMediumScreen = useMediaQuery(theme.breakpoints.down('md'));
    const width = isSmallScreen || isXSmallScreen ? 80 : isMediumScreen ? 120 : 500;
    const height = isMediumScreen ? 80 : 500;
    console.log(data.bio_link)
    return (
        <React.Fragment >
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                maxWidth="md"  // Change this value to 'lg' or 'xl' for a larger width
                fullWidth={true}
            >
                <DialogTitle id="alert-dialog-title" sx={{ padding: "0px" }} >
                    <Box style={{ backgroundColor: "#003e53", display: "flex", alignItems: "center", justifyContent: "space-between", padding: "15px", borderTop: "5px solid #A665E1", borderBottom: "5px solid #A665E1", }}>
                        <Image src="/images/HassanaLogos.png" alt="hassana" width={150} height={150} />
                        <Typography sx={{ color: "#FFF", textAlign: "right", fontSize: isXSmallScreen ? "12px" : "16px" }}>مرحبا بكم في حصانة<br />Welcome to hassana</Typography>
                    </Box>
                </DialogTitle>
                <DialogContent sx={{ padding: "0" }}>
                    <DialogContentText id="alert-dialog-description" sx={{ padding: 0, marginY: "50px" }}>
                        <Box sx={{ marginX: "10%" }}>
                            <Grid container spacing={5}>
                                <Grid item xs={12} sm={12} md={2} sx={isMediumScreen ? { display: 'flex', alignItems: 'center', justifyContent: 'center' } : {}} >
                                    {data.profile ? <img src={Baseurls+data.profile} width={width} height={height} style={{ borderRadius: "10px" }} /> : ""}



                                    {/* <Image src="/images/avatars/1.png" alt="hassana" width={width} height={height} style={{ borderRadius: "100px" }} /> */}
                                </Grid>
                                <Grid item xs={12} sm={12} md={10} >
                                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                        <Typography variant='h4' sx={{ fontSize: isSmallScreen ? "15px" : "24px" }} color={"#A665E1"} fontWeight={600}>{data.name ? data.name : "----"}</Typography>
                                        {/* <Typography variant='h4' sx={{fontSize:isSmallScreen?"15px":"24px"}} color={"#A665E1"} fontWeight={600}>Sharifa Alshuhail</Typography> */}
                                        <Typography variant='h4' sx={{ fontSize: isSmallScreen ? "15px" : "24px" }} color={"#A665E1"}>{data.name_arabic ? data.name_arabic : "----"}</Typography>
                                    </Box>
                                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                        <Typography variant='h5' sx={{ fontSize: isSmallScreen ? "12px" : "21px", color: "#000" }} >{data.department ? data.department : "----"}</Typography>
                                        <Typography variant='h5' sx={{ fontSize: isSmallScreen ? "12px" : "21px", color: "#000" }}>{data.department_arabic ? data.department_arabic : "----"}</Typography>
                                    </Box>
                                    <Box sx={{ display: "flex", justifyContent: "space-between", marginY: "10px" }}>
                                        <Divider sx={{ height: "3px", width: "10%", backgroundColor: "#00BC82", borderRadius: "10px" }} />
                                        <Divider sx={{ height: "3px", width: "10%", backgroundColor: "#00BC82", borderRadius: "10px" }} />
                                    </Box>
                                    <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                        <Typography variant='h5' sx={{ fontSize: isSmallScreen ? "12px" : "21px", color: "#000" }}  >{data.designation}</Typography>
                                        <Typography variant='h5' sx={{ fontSize: isSmallScreen ? "12px" : "21px", color: "#000" }}>{data.designation_arabic ? data.designation_arabic : "----"}</Typography>
                                    </Box>
                                    <Typography variant='h5' sx={{ fontSize: isSmallScreen ? "10px" : "21px", color: "#A7A7A7" }}>{data.email ? data.email : "----"}</Typography>
                                </Grid>
                            </Grid>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <Divider sx={{ height: "6px", width: "100%", backgroundColor: "#00BC82", marginBottom: "5px" }} />
                {data?.bio_link ? (
                    <Link href={data.bio_link} target="_blank">
                        <Box sx={{ background: "#A665E1", display: "flex", justifyContent: "space-between", padding: "15px" }}>
                            <Typography variant='body2' color={"#fff"} sx={{ fontSize: isXSmallScreen ? "10px" : "14px" }}>Click here for Bio</Typography>
                            <Typography variant='body2' color={"#fff"} sx={{ fontSize: isXSmallScreen ? "10px" : "14px" }}>انقر هنا للحصول على السيرة الذاتية</Typography>
                        </Box>
                    </Link>
                ) : (
                    <Box sx={{ background: "#A665E1", display: "flex", justifyContent: "space-between", padding: "15px" }}>
                        <Typography variant='body2' color={"#fff"} sx={{ fontSize: isXSmallScreen ? "10px" : "14px" }}>Click here for Bio</Typography>
                        <Typography variant='body2' color={"#fff"} sx={{ fontSize: isXSmallScreen ? "10px" : "14px" }}>انقر هنا للحصول على السيرة الذاتية</Typography>
                    </Box>
                )}

            </Dialog>
        </React.Fragment>
    );
}
