import { baseUrl } from "@/Data/ApolloClient";

/**
 * Utility function to construct proper image URLs for news/announcements
 * Handles different image path formats and ensures compatibility with any domain
 * 
 * @param {string} imagePath - The image path from the backend
 * @returns {string|null} - The properly constructed image URL or null if no image
 */
export const getImageUrl = (imagePath) => {
  // Return null if no image path provided
  if (!imagePath) {
    return null;
  }

  // If it's already a base64 data URL (for new uploads), return as is
  if (typeof imagePath === 'string' && imagePath.startsWith('data:')) {
    return imagePath;
  }

  // If it's already a complete URL (starts with http/https), return as is
  if (typeof imagePath === 'string' && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {
    return imagePath;
  }

  // Clean the image path by removing any existing domain/base URL
  let cleanPath = imagePath;
  
  // Remove common localhost URLs that might be in the path
  cleanPath = cleanPath.replace(/^https?:\/\/localhost:\d+/, '');
  cleanPath = cleanPath.replace(/^http:\/\/localhost:\d+/, '');
  
  // Remove any existing domain from the path
  cleanPath = cleanPath.replace(/^https?:\/\/[^/]+/, '');
  
  // Remove leading slash if present (we'll add it back)
  cleanPath = cleanPath.replace(/^\/+/, '');
  
  // Ensure the path starts with the correct resource path
  if (!cleanPath.startsWith('resource/v1') && !cleanPath.startsWith('v1/resource')) {
    // If the path doesn't include the resource directory, add it
    if (cleanPath.includes('news/') || cleanPath.includes('announcement/')) {
      cleanPath = `resource/v1/${cleanPath}`;
    } else {
      // Default to resource/v1 for other images
      cleanPath = `resource/v1/${cleanPath}`;
    }
  }
  
  // Construct the final URL with current baseUrl
  const finalUrl = `${baseUrl}/${cleanPath}`;
  
  console.log('Image URL construction:', {
    original: imagePath,
    cleaned: cleanPath,
    final: finalUrl,
    baseUrl: baseUrl
  });
  
  return finalUrl;
};

/**
 * Utility function specifically for news images
 * @param {string} imagePath - The image path from the backend
 * @returns {string|null} - The properly constructed news image URL
 */
export const getNewsImageUrl = (imagePath) => {
  if (!imagePath) return null;
  
  let cleanPath = imagePath;
  
  // Remove any existing domain
  cleanPath = cleanPath.replace(/^https?:\/\/[^/]+/, '');
  cleanPath = cleanPath.replace(/^\/+/, '');
  
  // Ensure it's in the correct news directory
  if (!cleanPath.includes('news/') && !cleanPath.startsWith('resource/v1/news/')) {
    cleanPath = `resource/v1/news/${cleanPath}`;
  } else if (!cleanPath.startsWith('resource/v1/')) {
    cleanPath = `resource/v1/${cleanPath}`;
  }
  
  return `${baseUrl}/${cleanPath}`;
};

/**
 * Utility function specifically for announcement images
 * @param {string} imagePath - The image path from the backend
 * @returns {string|null} - The properly constructed announcement image URL
 */
export const getAnnouncementImageUrl = (imagePath) => {
  if (!imagePath) return null;
  
  let cleanPath = imagePath;
  
  // Remove any existing domain
  cleanPath = cleanPath.replace(/^https?:\/\/[^/]+/, '');
  cleanPath = cleanPath.replace(/^\/+/, '');
  
  // Ensure it's in the correct announcement directory
  if (!cleanPath.includes('announcement/') && !cleanPath.startsWith('resource/v1/announcement/')) {
    cleanPath = `resource/v1/announcement/${cleanPath}`;
  } else if (!cleanPath.startsWith('resource/v1/')) {
    cleanPath = `resource/v1/${cleanPath}`;
  }
  
  return `${baseUrl}/${cleanPath}`;
};
