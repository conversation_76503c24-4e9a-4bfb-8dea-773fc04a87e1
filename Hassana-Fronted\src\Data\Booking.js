
import { gql } from "@apollo/client";
import { base_url } from "./ApolloClient";

import axios from "axios";
import formData from 'form-data';

export const MutationCreateBooking = gql`
mutation CreateBooking(
    # $id: ID!
    $title: String!
    $user_id: ID!
    # $resourceId: ID!
    $details: String!
    $location: String
    $teaBoy: Boolean!
    $parking: Boolean!
    $itTechnician: Boolean!
    $start: DateTime!
    $uid:String!
    # $status:String!
    $end: DateTime!
  ) {
    createBooking(CreateBookingInput: {
      # id: $id
      title: $title
      user_id: $user_id
      teaBoy: $teaBoy
      location: $location
      parking: $parking
      itTechnician: $itTechnician
      details: $details
      uid: $uid
      start: $start
      end: $end
    }) {
      id,
      title,
      user_id,
      details,
      teaBoy,
      location,
      parking,
      uid,
      itTechnician,
      start,
      end
    }
  }
  `;

export const createBooking = async (formDataInput, isImageChanged) => {
  try {
    let data = new FormData();
    console.log(FormData);
    if (isImageChanged) {
      data.append('registrationDoc', formDataInput.registrationDoc)
    }
    data.append('title', formDataInput.title);
    data.append('user_id', formDataInput.userId);
    data.append('teaBoy',formDataInput.teaBoy);
    data.append('location', formDataInput.location);
    data.append('parking', formDataInput.parking);
    data.append('itTechnician', formDataInput.itTechnician);
    data.append('details', formDataInput.details);
    data.append('uid', formDataInput.uid);
    data.append('start', formDataInput.start);
    data.append('end', formDataInput.end);

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${base_url}/${'our-booking'}`,
      // url: `http://localhost:3001/our-booking`,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: data
    };

    let res = await axios.request(config);
    console.log(res.data);
    return res.data;

  } catch (error) {
    console.log(error);
    return error.message
  }
}


export const getBookings = gql`
  query {
    bookings {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
  }
`;
// export const getBookings = gql`
//   query {
//     bookings {
//         id,
//         title,
//         user_id,
//         resourceId,
//         startTime,
//         endTime
//     }
//   }
// `;

export const GET_BOOKINGS_OF_USER = gql`
  query BookingsOfUser($user_id: ID!) {
    bookingsOfUser(id: $user_id) {
      id
      title
      start
      end
      details
      parking
      teaBoy
      location
      itTechnician
      registrationDoc
      uid
      user_id
    }
  }
`;

export const GET_BOOKINGS_OF_TEA_BOY = gql`
  query {
    bookingsOfTeaBoy {
      id
      title
      start
      end
      details
      registrationDoc
      parking
      teaBoy
      location
      itTechnician
      uid
      user_id
    }
  }
`;

export const mutationUpdateBookingStatus = gql`
  mutation UpdateBookingStatus(
    $id: ID!
    $status: String!
  ) {
    updateBookingStatus(updateBookingInput: {
        id: $id
        status: $status
    }) {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
}
`;