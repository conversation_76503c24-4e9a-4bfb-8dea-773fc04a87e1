"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.js":
/*!****************************!*\
  !*** ./src/pages/index.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomeComponent: function() { return /* binding */ HomeComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Dashboard */ \"./src/components/Dashboard.jsx\");\n/* harmony import */ var _barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Grid,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Grid,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _components_TaskSummary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TaskSummary */ \"./src/components/TaskSummary.jsx\");\n/* harmony import */ var _components_Celebration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Celebration */ \"./src/components/Celebration.jsx\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! prop-types */ \"./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var _components_Schedule__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Schedule */ \"./src/components/Schedule.jsx\");\n/* harmony import */ var _components_NewJoin__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NewJoin */ \"./src/components/NewJoin.jsx\");\n/* harmony import */ var _components_LeaveTracker__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LeaveTracker */ \"./src/components/LeaveTracker.jsx\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _components_ColorContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _helper_mediaQueries__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../helper/mediaQueries */ \"./src/helper/mediaQueries.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_Quote__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/Quote */ \"./src/components/Quote.jsx\");\n/* harmony import */ var _components_auth_withAuthServerSide__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/auth/withAuthServerSide */ \"./src/components/auth/withAuthServerSide.js\");\n/* harmony import */ var _components_ModeContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/auth/withAuth */ \"./src/components/auth/withAuth.js\");\n/* harmony import */ var _components_WeatherComponent__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/WeatherComponent */ \"./src/components/WeatherComponent.jsx\");\n/* harmony import */ var _components_HassanaOfferPopUp__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/HassanaOfferPopUp */ \"./src/components/HassanaOfferPopUp.jsx\");\n/* harmony import */ var _components_VoiceModal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/VoiceModal */ \"./src/components/VoiceModal.jsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var _theme__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/theme */ \"./src/theme.js\");\n/* harmony import */ var _components_RateModal__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/RateModal */ \"./src/components/RateModal.js\");\n/* harmony import */ var _components_OfferCard__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/OfferCard */ \"./src/components/OfferCard.jsx\");\n/* harmony import */ var _components_VerticalScroller__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/VerticalScroller */ \"./src/components/VerticalScroller.jsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import withAuth from \"@/components/auth/withAuth\";\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CustomTabPanel(props) {\n    const { children, value, index, ...other } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        role: \"tabpanel\",\n        hidden: value !== index,\n        id: \"simple-tabpanel-\".concat(index),\n        \"aria-labelledby\": \"simple-tab-\".concat(index),\n        ...other,\n        children: value === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n            sx: {\n                p: 3\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n            lineNumber: 43,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = CustomTabPanel;\nCustomTabPanel.propTypes = {\n    children: (prop_types__WEBPACK_IMPORTED_MODULE_26___default().node),\n    index: (prop_types__WEBPACK_IMPORTED_MODULE_26___default().number).isRequired,\n    value: (prop_types__WEBPACK_IMPORTED_MODULE_26___default().number).isRequired\n};\nfunction a11yProps(index) {\n    return {\n        id: \"simple-tab-\".concat(index),\n        \"aria-controls\": \"simple-tabpanel-\".concat(index)\n    };\n}\nconst HomeComponent = ()=>{\n    _s();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_27__.useTheme)();\n    const { color } = (0,_components_ColorContext__WEBPACK_IMPORTED_MODULE_8__.useColor)();\n    const { smallScreen, mediumScreen, largeScreen, xLargeScreen, xxLargeScreen, xxxLargeScreen, xxxxLargeScreen } = _helper_mediaQueries__WEBPACK_IMPORTED_MODULE_10__.breakpoints;\n    const isSmallScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(smallScreen);\n    const isMediumScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(mediumScreen);\n    const isLargeScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(largeScreen);\n    const isXLargeScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(xLargeScreen);\n    const isXXLargeScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(xxLargeScreen);\n    const isXXXLargeScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(xxxLargeScreen);\n    const isXXXXLargeScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(xxxxLargeScreen);\n    let mainDivWidth;\n    if (isSmallScreen) {\n        mainDivWidth = \"70vw\";\n    } else if (isMediumScreen) {\n        mainDivWidth = \"70vw\";\n    } else if (isLargeScreen) {\n        mainDivWidth = \"77vw\";\n    } else if (isXLargeScreen) {\n        mainDivWidth = \"54vw\";\n    } else if (isXXLargeScreen) {\n        mainDivWidth = \"62vw\";\n    } else if (isXXXLargeScreen) {\n        mainDivWidth = \"67vw\";\n    } else if (isXXXXLargeScreen) {\n        mainDivWidth = \"70vw\";\n    } else {\n        mainDivWidth = \"70vw\";\n    }\n    const data = [\n        {\n            name: \"New IT Ticket\",\n            tag: \"Raise Task In Board\",\n            icon: \"/images/Ticket.png\",\n            link: \"https://support.hassana.com.sa/\",\n            target: \"_blank\"\n        },\n        {\n            name: \"Submit Leave\",\n            tag: \"Add Leave Request\",\n            icon: \"/images/clipboard.png\",\n            link: \"https://fa-exsq-saasfaprod1.fa.ocs.oraclecloud.com/hcmUI/faces/FndOverview?macKey=vLT97HMqpC6JvxLz&amp;_afrLoop=4116657012806712&amp;_afrWindowMode=0&amp;_afrWindowId=null&amp;_adf.ctrl-state=fnkgj5sn9_14&amp;_afrFS=16&amp;_afrMT=screen&amp;_afrMFW=1272&amp;_afrMFH=686&amp;_afrMFDW=1280&amp;_afrMFDH=800&amp;_afrMFC=8&amp;_afrMFCI=0&amp;_afrMFM=0&amp;_afrMFR=144&amp;_afrMFG=0&amp;_afrMFS=0&amp;_afrMFO=0\",\n            target: \"_blank\"\n        },\n        {\n            name: \"Your Voice Matters\",\n            tag: \"Innovative Ideas\",\n            icon: \"/images/your voice.png\",\n            link: \"https://forms.office.com/pages/responsepage.aspx?id=lZNxJq_6M0qt0PNs91rIfTyK2reu4A1OvI4rfIC09cRUN0syOEkwODdBSkdaQjUwRVJDQlRUWDZLVi4u&amp;web=1&amp;wdLOR=cA5E9AA54-B977-4DA5-9AEA-E9B6711C02BF\",\n            target: \"_blank\"\n        },\n        {\n            name: \"Thank You Program\",\n            tag: \"Gratitude in Action\",\n            icon: \"/images/like.png\",\n            link: \"/AddressBook\",\n            target: \"\"\n        }\n    ];\n    const data2 = [\n        {\n            name: \"New IT Ticket\",\n            tag: \"Raise Task In Board\",\n            icon: \"/icons/light-icon-button1.png\",\n            link: \"https://support.hassana.com.sa/\",\n            target: \"\"\n        },\n        {\n            name: \"Submit Leave\",\n            tag: \"Add Leave Request\",\n            icon: \"/icons/light-icon-button2.png\",\n            link: \"https://fa-exsq-saasfaprod1.fa.ocs.oraclecloud.com/hcmUI/faces/FndOverview?macKey=vLT97HMqpC6JvxLz&amp;_afrLoop=4116657012806712&amp;_afrWindowMode=0&amp;_afrWindowId=null&amp;_adf.ctrl-state=fnkgj5sn9_14&amp;_afrFS=16&amp;_afrMT=screen&amp;_afrMFW=1272&amp;_afrMFH=686&amp;_afrMFDW=1280&amp;_afrMFDH=800&amp;_afrMFC=8&amp;_afrMFCI=0&amp;_afrMFM=0&amp;_afrMFR=144&amp;_afrMFG=0&amp;_afrMFS=0&amp;_afrMFO=0\",\n            target: \"_blank\"\n        },\n        {\n            name: \"Your Voice Matters\",\n            tag: \"Innovative Ideas\",\n            icon: \"/icons/light-icon-button3.png\",\n            link: \"https://forms.office.com/pages/responsepage.aspx?id=lZNxJq_6M0qt0PNs91rIfTyK2reu4A1OvI4rfIC09cRUN0syOEkwODdBSkdaQjUwRVJDQlRUWDZLVi4u&amp;web=1&amp;wdLOR=cA5E9AA54-B977-4DA5-9AEA-E9B6711C02BF\",\n            target: \"_blank\"\n        },\n        {\n            name: \"Thank You Program\",\n            tag: \"Gratitude in Action\",\n            icon: \"/icons/light-icon-4.png\",\n            link: \"/AddressBook\",\n            target: \"\"\n        }\n    ];\n    const slides = [\n        {\n            title: \"Company Special Events at 10:30 pm\"\n        },\n        {\n            title: \"Company Special Events at 11:30 pm\"\n        },\n        {\n            title: \"Company Special Events at 12:30 pm\"\n        }\n    ];\n    // const context = useContext(ColorContext);\n    // const theme = useTheme();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_20__.useRouter)();\n    const selectedColor = (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_9__.useSelectedColor)(color);\n    const [hassanaPopup, setHassanaPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ideasModalOpen, setIdeasModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_19__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : session.user.role) === \"ADMIN\";\n    const handleThankYouProgram = ()=>{\n        router.push(\"/AddressBook\");\n    };\n    function showPopup() {\n        setHassanaPopup(true);\n    }\n    function closePopup() {\n        setHassanaPopup(false);\n    }\n    function openIdeasModal() {\n        setIdeasModalOpen(true);\n    }\n    function closeIdeasModal() {\n        setIdeasModalOpen(false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n            sx: {\n                width: mainDivWidth,\n                margin: \"auto\",\n                height: \"90vh\",\n                overflow: \"auto\",\n                overflow: \"auto\",\n                \"&::-webkit-scrollbar\": {\n                    width: 0\n                },\n                scrollbarWidth: \"none\",\n                msOverflowStyle: \"none\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                    sx: {\n                        marginY: \"20px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                        container: true,\n                        spacing: isSmallScreen ? 1 : 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Quote__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Celebration__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                    sx: {\n                        marginY: \"20px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                        container: true,\n                        spacing: isSmallScreen ? 1 : 3,\n                        children: (color != \"white\" ? data2 : data).map((btn, index)=>{\n                            console.log(\"in home\", color);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                                item: true,\n                                xs: 6,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                    sx: {\n                                        background: selectedColor == theme.palette.background.primary ? theme.palette.text.blue : selectedColor,\n                                        color: selectedColor == theme.palette.background.primary ? theme.palette.text.primary : theme.palette.text.white,\n                                        textAlign: \"left\",\n                                        width: \"100%\",\n                                        height: \"100%\",\n                                        cursor: \"default\",\n                                        borderRadius: \"10px\",\n                                        boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n                                        padding: isSmallScreen ? \"5px\" : \"10px\"\n                                    },\n                                    children: [\n                                        index != 3 && index != 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                            href: btn.link,\n                                            target: btn.target,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                sx: {\n                                                    display: \"flex\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                        sx: {\n                                                            flexGrow: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                                variant: \"body4\",\n                                                                fontWeight: \"900\",\n                                                                children: btn.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                                variant: \"subtitle2\",\n                                                                color: \"black\",\n                                                                children: btn.tag\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: btn.icon,\n                                                            loading: \"lazy\",\n                                                            width: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                            lineNumber: 285,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        index == 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                            sx: {\n                                                display: \"flex\",\n                                                cursor: \"pointer\"\n                                            },\n                                            onClick: openIdeasModal,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                    sx: {\n                                                        flexGrow: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                            variant: \"body4\",\n                                                            fontWeight: \"900\",\n                                                            children: btn.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                            variant: \"subtitle2\",\n                                                            color: \"black\",\n                                                            children: btn.tag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: btn.icon,\n                                                        loading: \"lazy\",\n                                                        width: 25\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        index === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                            sx: {\n                                                display: \"flex\",\n                                                cursor: \"pointer\",\n                                                background: _theme__WEBPACK_IMPORTED_MODULE_21__.lightTheme.palette.blue\n                                            },\n                                            onClick: handleThankYouProgram,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                    sx: {\n                                                        flexGrow: 1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                            variant: \"body4\",\n                                                            fontWeight: \"900\",\n                                                            children: btn.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Typography, {\n                                                            variant: \"subtitle2\",\n                                                            color: \"black\",\n                                                            children: btn.tag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: btn.icon,\n                                                        loading: \"lazy\",\n                                                        width: 25\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                            lineNumber: 324,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 264,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                    sx: {\n                        marginY: \"20px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                        container: true,\n                        spacing: isSmallScreen ? 1 : 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Schedule__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                                item: true,\n                                xs: 12,\n                                sm: 12,\n                                md: 6,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WeatherComponent__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LeaveTracker__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VoiceModal__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    open: ideasModalOpen,\n                    handleClose: closeIdeasModal\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Box, {\n                    sx: {\n                        marginY: \"20px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                        container: true,\n                        spacing: isSmallScreen ? 1 : 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__.Grid, {\n                            item: true,\n                            xs: 12,\n                            sm: 12,\n                            md: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewJoin__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                            lineNumber: 374,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(HomeComponent, \"Gr5r20gFxQDJHA9ENBblWk3POXQ=\", false, function() {\n    return [\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_27__.useTheme,\n        _components_ColorContext__WEBPACK_IMPORTED_MODULE_8__.useColor,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        next_router__WEBPACK_IMPORTED_MODULE_20__.useRouter,\n        _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_9__.useSelectedColor,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_19__.useSession\n    ];\n});\n_c1 = HomeComponent;\nconst Home = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HomeComponent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n            lineNumber: 388,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\index.js\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = Home;\n// export default Home;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c3 = (0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(Home));\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"CustomTabPanel\");\n$RefreshReg$(_c1, \"HomeComponent\");\n$RefreshReg$(_c2, \"Home\");\n$RefreshReg$(_c3, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.js\n"));

/***/ })

});