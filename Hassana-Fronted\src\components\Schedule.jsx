import { Box, Grid, Divider, Typography, useTheme } from "@mui/material";
import CustomSlider from "./CustomSlider";
import React, { useState } from "react";
// import './CustomScrollbar.css';
import useMediaQuery from "@mui/material/useMediaQuery";
import { breakpoints } from "@/helper/mediaQueries";
import { formatTime, getFormattedDate, monthName } from "./HelperFunctions";
import { useQuery } from "@apollo/client";
import { getTodaysEvents } from "@/Data/Events";
import BasicModal from "./Modal";
import { baseUrl } from "@/Data/ApolloClient";
import axios from "axios";
import { useEffect } from "react";
import { useSession } from "next-auth/react";

let base_url = baseUrl;
let exchange = "/exchange-info";
let id = "/210";

function filterTodaysEvents(events) {
  const today = new Date();
  const dateString = today.toISOString().split('T')[0];

  return events.filter(event => {
      const eventDate = event.start.split('T')[0];
      return eventDate === dateString;
  });
}


const Schedule = () => {
  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);
  const date = getFormattedDate();
  const { loading, error, data } = useQuery(getTodaysEvents, {
    // variables: { today: "2023-11-29" },
    variables: { today: date, category: "corporate" },
  });
  if (error) {
    console.log(error);
  }
  let eventData = [];
  data &&
    data.todaysEvents.forEach((element) => {
      console.log(eventData);
      eventData.push({
        title: element.title,
        description: element.details,
      });
    });

  const [value, setValue] = useState(0);
  const [schedules, setSchedules] = useState([]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const { data: session } = useSession();
  const [Data, setData] = useState(null);
  const [dataModal, setDataModal] = useState(false);
  useEffect(() => {
    const fetchSchedules = async () => {
      const user_id = session && session.user.id;
      console.log("user_id",user_id)
      try {
        const response = await axios.get(`${baseUrl}/exchange-info/${user_id}`);
        const transformedSchedules = response.data.map((item) => ({
          title: item.title,
          status: item.status,
          start: item.t_start,
          end: item.t_end,
          description: `Starts at ${formatTime(
            item.t_start
          )} and ends at ${formatTime(item.t_end)}`,
        }));
        setSchedules(filterTodaysEvents(transformedSchedules));
        // setSchedules(transformedSchedules);
        console.log(transformedSchedules);
      } catch (error) {
        console.error("Error fetching schedules:", error);
      }
    };

    fetchSchedules();
  }, []);



  return (
    <>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: "10px",
          boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
          padding: "20px",
          cursor: "default"
        }}
      >
        <Typography variant="h6" fontSize="1.3rem" fontWeight="700" marginX="5px">Schedule for the day</Typography>

        <Box
          sx={{
            // display: "flex",
            // justifyContent: "space-between",
            marginTop: "5px",
            // marginBottom: "2em",
          }}
        >
          <Grid container spacing={2} sx={{display:"flex",alignItems:"center", justifyContent: "space-between"}}>
            {/* Left Column */}
            <Grid item xs={12} sm={9} >
              <BasicModal
                Data={Data}
                dataModal={dataModal}
                setDataModal={setDataModal}
              />
              <CustomSlider
                slides={eventData}
                setData={setData}
                setDataModal={setDataModal}
                design={{
                  background: theme.palette.background.primary,
                  borderRadius: "20px",
                  padding: "20px",
                  margin: "0px 0px ",
                  maxWidth: isSmallScreen ? "100%" : isMediumScreen ? "600px" : "800px",
                  minHeight: "80px",
                  maxHeight: "110px",
                }}
              />
            </Grid>

            {/* Right Column */}
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: "center", marginX: "10px", marginY: "15px" }}>
                <Typography variant="h4" sx={{ color: theme.palette.text.purple, fontWeight: "600" }}>
                  {new Date().getDate()}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {monthName(new Date().getMonth())}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {new Date().getFullYear()}
                </Typography>
              </Box>
            </Grid>
          </Grid>


        </Box>
        <Box>
          <Divider sx={{ backgroundColor: "#F0DFFF", marginTop: "9px" }} />
        </Box>
        <Box sx={{ marginY: "20px" }}>
          <Box>


            <Box
              sx={{
                marginY: "10px",
                height: "254px",
                overflow: "auto",
              }}
            >
              {schedules.length > 0 ? (
                schedules.map((schedule, index) => {
                  return (
                    <Box key={index}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography
                          variant="h6"
                          fontSize="1.2rem"
                          // marginTop="12px"
                          fontWeight="600"
                          color={theme.palette.text.primary}
                        >
                          {schedule.title}
                        </Typography>
                      </Box>
                      <Divider
                        sx={{
                          height: "3px",
                          width: "40%",
                          background: theme.palette.text.green,
                        }}
                      />
                      <Typography variant="subtitle2"
                        sx={{
                          fontWeight: "500", fontSize: "14px",

                          // lineHeight: "2",
                          marginY: "17px",
                          color: theme.palette.text.secondary,
                        }}
                      >
                        {schedule.description}
                      </Typography>
                    </Box>
                  );
                })
              ) : (
                <Typography
                  sx={{
                    fontSize: "16px",
                    lineHeight: "2",
                    color: theme.palette.text.secondary,
                    textAlign: "center"
                  }}
                >
                  No schedules available.
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};
export default Schedule;
