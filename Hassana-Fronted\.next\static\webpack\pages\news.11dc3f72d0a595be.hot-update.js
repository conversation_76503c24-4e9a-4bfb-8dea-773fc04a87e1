"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/news",{

/***/ "./src/components/NewsSection.jsx":
/*!****************************************!*\
  !*** ./src/components/NewsSection.jsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/material/Button */ \"./node_modules/@mui/material/Button/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Resource__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/Data/Resource */ \"./src/Data/Resource.js\");\n/* harmony import */ var _Data_News__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/Data/News */ \"./src/Data/News.js\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/imageUtils */ \"./src/utils/imageUtils.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst NewsSection = (param)=>{\n    let { isWideScreen, isLargeTablet, isSmallTablet, isMediumMobile, news } = param;\n    _s();\n    // Function to limit summary text\n    const limitSummary = (summary)=>{\n        const words = summary.split(\" \");\n        if (words.length > 200) {\n            return words.slice(0, 200).join(\" \") + \"...\";\n        }\n        return summary;\n    };\n    // Fetch resources using useQuery\n    const { data, loading, error } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useQuery)(_Data_Resource__WEBPACK_IMPORTED_MODULE_4__.getResources);\n    // Construct featuredImage URL based on news prop\n    //const baseUrl = \"http://localhost:3001/v1/resource/v1/news/\";\n    const featuredImage = news && news.featuredImage;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            news && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    marginLeft: isSmallTablet ? \"15px\" : \"32px\",\n                    marginTop: \"20px\",\n                    flexDirection: isMediumMobile ? \"column\" : \"row\",\n                    gap: isWideScreen ? \"10px\" : \"0\"\n                },\n                children: [\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        style: {\n                            marginRight: \"16px\"\n                        },\n                        children: \"Loading image...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        style: {\n                            marginRight: \"16px\",\n                            color: \"red\"\n                        },\n                        children: [\n                            \"Failed to load image: \",\n                            error.message\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, undefined) : featuredImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__.getNewsImageUrl)(news.featuredImage),\n                        style: {\n                            marginRight: \"16px\",\n                            maxWidth: isMediumMobile ? \"100%\" : isLargeTablet ? \"45%\" : \"50%\",\n                            maxHeight: \"50%\",\n                            width: \"50%\"\n                        },\n                        alt: \"Featured News Image\",\n                        onError: (e)=>{\n                            console.log(\"Image failed to load:\", (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_6__.getNewsImageUrl)(news.featuredImage));\n                            e.target.style.display = \"none\";\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        style: {\n                            marginRight: \"16px\"\n                        },\n                        children: \"No image available\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            flexDirection: \"column\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                style: {\n                                    color: \"#00BC82\",\n                                    marginBottom: \"8px\"\n                                },\n                                children: (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(news.publication)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                style: {\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    marginBottom: \"8px\"\n                                },\n                                children: limitSummary(news.title)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: news.url,\n                                target: \"_blank\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"contained\",\n                                    style: {\n                                        borderRadius: 5,\n                                        padding: \"7.005px 17.514px\",\n                                        background: \"#62B6F3\",\n                                        color: \"#fff\",\n                                        fontSize: \"12.259px\",\n                                        fontFamily: \"Urbanist\",\n                                        maxWidth: \"150px\",\n                                        width: \"100%\"\n                                    },\n                                    children: \"Learn More\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-around\",\n                    padding: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            fontSize: {\n                                lg: \"10px\",\n                                md: \"8px\",\n                                sm: \"8px\",\n                                xs: \"7px\",\n                                xl: \"12px\"\n                            }\n                        },\n                        children: \"All News\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            fontSize: {\n                                lg: \"10px\",\n                                md: \"8px\",\n                                sm: \"8px\",\n                                xs: \"7px\",\n                                xl: \"12px\"\n                            }\n                        },\n                        children: \"Events\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Button__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            fontSize: {\n                                lg: \"10px\",\n                                md: \"8px\",\n                                sm: \"8px\",\n                                xs: \"7px\",\n                                xl: \"12px\"\n                            }\n                        },\n                        children: \"Discussion\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"Line8\",\n                        style: {\n                            width: \"50%\",\n                            height: \"0\",\n                            border: \"1px #00BC82 solid\",\n                            maxWidth: \"572px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\NewsSection.jsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(NewsSection, \"tP+6C5plfRwxqCbBj3cMUcL7Opk=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = NewsSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NewsSection);\nvar _c;\n$RefreshReg$(_c, \"NewsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9OZXdzU2VjdGlvbi5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDVTtBQUNjO0FBQ1I7QUFDYjtBQUN5QjtBQUNaO0FBQ0s7QUFDTjtBQUNZO0FBRXJELE1BQU1VLGNBQWM7UUFBQyxFQUNuQkMsWUFBWSxFQUNaQyxhQUFhLEVBQ2JDLGFBQWEsRUFDYkMsY0FBYyxFQUNkQyxJQUFJLEVBQ0w7O0lBQ0MsaUNBQWlDO0lBQ2pDLE1BQU1DLGVBQWUsQ0FBQ0M7UUFDcEIsTUFBTUMsUUFBUUQsUUFBUUUsS0FBSyxDQUFDO1FBQzVCLElBQUlELE1BQU1FLE1BQU0sR0FBRyxLQUFLO1lBQ3RCLE9BQU9GLE1BQU1HLEtBQUssQ0FBQyxHQUFHLEtBQUtDLElBQUksQ0FBQyxPQUFPO1FBQ3pDO1FBQ0EsT0FBT0w7SUFDVDtJQUVBLGlDQUFpQztJQUNqQyxNQUFNLEVBQUVNLElBQUksRUFBRUMsT0FBTyxFQUFFQyxLQUFLLEVBQUUsR0FBR25CLHdEQUFRQSxDQUFDQyx3REFBWUE7SUFFdEQsaURBQWlEO0lBQ2pELCtEQUErRDtJQUMvRCxNQUFNbUIsZ0JBQWdCWCxRQUFRQSxLQUFLVyxhQUFhO0lBR2hELHFCQUNFOztZQUNHWCxzQkFDQyw4REFBQ2QseURBQUdBO2dCQUNGMEIsT0FBTztvQkFDTEMsU0FBUztvQkFDVEMsWUFBWTtvQkFDWkMsWUFBWWpCLGdCQUFnQixTQUFTO29CQUNyQ2tCLFdBQVc7b0JBQ1hDLGVBQWVsQixpQkFBaUIsV0FBVztvQkFDM0NtQixLQUFLdEIsZUFBZSxTQUFTO2dCQUMvQjs7b0JBRUNhLHdCQUNDLDhEQUFDdEIsZ0VBQVVBO3dCQUFDeUIsT0FBTzs0QkFBRU8sYUFBYTt3QkFBTztrQ0FBRzs7Ozs7b0NBQzFDVCxzQkFDRiw4REFBQ3ZCLGdFQUFVQTt3QkFBQ3lCLE9BQU87NEJBQUVPLGFBQWE7NEJBQVFDLE9BQU87d0JBQU07OzRCQUFHOzRCQUNqQ1YsTUFBTVcsT0FBTzs7Ozs7O29DQUVwQ1YsOEJBQ0YsOERBQUNXO3dCQUNDQyxLQUFLN0Isa0VBQWVBLENBQUNNLEtBQUtXLGFBQWE7d0JBQ3ZDQyxPQUFPOzRCQUNMTyxhQUFhOzRCQUNiSyxVQUFVekIsaUJBQ04sU0FDQUYsZ0JBQ0EsUUFDQTs0QkFDSjRCLFdBQVc7NEJBQ1hDLE9BQU87d0JBQ1Q7d0JBQ0FDLEtBQUk7d0JBQ0pDLFNBQVMsQ0FBQ0M7NEJBQ1JDLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJyQyxrRUFBZUEsQ0FBQ00sS0FBS1csYUFBYTs0QkFDdkVrQixFQUFFRyxNQUFNLENBQUNwQixLQUFLLENBQUNDLE9BQU8sR0FBRzt3QkFDM0I7Ozs7O2tEQUdGLDhEQUFDMUIsZ0VBQVVBO3dCQUFDeUIsT0FBTzs0QkFBRU8sYUFBYTt3QkFBTztrQ0FBRzs7Ozs7O2tDQUk5Qyw4REFBQ2M7d0JBQ0NyQixPQUFPOzRCQUNMQyxTQUFTOzRCQUNUSSxlQUFlO3dCQUNqQjs7MENBRUEsOERBQUM5QixnRUFBVUE7Z0NBQUN5QixPQUFPO29DQUFFUSxPQUFPO29DQUFXYyxjQUFjO2dDQUFNOzBDQUN4RDVDLG1FQUFpQkEsQ0FBQ1UsS0FBS21DLFdBQVc7Ozs7OzswQ0FFckMsOERBQUNoRCxnRUFBVUE7Z0NBQ1R5QixPQUFPO29DQUNMd0IsVUFBVTtvQ0FDVkMsWUFBWTtvQ0FDWkgsY0FBYztnQ0FDaEI7MENBRUNqQyxhQUFhRCxLQUFLc0MsS0FBSzs7Ozs7OzBDQUUxQiw4REFBQ2pELGtEQUFJQTtnQ0FBQ2tELE1BQU12QyxLQUFLd0MsR0FBRztnQ0FBRVIsUUFBTzswQ0FDM0IsNEVBQUM1Qyw2REFBTUE7b0NBQ0xxRCxTQUFRO29DQUNSN0IsT0FBTzt3Q0FDTDhCLGNBQWM7d0NBQ2RDLFNBQVM7d0NBQ1RDLFlBQVk7d0NBQ1p4QixPQUFPO3dDQUNQZ0IsVUFBVTt3Q0FDVlMsWUFBWTt3Q0FDWnJCLFVBQVU7d0NBQ1ZFLE9BQU87b0NBQ1Q7OENBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9ULDhEQUFDeEMseURBQUdBO2dCQUNGMEIsT0FBTztvQkFDTEMsU0FBUztvQkFDVEMsWUFBWTtvQkFDWmdDLGdCQUFnQjtvQkFDaEJILFNBQVM7Z0JBQ1g7O2tDQUVBLDhEQUFDdkQsNkRBQU1BO3dCQUNMMkQsSUFBSTs0QkFDRlgsVUFBVTtnQ0FDUlksSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTs0QkFDTjt3QkFDRjtrQ0FDRDs7Ozs7O2tDQUdELDhEQUFDaEUsNkRBQU1BO3dCQUNMMkQsSUFBSTs0QkFDRlgsVUFBVTtnQ0FDUlksSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTs0QkFDTjt3QkFDRjtrQ0FDRDs7Ozs7O2tDQUdELDhEQUFDaEUsNkRBQU1BO3dCQUNMMkQsSUFBSTs0QkFDRlgsVUFBVTtnQ0FDUlksSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTs0QkFDTjt3QkFDRjtrQ0FDRDs7Ozs7O2tDQUdELDhEQUFDbkI7d0JBQ0NvQixXQUFVO3dCQUNWekMsT0FBTzs0QkFDTGMsT0FBTzs0QkFDUDRCLFFBQVE7NEJBQ1JDLFFBQVE7NEJBQ1IvQixVQUFVO3dCQUNaOzs7Ozs7Ozs7Ozs7OztBQUtWO0dBcEtNN0I7O1FBaUI2Qkosb0RBQVFBOzs7S0FqQnJDSTtBQXNLTiwrREFBZUEsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9OZXdzU2VjdGlvbi5qc3g/MTFiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBCb3ggZnJvbSBcIkBtdWkvbWF0ZXJpYWwvQm94XCI7XHJcbmltcG9ydCBUeXBvZ3JhcGh5IGZyb20gXCJAbXVpL21hdGVyaWFsL1R5cG9ncmFwaHlcIjtcclxuaW1wb3J0IEJ1dHRvbiBmcm9tIFwiQG11aS9tYXRlcmlhbC9CdXR0b25cIjtcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgeyBmb3JtYXREYXRlVGltZVVUQyB9IGZyb20gXCIuL0hlbHBlckZ1bmN0aW9uc1wiO1xyXG5pbXBvcnQgeyB1c2VRdWVyeSB9IGZyb20gXCJAYXBvbGxvL2NsaWVudFwiO1xyXG5pbXBvcnQgeyBnZXRSZXNvdXJjZXMgfSBmcm9tIFwiQC9EYXRhL1Jlc291cmNlXCI7XHJcbmltcG9ydCB7IGdldEFsbE5ld3MgfSBmcm9tIFwiQC9EYXRhL05ld3NcIjtcclxuaW1wb3J0IHsgZ2V0TmV3c0ltYWdlVXJsIH0gZnJvbSBcIkAvdXRpbHMvaW1hZ2VVdGlsc1wiO1xyXG5cclxuY29uc3QgTmV3c1NlY3Rpb24gPSAoe1xyXG4gIGlzV2lkZVNjcmVlbixcclxuICBpc0xhcmdlVGFibGV0LFxyXG4gIGlzU21hbGxUYWJsZXQsXHJcbiAgaXNNZWRpdW1Nb2JpbGUsXHJcbiAgbmV3cyxcclxufSkgPT4ge1xyXG4gIC8vIEZ1bmN0aW9uIHRvIGxpbWl0IHN1bW1hcnkgdGV4dFxyXG4gIGNvbnN0IGxpbWl0U3VtbWFyeSA9IChzdW1tYXJ5KSA9PiB7XHJcbiAgICBjb25zdCB3b3JkcyA9IHN1bW1hcnkuc3BsaXQoXCIgXCIpO1xyXG4gICAgaWYgKHdvcmRzLmxlbmd0aCA+IDIwMCkge1xyXG4gICAgICByZXR1cm4gd29yZHMuc2xpY2UoMCwgMjAwKS5qb2luKFwiIFwiKSArIFwiLi4uXCI7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gc3VtbWFyeTtcclxuICB9O1xyXG5cclxuICAvLyBGZXRjaCByZXNvdXJjZXMgdXNpbmcgdXNlUXVlcnlcclxuICBjb25zdCB7IGRhdGEsIGxvYWRpbmcsIGVycm9yIH0gPSB1c2VRdWVyeShnZXRSZXNvdXJjZXMpO1xyXG5cclxuICAvLyBDb25zdHJ1Y3QgZmVhdHVyZWRJbWFnZSBVUkwgYmFzZWQgb24gbmV3cyBwcm9wXHJcbiAgLy9jb25zdCBiYXNlVXJsID0gXCJodHRwOi8vbG9jYWxob3N0OjMwMDEvdjEvcmVzb3VyY2UvdjEvbmV3cy9cIjtcclxuICBjb25zdCBmZWF0dXJlZEltYWdlID0gbmV3cyAmJiBuZXdzLmZlYXR1cmVkSW1hZ2VcclxuICAgXHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICB7bmV3cyAmJiAoXHJcbiAgICAgICAgPEJveFxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgIG1hcmdpbkxlZnQ6IGlzU21hbGxUYWJsZXQgPyBcIjE1cHhcIiA6IFwiMzJweFwiLFxyXG4gICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICBmbGV4RGlyZWN0aW9uOiBpc01lZGl1bU1vYmlsZSA/IFwiY29sdW1uXCIgOiBcInJvd1wiLFxyXG4gICAgICAgICAgICBnYXA6IGlzV2lkZVNjcmVlbiA/IFwiMTBweFwiIDogXCIwXCIsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeSBzdHlsZT17eyBtYXJnaW5SaWdodDogXCIxNnB4XCIgfX0+TG9hZGluZyBpbWFnZS4uLjwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICkgOiBlcnJvciA/IChcclxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgc3R5bGU9e3sgbWFyZ2luUmlnaHQ6IFwiMTZweFwiLCBjb2xvcjogXCJyZWRcIiB9fT5cclxuICAgICAgICAgICAgICBGYWlsZWQgdG8gbG9hZCBpbWFnZToge2Vycm9yLm1lc3NhZ2V9XHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICkgOiBmZWF0dXJlZEltYWdlID8gKFxyXG4gICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgc3JjPXtnZXROZXdzSW1hZ2VVcmwobmV3cy5mZWF0dXJlZEltYWdlKX1cclxuICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luUmlnaHQ6IFwiMTZweFwiLFxyXG4gICAgICAgICAgICAgICAgbWF4V2lkdGg6IGlzTWVkaXVtTW9iaWxlXHJcbiAgICAgICAgICAgICAgICAgID8gXCIxMDAlXCJcclxuICAgICAgICAgICAgICAgICAgOiBpc0xhcmdlVGFibGV0XHJcbiAgICAgICAgICAgICAgICAgID8gXCI0NSVcIlxyXG4gICAgICAgICAgICAgICAgICA6IFwiNTAlXCIsXHJcbiAgICAgICAgICAgICAgICBtYXhIZWlnaHQ6IFwiNTAlXCIsXHJcbiAgICAgICAgICAgICAgICB3aWR0aDogXCI1MCVcIixcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIGFsdD1cIkZlYXR1cmVkIE5ld3MgSW1hZ2VcIlxyXG4gICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIkltYWdlIGZhaWxlZCB0byBsb2FkOlwiLCBnZXROZXdzSW1hZ2VVcmwobmV3cy5mZWF0dXJlZEltYWdlKSk7XHJcbiAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8VHlwb2dyYXBoeSBzdHlsZT17eyBtYXJnaW5SaWdodDogXCIxNnB4XCIgfX0+XHJcbiAgICAgICAgICAgICAgTm8gaW1hZ2UgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgZmxleERpcmVjdGlvbjogXCJjb2x1bW5cIixcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgc3R5bGU9e3sgY29sb3I6IFwiIzAwQkM4MlwiLCBtYXJnaW5Cb3R0b206IFwiOHB4XCIgfX0+XHJcbiAgICAgICAgICAgICAge2Zvcm1hdERhdGVUaW1lVVRDKG5ld3MucHVibGljYXRpb24pfVxyXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE0cHhcIixcclxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiNTAwXCIsXHJcbiAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtsaW1pdFN1bW1hcnkobmV3cy50aXRsZSl9XHJcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgPExpbmsgaHJlZj17bmV3cy51cmx9IHRhcmdldD1cIl9ibGFua1wiPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjb250YWluZWRcIlxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA1LFxyXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjcuMDA1cHggMTcuNTE0cHhcIixcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogXCIjNjJCNkYzXCIsXHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiNmZmZcIixcclxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTIuMjU5cHhcIixcclxuICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCJVcmJhbmlzdFwiLFxyXG4gICAgICAgICAgICAgICAgICBtYXhXaWR0aDogXCIxNTBweFwiLFxyXG4gICAgICAgICAgICAgICAgICB3aWR0aDogXCIxMDAlXCIsXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIExlYXJuIE1vcmVcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgICl9XHJcbiAgICAgIDxCb3hcclxuICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYXJvdW5kXCIsXHJcbiAgICAgICAgICBwYWRkaW5nOiBcIjEwcHhcIixcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgZm9udFNpemU6IHtcclxuICAgICAgICAgICAgICBsZzogXCIxMHB4XCIsXHJcbiAgICAgICAgICAgICAgbWQ6IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgc206IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgeHM6IFwiN3B4XCIsXHJcbiAgICAgICAgICAgICAgeGw6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICBBbGwgTmV3c1xyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIGZvbnRTaXplOiB7XHJcbiAgICAgICAgICAgICAgbGc6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgIG1kOiBcIjhweFwiLFxyXG4gICAgICAgICAgICAgIHNtOiBcIjhweFwiLFxyXG4gICAgICAgICAgICAgIHhzOiBcIjdweFwiLFxyXG4gICAgICAgICAgICAgIHhsOiBcIjEycHhcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgRXZlbnRzXHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgZm9udFNpemU6IHtcclxuICAgICAgICAgICAgICBsZzogXCIxMHB4XCIsXHJcbiAgICAgICAgICAgICAgbWQ6IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgc206IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgeHM6IFwiN3B4XCIsXHJcbiAgICAgICAgICAgICAgeGw6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICBEaXNjdXNzaW9uXHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiTGluZThcIlxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgd2lkdGg6IFwiNTAlXCIsXHJcbiAgICAgICAgICAgIGhlaWdodDogXCIwXCIsXHJcbiAgICAgICAgICAgIGJvcmRlcjogXCIxcHggIzAwQkM4MiBzb2xpZFwiLFxyXG4gICAgICAgICAgICBtYXhXaWR0aDogXCI1NzJweFwiLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+PC9kaXY+XHJcbiAgICAgIDwvQm94PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IE5ld3NTZWN0aW9uOyJdLCJuYW1lcyI6WyJSZWFjdCIsIkJveCIsIlR5cG9ncmFwaHkiLCJCdXR0b24iLCJMaW5rIiwiZm9ybWF0RGF0ZVRpbWVVVEMiLCJ1c2VRdWVyeSIsImdldFJlc291cmNlcyIsImdldEFsbE5ld3MiLCJnZXROZXdzSW1hZ2VVcmwiLCJOZXdzU2VjdGlvbiIsImlzV2lkZVNjcmVlbiIsImlzTGFyZ2VUYWJsZXQiLCJpc1NtYWxsVGFibGV0IiwiaXNNZWRpdW1Nb2JpbGUiLCJuZXdzIiwibGltaXRTdW1tYXJ5Iiwic3VtbWFyeSIsIndvcmRzIiwic3BsaXQiLCJsZW5ndGgiLCJzbGljZSIsImpvaW4iLCJkYXRhIiwibG9hZGluZyIsImVycm9yIiwiZmVhdHVyZWRJbWFnZSIsInN0eWxlIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJtYXJnaW5MZWZ0IiwibWFyZ2luVG9wIiwiZmxleERpcmVjdGlvbiIsImdhcCIsIm1hcmdpblJpZ2h0IiwiY29sb3IiLCJtZXNzYWdlIiwiaW1nIiwic3JjIiwibWF4V2lkdGgiLCJtYXhIZWlnaHQiLCJ3aWR0aCIsImFsdCIsIm9uRXJyb3IiLCJlIiwiY29uc29sZSIsImxvZyIsInRhcmdldCIsImRpdiIsIm1hcmdpbkJvdHRvbSIsInB1YmxpY2F0aW9uIiwiZm9udFNpemUiLCJmb250V2VpZ2h0IiwidGl0bGUiLCJocmVmIiwidXJsIiwidmFyaWFudCIsImJvcmRlclJhZGl1cyIsInBhZGRpbmciLCJiYWNrZ3JvdW5kIiwiZm9udEZhbWlseSIsImp1c3RpZnlDb250ZW50Iiwic3giLCJsZyIsIm1kIiwic20iLCJ4cyIsInhsIiwiY2xhc3NOYW1lIiwiaGVpZ2h0IiwiYm9yZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/NewsSection.jsx\n"));

/***/ })

});