"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/pages/notifications.js":
/*!************************************!*\
  !*** ./src/pages/notifications.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Divider */ \"./node_modules/@mui/material/Divider/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NotificationButtons */ \"./src/components/NotificationButtons.jsx\");\n/* harmony import */ var _components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AnnouncementCard */ \"./src/components/AnnouncementCard.js\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/NotificationCard */ \"./src/components/NotificationCard.jsx\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Dashboard */ \"./src/components/Dashboard.jsx\");\n/* harmony import */ var _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _Data_Events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/Data/Events */ \"./src/Data/Events.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Booking__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Booking */ \"./src/Data/Booking.js\");\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/auth/withAuth */ \"./src/components/auth/withAuth.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ResponsiveText = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(\"div\")((param)=>{\n    let { theme } = param;\n    return {\n        color: \"#1B3745\",\n        fontSize: \"16.02px\",\n        fontFamily: \"Helvetica\",\n        fontWeight: 400,\n        wordWrap: \"break-word\",\n        maxWidth: \"100%\"\n    };\n});\nconst ResponsiveBox = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box)((param)=>{\n    let { theme, backgroundColor, disableBorder, isCurrentNotification } = param;\n    return {\n        width: \"100%\",\n        height: \"auto !important\",\n        background: backgroundColor || \"white\",\n        backgroundColor: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : theme.palette.background.secondary,\n        boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.05)\",\n        borderRadius: 10,\n        position: \"relative\",\n        \"&::before\": !disableBorder && {\n            content: '\"\"',\n            position: \"absolute\",\n            top: 0,\n            left: 0.6,\n            width: \"5px\",\n            height: \"100%\",\n            background: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : isCurrentNotification ? \"linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)\" : \"none\",\n            borderRadius: \"20px 0 0 20px\"\n        }\n    };\n});\n_c = ResponsiveBox;\nconst Notifications = ()=>{\n    var _notificationData_notifications;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const { notificationId } = router.query; // Get notificationId from query params\n    const { loading: eventLoading, data: eventData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Events__WEBPACK_IMPORTED_MODULE_8__.getEvents);\n    const { loading: notificationLoading, data: notificationData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications);\n    const { loading: bookingLoading, data: bookingData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Booking__WEBPACK_IMPORTED_MODULE_10__.getBookings);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openModal, setOpenModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAnnouncement, setSelectedAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meetings, setMeetings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeButton, setActiveButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const isWideScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1150px)\");\n    const isLargeTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1079px)\");\n    const isTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1060px)\");\n    const isSmallTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:967px)\");\n    const isWideMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:869px)\");\n    const isMediumMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:823px)\");\n    const isNarrowMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:528px)\");\n    const isStandardDesktop = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1439px)\");\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme)();\n    const { color } = (0,_components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const selectedColor = (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor)(color);\n    // Handle navigation from query params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (notificationId && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            // Find the notification directly in notifications data\n            const notification = notificationData.notifications.find((notif)=>notif.id === notificationId);\n            if (notification) {\n                // Switch to notifications tab and select the notification\n                setDisplay(\"notifications\");\n                setActiveButton(\"notifications\");\n                // Find the index in the notifications array\n                const notifIndex = notificationData.notifications.findIndex((notif)=>notif.id === notificationId);\n                // Adjust index based on how notifications are displayed (newest first)\n                const adjustedIndex = notifIndex === notificationData.notifications.length - 1 ? 0 : notificationData.notifications.length - 1 - notifIndex;\n                setSelectedCard(adjustedIndex);\n                setSelectedAnnouncement(notification);\n            }\n        }\n    }, [\n        notificationId,\n        notificationData\n    ]);\n    const handleButtonClick = (buttonType)=>{\n        setDisplay(buttonType);\n        setActiveButton(buttonType);\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!notificationLoading && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        notificationLoading,\n        notificationData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (eventData && eventData.events) {\n            setEvents(eventData.events);\n        }\n        if (bookingData && bookingData.bookings) {\n            setMeetings(bookingData.bookings);\n        }\n        if (notificationData && notificationData.notifications) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        eventData,\n        bookingData,\n        notificationData\n    ]);\n    const handleCardClick = (index)=>{\n        setSelectedCard(index);\n        let selectedItem = null;\n        if (display === \"all\") {\n            selectedItem = combinedItems[index];\n        } else if (display === \"events\") {\n            selectedItem = index < currentEvents.length ? currentEvents[index] : previousEvents[index - currentEvents.length];\n        } else if (display === \"notifications\") {\n            selectedItem = index === 0 ? notifications[notifications.length - 1] : notifications.slice(0, -1)[index - 1];\n        } else if (display === \"meetings\") {\n            selectedItem = index < upcomingMeetings.length ? upcomingMeetings[index] : previousMeetings[index - upcomingMeetings.length];\n        }\n        setSelectedAnnouncement(selectedItem);\n    };\n    const handleOpenModal = (announcement)=>{\n        setSelectedAnnouncement(announcement);\n        setOpenModal(true);\n    };\n    const handleReset = ()=>{\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n        // Remove notificationId from URL\n        router.push(\"/notifications\", undefined, {\n            shallow: true\n        });\n    };\n    const handleCloseModal = ()=>{\n        setOpenModal(false);\n        setSelectedAnnouncement(null);\n    };\n    const currentDate = new Date();\n    const startOfWeek = currentDate.getDate() - currentDate.getDay();\n    const endOfWeek = startOfWeek + 6;\n    const currentWeekStart = new Date(currentDate);\n    currentWeekStart.setDate(startOfWeek);\n    const currentWeekEnd = new Date(currentDate);\n    currentWeekEnd.setDate(endOfWeek);\n    const currentEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate >= currentWeekStart && eventDate <= currentWeekEnd;\n    });\n    const previousEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate < currentWeekStart;\n    });\n    const upcomingMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) >= currentDate).sort((a, b)=>new Date(a.startTime) - new Date(b.startTime));\n    const previousMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) < currentDate).sort((a, b)=>new Date(b.startTime) - new Date(a.startTime));\n    const currentNotifications = notifications.filter((notification)=>new Date(notification.createdAt) >= currentWeekStart).sort((a, b)=>new Date(a.createdAt) - new Date(b.createdAt));\n    const previousNotifications = notifications.filter((notification)=>new Date(notification.createdAt) < currentWeekStart).sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n    const combinedItems = [\n        ...events.map((item)=>({\n                ...item,\n                type: \"event\"\n            })),\n        ...meetings.map((item)=>({\n                ...item,\n                type: \"meeting\"\n            })),\n        ...notifications.map((item)=>({\n                ...item,\n                type: \"notification\"\n            }))\n    ].sort((a, b)=>{\n        const dateA = a.createdAt || a.startTime || a.date;\n        const dateB = b.createdAt || b.startTime || b.date;\n        return new Date(dateB) - new Date(dateA);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                sx: {\n                    display: \"flex\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                    component: \"main\",\n                    sx: {\n                        background: selectedColor === theme.palette.background.primary ? theme.palette.background.secondary : selectedColor,\n                        paddingLeft: isLargeTablet ? \"50px\" : \"\",\n                        paddingRight: isLargeTablet ? \"50px\" : \"\",\n                        width: isMediumMobile ? \"100%\" : isWideMobile ? \"45rem\" : isSmallTablet ? \"48rem\" : isTablet ? \"54rem\" : \"60vw\",\n                        margin: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginLeft: \"15px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                        variant: \"h5\",\n                                        style: {\n                                            marginTop: \"32px\",\n                                            fontSize: \"16.024px\",\n                                            marginLeft: isStandardDesktop ? \"16px\" : \"\"\n                                        },\n                                        children: \"Latest updates from Hassana\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"h1\",\n                                            style: {\n                                                fontSize: isWideScreen ? \"22px\" : \"27.47px\",\n                                                fontWeight: \"700\",\n                                                marginTop: \"0\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"15px\"\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                paddingRight: \"25px\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"20px\",\n                                                marginTop: \"20px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                handleButtonClick: handleButtonClick,\n                                                activeButton: activeButton,\n                                                isNarrowMobile: isNarrowMobile,\n                                                isWideScreen: isWideScreen\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedCard !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    onClick: handleReset,\n                                    style: {\n                                        marginLeft: isStandardDesktop ? \"20px\" : \"5px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\",\n                                        color: \"#888\",\n                                        gap: 5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icons/arrow-left.svg\",\n                                            alt: \"arrow\",\n                                            height: 16,\n                                            width: true,\n                                            parametern: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"8px\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            sx: {\n                                                width: \"15px\",\n                                                backgroundColor: \"#A7A7A7\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginTop: \"2px\"\n                                            },\n                                            children: (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 341,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"4px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                padding: \"3%\",\n                                height: \"70vh\",\n                                overflow: \"auto\",\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 12,\n                                lg: 12,\n                                order: {\n                                    xs: 2,\n                                    sm: 2,\n                                    md: 2,\n                                    lg: 1,\n                                    xl: 1\n                                },\n                                children: [\n                                    display === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading || eventLoading || bookingLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 389,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: combinedItems.map((item, index)=>{\n                                                const isEvent = item.type === \"event\";\n                                                const isMeeting = item.type === \"meeting\";\n                                                const isNotification = item.type === \"notification\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: isNotification,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\",\n                                                        background: isNotification || isMeeting ? theme.palette.background.secondary : undefined\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        isEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.date : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isMeeting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.startTime : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isNotification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: item.notification,\n                                                            isSelected: selectedCard === index,\n                                                            isCurrentNotification: true,\n                                                            showDate: selectedCard === index,\n                                                            date: selectedCard === index ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(item.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 29\n                                                }, undefined);\n                                            })\n                                        }, void 0, false)\n                                    }, void 0, false),\n                                    display === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            currentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        isCurrentNotification: true,\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        date: selectedCard === index ? event.date : null,\n                                                        isSelected: selectedCard === index\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Events\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 481,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + currentEvents.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + currentEvents.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + currentEvents.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        isSelected: selectedCard === index + currentEvents.length,\n                                                        date: selectedCard === index + currentEvents.length ? event.date : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true),\n                                    display === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 543,\n                                            columnNumber: 23\n                                        }, undefined) : (notificationData === null || notificationData === void 0 ? void 0 : (_notificationData_notifications = notificationData.notifications) === null || _notificationData_notifications === void 0 ? void 0 : _notificationData_notifications.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"16px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== 0 ? \"none\" : \"block\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        notification: notificationData.notifications[notificationData.notifications.length - 1].notification,\n                                                        isSelected: selectedCard === 0,\n                                                        isCurrentNotification: true,\n                                                        showDate: selectedCard === 0,\n                                                        date: selectedCard === 0 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notificationData.notifications[notificationData.notifications.length - 1].createdAt)) : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                notificationData.notifications.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    marginBottom: \"24px\",\n                                                    marginTop: \"24px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderBottom: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Previous Notifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderTop: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                notificationData.notifications.slice(0, -1).map((notification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                        sx: {\n                                                            background: theme.palette.background.secondary,\n                                                            padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                            marginTop: \"20px\",\n                                                            display: selectedCard !== null && selectedCard !== index + 1 ? \"none\" : \"block\",\n                                                            height: selectedCard === index + 1 ? \"auto\" : \"170px\"\n                                                        },\n                                                        onClick: ()=>handleCardClick(index + 1),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: notification.notification,\n                                                            isSelected: selectedCard === index + 1,\n                                                            isCurrentNotification: false,\n                                                            showDate: selectedCard === index + 1,\n                                                            date: selectedCard === index + 1 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notification.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, notification.id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"No notifications available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 603,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false),\n                                    display === \"meetings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            upcomingMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Upcoming Meetings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: meeting.title,\n                                                            details: meeting.details,\n                                                            date: selectedCard === index ? meeting.startTime : null,\n                                                            isSelected: selectedCard === index,\n                                                            onClick: ()=>handleOpenModal(meeting)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, meeting.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousMeetings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Meetings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 649,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + upcomingMeetings.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + upcomingMeetings.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + upcomingMeetings.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: meeting.title,\n                                                        details: meeting.details,\n                                                        date: selectedCard === index + upcomingMeetings.length ? meeting.startTime : null,\n                                                        isSelected: selectedCard === index + upcomingMeetings.length,\n                                                        onClick: ()=>handleOpenModal(meeting)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, meeting.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                            open: openModal,\n                            onClose: handleCloseModal,\n                            maxWidth: \"sm\",\n                            fullWidth: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                    children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body1\",\n                                            children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.notification) || (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.details) || \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Start Time: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.createdAt))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 734,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 719,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogActions, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                        onClick: handleCloseModal,\n                                        color: \"primary\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 740,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 739,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 715,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(Notifications, \"k15HFFbkW2IpBjetlj1ltXnmtLg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme,\n        _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor\n    ];\n});\n_c1 = Notifications;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c2 = (0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(Notifications));\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResponsiveBox\");\n$RefreshReg$(_c1, \"Notifications\");\n$RefreshReg$(_c2, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvbm90aWZpY2F0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXFDO0FBQ2M7QUFDSztBQVVqQztBQUMyQjtBQUNOO0FBQ1k7QUFDZDtBQUN5QjtBQUNMO0FBQ0E7QUFDZjtBQUNNO0FBQ1c7QUFDdEI7QUFDYTtBQUNvQjtBQUM5QjtBQUNLO0FBQ1Y7QUFFeEMsTUFBTStCLGlCQUFpQjNCLDZEQUFNQSxDQUFDLE9BQU87UUFBQyxFQUFFNEIsS0FBSyxFQUFFO1dBQU07UUFDbkRDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxVQUFVO0lBQ1o7QUFBQTtBQUVBLE1BQU1DLGdCQUFnQm5DLDZEQUFNQSxDQUFDRSwrSUFBR0EsRUFDOUI7UUFBQyxFQUFFMEIsS0FBSyxFQUFFUSxlQUFlLEVBQUVDLGFBQWEsRUFBRUMscUJBQXFCLEVBQUU7V0FBTTtRQUNyRUMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFlBQVlMLG1CQUFtQjtRQUMvQkEsaUJBQWlCLENBQUNFLHdCQUNkVixNQUFNYyxPQUFPLENBQUNDLElBQUksS0FBSyxVQUNyQixZQUNBZixNQUFNYyxPQUFPLENBQUNELFVBQVUsQ0FBQ0csU0FBUyxHQUNwQ2hCLE1BQU1jLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDRyxTQUFTO1FBQ3RDQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsVUFBVTtRQUNWLGFBQWEsQ0FBQ1YsaUJBQWlCO1lBQzdCVyxTQUFTO1lBQ1RELFVBQVU7WUFDVkUsS0FBSztZQUNMQyxNQUFNO1lBQ05YLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxZQUFZLENBQUNILHdCQUNUVixNQUFNYyxPQUFPLENBQUNDLElBQUksS0FBSyxVQUNyQixZQUNBZixNQUFNYyxPQUFPLENBQUNELFVBQVUsQ0FBQ0csU0FBUyxHQUNwQ04sd0JBQ0csd0RBQ0Q7WUFDTlEsY0FBYztRQUNoQjtJQUNGO0FBQUE7S0E3QklYO0FBZ0NOLE1BQU1nQixnQkFBZ0I7UUF3ZEVDOztJQXZkdEIsTUFBTUMsU0FBUzNCLHVEQUFTQTtJQUN4QixNQUFNLEVBQUU0QixjQUFjLEVBQUUsR0FBR0QsT0FBT0UsS0FBSyxFQUFFLHVDQUF1QztJQUNoRixNQUFNLEVBQUVDLFNBQVNDLFlBQVksRUFBRUMsTUFBTUMsU0FBUyxFQUFFLEdBQUc5Qyx5REFBUUEsQ0FBQ08sbURBQVNBO0lBQ3JFLE1BQU0sRUFBRW9DLFNBQVNJLG1CQUFtQixFQUFFRixNQUFNTixnQkFBZ0IsRUFBRSxHQUFHdkMseURBQVFBLENBQUNRLGdFQUFnQkE7SUFDMUYsTUFBTSxFQUFFbUMsU0FBU0ssY0FBYyxFQUFFSCxNQUFNSSxXQUFXLEVBQUUsR0FBR2pELHlEQUFRQSxDQUFDVyx1REFBV0E7SUFFM0UsTUFBTSxDQUFDdUMsY0FBY0MsZ0JBQWdCLEdBQUdsRSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNtRSxXQUFXQyxhQUFhLEdBQUdwRSwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNxRSxzQkFBc0JDLHdCQUF3QixHQUFHdEUsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDdUUsUUFBUUMsVUFBVSxHQUFHeEUsK0NBQVFBLENBQUMsRUFBRTtJQUN2QyxNQUFNLENBQUN5RSxlQUFlQyxpQkFBaUIsR0FBRzFFLCtDQUFRQSxDQUFDLEVBQUU7SUFDckQsTUFBTSxDQUFDMkUsVUFBVUMsWUFBWSxHQUFHNUUsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUM2RSxjQUFjQyxnQkFBZ0IsR0FBRzlFLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQytFLFNBQVNDLFdBQVcsR0FBR2hGLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU1pRixlQUFlbkUsd0VBQWFBLENBQUM7SUFDbkMsTUFBTW9FLGdCQUFnQnBFLHdFQUFhQSxDQUFDO0lBQ3BDLE1BQU1xRSxXQUFXckUsd0VBQWFBLENBQUM7SUFDL0IsTUFBTXNFLGdCQUFnQnRFLHdFQUFhQSxDQUFDO0lBQ3BDLE1BQU11RSxlQUFldkUsd0VBQWFBLENBQUM7SUFDbkMsTUFBTXdFLGlCQUFpQnhFLHdFQUFhQSxDQUFDO0lBQ3JDLE1BQU15RSxpQkFBaUJ6RSx3RUFBYUEsQ0FBQztJQUNyQyxNQUFNMEUsb0JBQW9CMUUsd0VBQWFBLENBQUM7SUFFeEMsTUFBTWdCLFFBQVEzQiwrREFBUUE7SUFDdEIsTUFBTSxFQUFFNEIsS0FBSyxFQUFFLEdBQUdYLGtFQUFRQTtJQUMxQixNQUFNcUUsZ0JBQWdCcEUsNkVBQWdCQSxDQUFDVTtJQUV2QyxzQ0FBc0M7SUFDdEM5QixnREFBU0EsQ0FBQztRQUNSLElBQUl1RCxtQkFBa0JGLDZCQUFBQSx1Q0FBQUEsaUJBQWtCbUIsYUFBYSxHQUFFO1lBQ3JELHVEQUF1RDtZQUN2RCxNQUFNaUIsZUFBZXBDLGlCQUFpQm1CLGFBQWEsQ0FBQ2tCLElBQUksQ0FDdEQsQ0FBQ0MsUUFBVUEsTUFBTUMsRUFBRSxLQUFLckM7WUFHMUIsSUFBSWtDLGNBQWM7Z0JBQ2hCLDBEQUEwRDtnQkFDMURWLFdBQVc7Z0JBQ1hGLGdCQUFnQjtnQkFFaEIsNENBQTRDO2dCQUM1QyxNQUFNZ0IsYUFBYXhDLGlCQUFpQm1CLGFBQWEsQ0FBQ3NCLFNBQVMsQ0FDekQsQ0FBQ0gsUUFBVUEsTUFBTUMsRUFBRSxLQUFLckM7Z0JBRzFCLHVFQUF1RTtnQkFDdkUsTUFBTXdDLGdCQUFnQkYsZUFBZXhDLGlCQUFpQm1CLGFBQWEsQ0FBQ3dCLE1BQU0sR0FBRyxJQUFJLElBQUkzQyxpQkFBaUJtQixhQUFhLENBQUN3QixNQUFNLEdBQUcsSUFBSUg7Z0JBRWpJNUIsZ0JBQWdCOEI7Z0JBQ2hCMUIsd0JBQXdCb0I7WUFDMUI7UUFDRjtJQUNGLEdBQUc7UUFBQ2xDO1FBQWdCRjtLQUFpQjtJQUVyQyxNQUFNNEMsb0JBQW9CLENBQUNDO1FBQ3pCbkIsV0FBV21CO1FBQ1hyQixnQkFBZ0JxQjtRQUNoQmpDLGdCQUFnQjtRQUNoQkksd0JBQXdCO0lBQzFCO0lBRUFyRSxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQzZELHdCQUF1QlIsNkJBQUFBLHVDQUFBQSxpQkFBa0JtQixhQUFhLEdBQUU7WUFDM0RDLGlCQUFpQnBCLGlCQUFpQm1CLGFBQWE7UUFDakQ7SUFDRixHQUFHO1FBQUNYO1FBQXFCUjtLQUFpQjtJQUUxQ3JELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTRELGFBQWFBLFVBQVVVLE1BQU0sRUFBRTtZQUNqQ0MsVUFBVVgsVUFBVVUsTUFBTTtRQUM1QjtRQUNBLElBQUlQLGVBQWVBLFlBQVlvQyxRQUFRLEVBQUU7WUFDdkN4QixZQUFZWixZQUFZb0MsUUFBUTtRQUNsQztRQUNBLElBQUk5QyxvQkFBb0JBLGlCQUFpQm1CLGFBQWEsRUFBRTtZQUN0REMsaUJBQWlCcEIsaUJBQWlCbUIsYUFBYTtRQUNqRDtJQUNGLEdBQUc7UUFBQ1o7UUFBV0c7UUFBYVY7S0FBaUI7SUFFN0MsTUFBTStDLGtCQUFrQixDQUFDQztRQUN2QnBDLGdCQUFnQm9DO1FBQ2hCLElBQUlDLGVBQWU7UUFDbkIsSUFBSXhCLFlBQVksT0FBTztZQUNyQndCLGVBQWVDLGFBQWEsQ0FBQ0YsTUFBTTtRQUNyQyxPQUFPLElBQUl2QixZQUFZLFVBQVU7WUFDL0J3QixlQUFlRCxRQUFRRyxjQUFjUixNQUFNLEdBQUdRLGFBQWEsQ0FBQ0gsTUFBTSxHQUFHSSxjQUFjLENBQUNKLFFBQVFHLGNBQWNSLE1BQU0sQ0FBQztRQUNuSCxPQUFPLElBQUlsQixZQUFZLGlCQUFpQjtZQUN0Q3dCLGVBQWVELFVBQVUsSUFBSTdCLGFBQWEsQ0FBQ0EsY0FBY3dCLE1BQU0sR0FBRyxFQUFFLEdBQUd4QixjQUFja0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUNMLFFBQVEsRUFBRTtRQUM5RyxPQUFPLElBQUl2QixZQUFZLFlBQVk7WUFDakN3QixlQUFlRCxRQUFRTSxpQkFBaUJYLE1BQU0sR0FBR1csZ0JBQWdCLENBQUNOLE1BQU0sR0FBR08sZ0JBQWdCLENBQUNQLFFBQVFNLGlCQUFpQlgsTUFBTSxDQUFDO1FBQzlIO1FBQ0EzQix3QkFBd0JpQztJQUMxQjtJQUVBLE1BQU1PLGtCQUFrQixDQUFDQztRQUN2QnpDLHdCQUF3QnlDO1FBQ3hCM0MsYUFBYTtJQUNmO0lBRUEsTUFBTTRDLGNBQWM7UUFDbEI5QyxnQkFBZ0I7UUFDaEJJLHdCQUF3QjtRQUN4QixpQ0FBaUM7UUFDakNmLE9BQU8wRCxJQUFJLENBQUMsa0JBQWtCQyxXQUFXO1lBQUVDLFNBQVM7UUFBSztJQUMzRDtJQUVBLE1BQU1DLG1CQUFtQjtRQUN2QmhELGFBQWE7UUFDYkUsd0JBQXdCO0lBQzFCO0lBRUEsTUFBTStDLGNBQWMsSUFBSUM7SUFDeEIsTUFBTUMsY0FBY0YsWUFBWUcsT0FBTyxLQUFLSCxZQUFZSSxNQUFNO0lBQzlELE1BQU1DLFlBQVlILGNBQWM7SUFFaEMsTUFBTUksbUJBQW1CLElBQUlMLEtBQUtEO0lBQ2xDTSxpQkFBaUJDLE9BQU8sQ0FBQ0w7SUFDekIsTUFBTU0saUJBQWlCLElBQUlQLEtBQUtEO0lBQ2hDUSxlQUFlRCxPQUFPLENBQUNGO0lBRXZCLE1BQU1qQixnQkFBZ0JsQyxPQUFPdUQsTUFBTSxDQUFDLENBQUNDO1FBQ25DLE1BQU1DLFlBQVksSUFBSVYsS0FBS1MsTUFBTUUsSUFBSTtRQUNyQyxPQUFPRCxhQUFhTCxvQkFBb0JLLGFBQWFIO0lBQ3ZEO0lBRUEsTUFBTW5CLGlCQUFpQm5DLE9BQU91RCxNQUFNLENBQUMsQ0FBQ0M7UUFDcEMsTUFBTUMsWUFBWSxJQUFJVixLQUFLUyxNQUFNRSxJQUFJO1FBQ3JDLE9BQU9ELFlBQVlMO0lBQ3JCO0lBRUEsTUFBTWYsbUJBQW1CakMsU0FDdEJtRCxNQUFNLENBQUMsQ0FBQ0ksVUFBWSxJQUFJWixLQUFLWSxRQUFRQyxTQUFTLEtBQUtkLGFBQ25EZSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJaEIsS0FBS2UsRUFBRUYsU0FBUyxJQUFJLElBQUliLEtBQUtnQixFQUFFSCxTQUFTO0lBRTlELE1BQU10QixtQkFBbUJsQyxTQUN0Qm1ELE1BQU0sQ0FBQyxDQUFDSSxVQUFZLElBQUlaLEtBQUtZLFFBQVFDLFNBQVMsSUFBSWQsYUFDbERlLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNLElBQUloQixLQUFLZ0IsRUFBRUgsU0FBUyxJQUFJLElBQUliLEtBQUtlLEVBQUVGLFNBQVM7SUFFOUQsTUFBTUksdUJBQXVCOUQsY0FDMUJxRCxNQUFNLENBQUMsQ0FBQ3BDLGVBQWlCLElBQUk0QixLQUFLNUIsYUFBYThDLFNBQVMsS0FBS2Isa0JBQzdEUyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJaEIsS0FBS2UsRUFBRUcsU0FBUyxJQUFJLElBQUlsQixLQUFLZ0IsRUFBRUUsU0FBUztJQUU5RCxNQUFNQyx3QkFBd0JoRSxjQUMzQnFELE1BQU0sQ0FBQyxDQUFDcEMsZUFBaUIsSUFBSTRCLEtBQUs1QixhQUFhOEMsU0FBUyxJQUFJYixrQkFDNURTLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNLElBQUloQixLQUFLZ0IsRUFBRUUsU0FBUyxJQUFJLElBQUlsQixLQUFLZSxFQUFFRyxTQUFTO0lBRTlELE1BQU1oQyxnQkFBZ0I7V0FDakJqQyxPQUFPbUUsR0FBRyxDQUFDLENBQUNDLE9BQVU7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRTlGLE1BQU07WUFBUTtXQUMvQzhCLFNBQVMrRCxHQUFHLENBQUMsQ0FBQ0MsT0FBVTtnQkFBRSxHQUFHQSxJQUFJO2dCQUFFOUYsTUFBTTtZQUFVO1dBQ25ENEIsY0FBY2lFLEdBQUcsQ0FBQyxDQUFDQyxPQUFVO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUU5RixNQUFNO1lBQWU7S0FDakUsQ0FBQ3VGLElBQUksQ0FBQyxDQUFDQyxHQUFHQztRQUNULE1BQU1NLFFBQVFQLEVBQUVHLFNBQVMsSUFBSUgsRUFBRUYsU0FBUyxJQUFJRSxFQUFFSixJQUFJO1FBQ2xELE1BQU1ZLFFBQVFQLEVBQUVFLFNBQVMsSUFBSUYsRUFBRUgsU0FBUyxJQUFJRyxFQUFFTCxJQUFJO1FBQ2xELE9BQU8sSUFBSVgsS0FBS3VCLFNBQVMsSUFBSXZCLEtBQUtzQjtJQUNwQztJQUVBLHFCQUNFO2tCQUNFLDRFQUFDekgsNkRBQVNBO3NCQUNSLDRFQUFDZiwrSUFBR0E7Z0JBQUMwSSxJQUFJO29CQUFFL0QsU0FBUztnQkFBTzswQkFDekIsNEVBQUMzRSwrSUFBR0E7b0JBQ0YySSxXQUFVO29CQUNWRCxJQUFJO3dCQUNGbkcsWUFDRThDLGtCQUFrQjNELE1BQU1jLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDcUcsT0FBTyxHQUM5Q2xILE1BQU1jLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDRyxTQUFTLEdBQ2xDMkM7d0JBQ053RCxhQUFhL0QsZ0JBQWdCLFNBQVM7d0JBQ3RDZ0UsY0FBY2hFLGdCQUFnQixTQUFTO3dCQUN2Q3pDLE9BQU82QyxpQkFDSCxTQUNBRCxlQUNFLFVBQ0FELGdCQUNFLFVBQ0FELFdBQ0UsVUFDQTt3QkFDVmdFLFFBQVE7b0JBQ1Y7O3NDQUVBLDhEQUFDL0ksK0lBQUdBOzs4Q0FDRiw4REFBQ2dKO29DQUFJQyxPQUFPO3dDQUFFQyxZQUFZO29DQUFPOzhDQUMvQiw0RUFBQ2hKLHNKQUFVQTt3Q0FDVGlKLFNBQVE7d0NBQ1JGLE9BQU87NENBQ0xHLFdBQVc7NENBQ1h4SCxVQUFVOzRDQUNWc0gsWUFBWTlELG9CQUFvQixTQUFTO3dDQUMzQztrREFDRDs7Ozs7Ozs7Ozs7OENBSUgsOERBQUM0RDtvQ0FBSUMsT0FBTzt3Q0FBRUcsV0FBVztvQ0FBTzs7c0RBQzlCLDhEQUFDbEosc0pBQVVBOzRDQUNUaUosU0FBUTs0Q0FDUkYsT0FBTztnREFDTHJILFVBQVVpRCxlQUFlLFNBQVM7Z0RBQ2xDL0MsWUFBWTtnREFDWnNILFdBQVc7Z0RBQ1hGLFlBQVk5RCxvQkFBb0IsU0FBUzs0Q0FDM0M7c0RBQ0Q7Ozs7OztzREFHRCw4REFBQ3BGLCtJQUFHQTs0Q0FDRjBJLElBQUk7Z0RBQ0YvRCxTQUFTO2dEQUNUMEUsZ0JBQWdCO2dEQUNoQlAsY0FBYztnREFDZEksWUFBWTlELG9CQUFvQixTQUFTO2dEQUN6Q2dFLFdBQVc7NENBQ2I7c0RBRUEsNEVBQUN4SSx1RUFBbUJBO2dEQUNsQmtGLG1CQUFtQkE7Z0RBQ25CckIsY0FBY0E7Z0RBQ2RVLGdCQUFnQkE7Z0RBQ2hCTixjQUFjQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBS3JCaEIsaUJBQWlCLHNCQUNoQiw4REFBQ21GOzRCQUFJQyxPQUFPO2dDQUFFdEUsU0FBUztnQ0FBUTJFLFlBQVk7Z0NBQVVGLFdBQVc7NEJBQU87OzhDQUNyRSw4REFBQzVJLGlFQUFVQTtvQ0FDVCtJLFNBQVMzQztvQ0FDVHFDLE9BQU87d0NBQ0xDLFlBQVk5RCxvQkFBb0IsU0FBUzt3Q0FDekNULFNBQVM7d0NBQ1QyRSxZQUFZO3dDQUNaMUgsVUFBVTt3Q0FDVkQsT0FBTzt3Q0FDUDZILEtBQUs7b0NBQ1A7O3NEQUVBLDhEQUFDQzs0Q0FBSUMsS0FBSTs0Q0FBd0JDLEtBQUk7NENBQVFySCxRQUFROzRDQUFJRCxLQUFLOzRDQUFDdUgsWUFBWTs7Ozs7O3dDQUMxRTNGLENBQUFBLGlDQUFBQSwyQ0FBQUEscUJBQXNCNEYsS0FBSyxLQUFJOzs7Ozs7OzhDQUVsQyw4REFBQ2I7b0NBQ0NDLE9BQU87d0NBQ0x0RSxTQUFTO3dDQUNUNkUsS0FBSzt3Q0FDTEYsWUFBWTt3Q0FDWjFILFVBQVU7b0NBQ1o7O3NEQUVBLDhEQUFDbkIsOERBQU9BOzRDQUNOaUksSUFBSTtnREFDRnJHLE9BQU87Z0RBQ1BILGlCQUFpQjs0Q0FDbkI7Ozs7Ozt3Q0FFRCtCLENBQUFBLGlDQUFBQSwyQ0FBQUEscUJBQXNCbUUsU0FBUyxtQkFDOUIsOERBQUNsSSxzSkFBVUE7NENBQ1RpSixTQUFROzRDQUNSVCxJQUFJO2dEQUNGL0csT0FBTztnREFDUEMsVUFBVTtnREFDVndILFdBQVc7NENBQ2I7c0RBRUNoSSw2RUFBZ0JBLENBQUM2QyxxQkFBcUJtRSxTQUFTOzs7Ozs7d0NBR25EbkUsQ0FBQUEsaUNBQUFBLDJDQUFBQSxxQkFBc0I0RCxJQUFJLEtBQUksRUFBQzVELGlDQUFBQSwyQ0FBQUEscUJBQXNCbUUsU0FBUyxtQkFDN0QsOERBQUNsSSxzSkFBVUE7NENBQ1RpSixTQUFROzRDQUNSVCxJQUFJO2dEQUNGL0csT0FBTztnREFDUEMsVUFBVTtnREFDVmtJLGNBQWM7NENBQ2hCOztnREFFQzFJLDZFQUFnQkEsQ0FBQzZDLHFCQUFxQjRELElBQUk7Z0RBQUU7Z0RBQUc7Z0RBQy9DeEcsc0VBQVNBLENBQUMsSUFBSTZGLEtBQUtqRCxxQkFBcUI0RCxJQUFJOzs7Ozs7O3dDQUdoRDVELENBQUFBLGlDQUFBQSwyQ0FBQUEscUJBQXNCOEQsU0FBUyxLQUFJLEVBQUM5RCxpQ0FBQUEsMkNBQUFBLHFCQUFzQm1FLFNBQVMsS0FBSSxFQUFDbkUsaUNBQUFBLDJDQUFBQSxxQkFBc0I0RCxJQUFJLG1CQUNqRyw4REFBQzNILHNKQUFVQTs0Q0FDVGlKLFNBQVE7NENBQ1JULElBQUk7Z0RBQ0YvRyxPQUFPO2dEQUNQQyxVQUFVO2dEQUNWa0ksY0FBYzs0Q0FDaEI7O2dEQUVDMUksNkVBQWdCQSxDQUFDNkMscUJBQXFCOEQsU0FBUztnREFBRTtnREFBRztnREFDcEQxRyxzRUFBU0EsQ0FBQyxJQUFJNkYsS0FBS2pELHFCQUFxQjhELFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTVELDhEQUFDOUgsZ0pBQUlBOzRCQUNIOEosU0FBUzs0QkFDVEMsU0FBUzs0QkFDVHRCLElBQUk7Z0NBQ0Z1QixTQUFTO2dDQUNUM0gsUUFBUTtnQ0FDUjRILFVBQVU7Z0NBQ1ZkLFdBQVc7NEJBQ2I7c0NBRUEsNEVBQUNuSixnSkFBSUE7Z0NBQ0hzSSxJQUFJO2dDQUNKNEIsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsT0FBTztvQ0FBRUgsSUFBSTtvQ0FBR0ksSUFBSTtvQ0FBR0gsSUFBSTtvQ0FBR0MsSUFBSTtvQ0FBR0csSUFBSTtnQ0FBRTs7b0NBRTFDN0YsWUFBWSx1QkFDWDtrREFDR2pCLHVCQUF1QkgsZ0JBQWdCSSwrQkFDdEMsOERBQUN6RCxzSkFBVUE7c0RBQUM7Ozs7O3NFQUVaO3NEQUNHa0csY0FBY2tDLEdBQUcsQ0FBQyxDQUFDQyxNQUFNckM7Z0RBQ3hCLE1BQU11RSxVQUFVbEMsS0FBSzlGLElBQUksS0FBSztnREFDOUIsTUFBTWlJLFlBQVluQyxLQUFLOUYsSUFBSSxLQUFLO2dEQUNoQyxNQUFNa0ksaUJBQWlCcEMsS0FBSzlGLElBQUksS0FBSztnREFDckMscUJBQ0UsOERBQUNSO29EQUVDRyx1QkFBdUJ1STtvREFDdkJqQyxJQUFJO3dEQUNGdUIsU0FBUzt3REFDVGIsV0FBVzt3REFDWHpFLFNBQ0VkLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO3dEQUNONUQsUUFDRXVCLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO3dEQUNOM0QsWUFBWW9JLGtCQUFrQkQsWUFBWWhKLE1BQU1jLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDRyxTQUFTLEdBQUdvRTtvREFDakY7b0RBQ0F5QyxTQUFTLElBQU10RCxnQkFBZ0JDOzt3REFFOUJ1RSx5QkFDQyw4REFBQzVKLG9FQUFnQkE7NERBQ2ZnSixPQUFPdEIsS0FBS3NCLEtBQUs7NERBQ2pCZSxTQUFTckMsS0FBS3FDLE9BQU87NERBQ3JCQyxZQUFZaEgsaUJBQWlCcUM7NERBQzdCMkIsTUFBTWhFLGlCQUFpQnFDLFFBQVFxQyxLQUFLVixJQUFJLEdBQUc7Ozs7Ozt3REFHOUM2QywyQkFDQyw4REFBQzdKLG9FQUFnQkE7NERBQ2ZnSixPQUFPdEIsS0FBS3NCLEtBQUs7NERBQ2pCZSxTQUFTckMsS0FBS3FDLE9BQU87NERBQ3JCQyxZQUFZaEgsaUJBQWlCcUM7NERBQzdCMkIsTUFBTWhFLGlCQUFpQnFDLFFBQVFxQyxLQUFLUixTQUFTLEdBQUc7Ozs7Ozt3REFHbkQ0QyxnQ0FDQyw4REFBQzdKLG9FQUFnQkE7NERBQ2Z3RSxjQUFjaUQsS0FBS2pELFlBQVk7NERBQy9CdUYsWUFBWWhILGlCQUFpQnFDOzREQUM3QjlELHVCQUF1Qjs0REFDdkIwSSxVQUFVakgsaUJBQWlCcUM7NERBQzNCMkIsTUFDRWhFLGlCQUFpQnFDLFFBQ2IsR0FBb0MsT0FBakM5RSw2RUFBZ0JBLENBQUNtSCxLQUFLSCxTQUFTLEtBQ2xDOzs7Ozs7O21EQTFDTEcsS0FBSzlDLEVBQUUsSUFBSVM7Ozs7OzRDQWdEdEI7OztvQ0FLUHZCLFlBQVksMEJBQ1g7OzRDQUNHMEIsY0FBY2lDLEdBQUcsQ0FBQyxDQUFDWCxPQUFPekIsc0JBQ3pCLDhEQUFDakU7b0RBRUNHLHVCQUF1QjtvREFDdkJzRyxJQUFJO3dEQUNGdUIsU0FBUzt3REFDVGIsV0FBVzt3REFDWHpFLFNBQ0VkLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO3dEQUNONUQsUUFDRXVCLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO29EQUNSO29EQUNBcUQsU0FBUyxJQUFNdEQsZ0JBQWdCQzs4REFFL0IsNEVBQUNyRixvRUFBZ0JBO3dEQUNmdUIsdUJBQXVCO3dEQUN2QnlILE9BQU9sQyxNQUFNa0MsS0FBSzt3REFDbEJlLFNBQVNqRCxNQUFNaUQsT0FBTzt3REFDdEIvQyxNQUFNaEUsaUJBQWlCcUMsUUFBUXlCLE1BQU1FLElBQUksR0FBRzt3REFDNUNnRCxZQUFZaEgsaUJBQWlCcUM7Ozs7OzttREFyQjFCeUIsTUFBTWxDLEVBQUUsSUFBSVM7Ozs7OzRDQXlCcEJJLGVBQWVULE1BQU0sR0FBRyxtQkFDdkIsOERBQUM3RiwrSUFBR0E7Z0RBQ0YyRSxTQUFRO2dEQUNSMkUsWUFBVztnREFDWFEsY0FBYTtnREFDYlYsV0FBVTs7a0VBRVYsOERBQUNwSiwrSUFBR0E7d0RBQ0YrSyxjQUFhO3dEQUNiMUksT0FBTTs7Ozs7O2tFQUVSLDhEQUFDbkMsc0pBQVVBO3dEQUNUaUosU0FBUTt3REFDUjZCLE9BQU07d0RBQ050QyxJQUFJOzREQUNGdUMsSUFBSTs0REFDSnRKLE9BQU87NERBQ1BDLFVBQVU7NERBQ1ZzSixZQUFZO3dEQUNkO2tFQUNEOzs7Ozs7a0VBR0QsOERBQUNsTCwrSUFBR0E7d0RBQUNtTCxXQUFVO3dEQUFvQjlJLE9BQU07Ozs7Ozs7Ozs7Ozs0Q0FHNUNpRSxlQUFlZ0MsR0FBRyxDQUFDLENBQUNYLE9BQU96QixzQkFDMUIsOERBQUNqRTtvREFFQ3lHLElBQUk7d0RBQ0ZuRyxZQUFZYixNQUFNYyxPQUFPLENBQUNELFVBQVUsQ0FBQ0csU0FBUzt3REFDOUN1SCxTQUFTO3dEQUNUYixXQUFXO3dEQUNYekUsU0FDRWQsaUJBQWlCLFFBQ2ZBLGlCQUFpQnFDLFFBQVFHLGNBQWNSLE1BQU0sR0FDM0MsU0FDQTt3REFDTnZELFFBQ0V1QixpQkFBaUIsUUFDZkEsaUJBQWlCcUMsUUFBUUcsY0FBY1IsTUFBTSxHQUMzQyxTQUNBO29EQUNSO29EQUNBMEQsU0FBUyxJQUNQdEQsZ0JBQWdCQyxRQUFRRyxjQUFjUixNQUFNOzhEQUc5Qyw0RUFBQ2hGLG9FQUFnQkE7d0RBQ2ZnSixPQUFPbEMsTUFBTWtDLEtBQUs7d0RBQ2xCZSxTQUFTakQsTUFBTWlELE9BQU87d0RBQ3RCQyxZQUNFaEgsaUJBQWlCcUMsUUFBUUcsY0FBY1IsTUFBTTt3REFFL0NnQyxNQUFNaEUsaUJBQWlCcUMsUUFBUUcsY0FBY1IsTUFBTSxHQUFHOEIsTUFBTUUsSUFBSSxHQUFHOzs7Ozs7bURBMUJoRUYsTUFBTWxDLEVBQUUsSUFBSVM7Ozs7Ozs7b0NBZ0N4QnZCLFlBQVksaUNBQ1g7a0RBQ0dqQixvQ0FDQyw4REFBQ3hELHNKQUFVQTtzREFBQzs7Ozs7d0RBQ1ZnRCxDQUFBQSw2QkFBQUEsd0NBQUFBLGtDQUFBQSxpQkFBa0JtQixhQUFhLGNBQS9CbkIsc0RBQUFBLGdDQUFpQzJDLE1BQU0sSUFBRyxrQkFDNUM7OzhEQUNFLDhEQUFDNUQ7b0RBQ0NHLHVCQUF1QjtvREFDdkJzRyxJQUFJO3dEQUNGdUIsU0FBUzt3REFDVGIsV0FBVzt3REFDWHpFLFNBQVNkLGlCQUFpQixRQUFRQSxpQkFBaUIsSUFBSSxTQUFTO29EQUNsRTtvREFDQTBGLFNBQVMsSUFBTXRELGdCQUFnQjs4REFFL0IsNEVBQUNuRixvRUFBZ0JBO3dEQUNmd0UsY0FBY3BDLGlCQUFpQm1CLGFBQWEsQ0FBQ25CLGlCQUFpQm1CLGFBQWEsQ0FBQ3dCLE1BQU0sR0FBRyxFQUFFLENBQUNQLFlBQVk7d0RBQ3BHdUYsWUFBWWhILGlCQUFpQjt3REFDN0J6Qix1QkFBdUI7d0RBQ3ZCMEksVUFBVWpILGlCQUFpQjt3REFDM0JnRSxNQUNFaEUsaUJBQWlCLElBQ2IsR0FBeUcsT0FBdEd6Qyw2RUFBZ0JBLENBQUM4QixpQkFBaUJtQixhQUFhLENBQUNuQixpQkFBaUJtQixhQUFhLENBQUN3QixNQUFNLEdBQUcsRUFBRSxDQUFDdUMsU0FBUyxLQUN2Rzs7Ozs7Ozs7Ozs7Z0RBSVRsRixpQkFBaUJtQixhQUFhLENBQUN3QixNQUFNLEdBQUcsbUJBQ3ZDLDhEQUFDN0YsK0lBQUdBO29EQUFDMkUsU0FBUTtvREFBTzJFLFlBQVc7b0RBQVNRLGNBQWE7b0RBQU9WLFdBQVU7O3NFQUNwRSw4REFBQ3BKLCtJQUFHQTs0REFBQytLLGNBQWE7NERBQW9CMUksT0FBTTs7Ozs7O3NFQUM1Qyw4REFBQ25DLHNKQUFVQTs0REFBQ2lKLFNBQVE7NERBQVE2QixPQUFNOzREQUFTdEMsSUFBSTtnRUFBRXVDLElBQUk7Z0VBQUd0SixPQUFPO2dFQUFXQyxVQUFVO2dFQUFRc0osWUFBWTs0REFBUztzRUFBRzs7Ozs7O3NFQUdwSCw4REFBQ2xMLCtJQUFHQTs0REFBQ21MLFdBQVU7NERBQW9COUksT0FBTTs7Ozs7Ozs7Ozs7O2dEQUc1Q2EsaUJBQWlCbUIsYUFBYSxDQUFDa0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHK0IsR0FBRyxDQUFDLENBQUNoRCxjQUFjWSxzQkFDOUQsOERBQUNqRTt3REFFQ3lHLElBQUk7NERBQ0ZuRyxZQUFZYixNQUFNYyxPQUFPLENBQUNELFVBQVUsQ0FBQ0csU0FBUzs0REFDOUN1SCxTQUFTOzREQUNUYixXQUFXOzREQUNYekUsU0FBU2QsaUJBQWlCLFFBQVFBLGlCQUFpQnFDLFFBQVEsSUFBSSxTQUFTOzREQUN4RTVELFFBQVF1QixpQkFBaUJxQyxRQUFRLElBQUksU0FBUzt3REFDaEQ7d0RBQ0FxRCxTQUFTLElBQU10RCxnQkFBZ0JDLFFBQVE7a0VBRXZDLDRFQUFDcEYsb0VBQWdCQTs0REFDZndFLGNBQWNBLGFBQWFBLFlBQVk7NERBQ3ZDdUYsWUFBWWhILGlCQUFpQnFDLFFBQVE7NERBQ3JDOUQsdUJBQXVCOzREQUN2QjBJLFVBQVVqSCxpQkFBaUJxQyxRQUFROzREQUNuQzJCLE1BQ0VoRSxpQkFBaUJxQyxRQUFRLElBQ3JCLEdBQTRDLE9BQXpDOUUsNkVBQWdCQSxDQUFDa0UsYUFBYThDLFNBQVMsS0FDMUM7Ozs7Ozt1REFsQkg5QyxhQUFhRyxFQUFFLElBQUlTOzs7Ozs7eUVBeUI5Qiw4REFBQ2hHLHNKQUFVQTtzREFBQzs7Ozs7OztvQ0FJakJ5RSxZQUFZLDRCQUNYOzs0Q0FDRzZCLGlCQUFpQjhCLEdBQUcsQ0FBQyxDQUFDUixTQUFTNUIsc0JBQzlCLDhEQUFDakU7b0RBRUN5RyxJQUFJO3dEQUNGbkcsWUFBWWIsTUFBTWMsT0FBTyxDQUFDRCxVQUFVLENBQUNHLFNBQVM7d0RBQzlDdUgsU0FBUzt3REFDVGIsV0FBVzt3REFDWHpFLFNBQ0VkLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO3dEQUNONUQsUUFDRXVCLGlCQUFpQixRQUFRQSxpQkFBaUJxQyxRQUN0QyxTQUNBO29EQUNSO29EQUNBcUQsU0FBUyxJQUFNdEQsZ0JBQWdCQzs7c0VBRS9CLDhEQUFDaEcsc0pBQVVBOzREQUNUaUosU0FBUTs0REFDUjZCLE9BQU07NERBQ050QyxJQUFJO2dFQUNGdUMsSUFBSTtnRUFDSnRKLE9BQU87Z0VBQ1BDLFVBQVU7Z0VBQ1ZzSixZQUFZOzREQUNkO3NFQUNEOzs7Ozs7c0VBR0QsOERBQUNySyxvRUFBZ0JBOzREQUNmZ0osT0FBTy9CLFFBQVErQixLQUFLOzREQUNwQmUsU0FBUzlDLFFBQVE4QyxPQUFPOzREQUN4Qi9DLE1BQU1oRSxpQkFBaUJxQyxRQUFRNEIsUUFBUUMsU0FBUyxHQUFHOzREQUNuRDhDLFlBQVloSCxpQkFBaUJxQzs0REFDN0JxRCxTQUFTLElBQU03QyxnQkFBZ0JvQjs7Ozs7OzttREFqQzVCQSxRQUFRckMsRUFBRSxJQUFJUzs7Ozs7NENBcUN0Qk8saUJBQWlCWixNQUFNLEdBQUcsbUJBQ3pCLDhEQUFDN0YsK0lBQUdBO2dEQUNGMkUsU0FBUTtnREFDUjJFLFlBQVc7Z0RBQ1hRLGNBQWE7Z0RBQ2JWLFdBQVU7O2tFQUVWLDhEQUFDcEosK0lBQUdBO3dEQUNGK0ssY0FBYTt3REFDYjFJLE9BQU07Ozs7OztrRUFFUiw4REFBQ25DLHNKQUFVQTt3REFDVGlKLFNBQVE7d0RBQ1I2QixPQUFNO3dEQUNOdEMsSUFBSTs0REFDRnVDLElBQUk7NERBQ0p0SixPQUFPOzREQUNQQyxVQUFVOzREQUNWc0osWUFBWTt3REFDZDtrRUFDRDs7Ozs7O2tFQUdELDhEQUFDbEwsK0lBQUdBO3dEQUFDbUwsV0FBVTt3REFBb0I5SSxPQUFNOzs7Ozs7Ozs7Ozs7NENBRzVDb0UsaUJBQWlCNkIsR0FBRyxDQUFDLENBQUNSLFNBQVM1QixzQkFDOUIsOERBQUNqRTtvREFFQ3lHLElBQUk7d0RBQ0ZuRyxZQUFZYixNQUFNYyxPQUFPLENBQUNELFVBQVUsQ0FBQ0csU0FBUzt3REFDOUN1SCxTQUFTO3dEQUNUYixXQUFXO3dEQUNYekUsU0FDRWQsaUJBQWlCLFFBQ2ZBLGlCQUFpQnFDLFFBQVFNLGlCQUFpQlgsTUFBTSxHQUM5QyxTQUNBO3dEQUNOdkQsUUFDRXVCLGlCQUFpQixRQUNmQSxpQkFBaUJxQyxRQUFRTSxpQkFBaUJYLE1BQU0sR0FDOUMsU0FDQTtvREFDUjtvREFDQTBELFNBQVMsSUFDUHRELGdCQUFnQkMsUUFBUU0saUJBQWlCWCxNQUFNOzhEQUdqRCw0RUFBQ2hGLG9FQUFnQkE7d0RBQ2ZnSixPQUFPL0IsUUFBUStCLEtBQUs7d0RBQ3BCZSxTQUFTOUMsUUFBUThDLE9BQU87d0RBQ3hCL0MsTUFDRWhFLGlCQUFpQnFDLFFBQVFNLGlCQUFpQlgsTUFBTSxHQUM1Q2lDLFFBQVFDLFNBQVMsR0FDakI7d0RBRU44QyxZQUNFaEgsaUJBQWlCcUMsUUFBUU0saUJBQWlCWCxNQUFNO3dEQUVsRDBELFNBQVMsSUFBTTdDLGdCQUFnQm9COzs7Ozs7bURBL0I1QkEsUUFBUXJDLEVBQUUsSUFBSVM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0F1Qy9CLDhEQUFDOUYsa0pBQU1BOzRCQUFDZ0wsTUFBTXJIOzRCQUFXc0gsU0FBU3JFOzRCQUFrQmhGLFVBQVM7NEJBQUtzSixTQUFTOzs4Q0FDekUsOERBQUNqTCx1SkFBV0E7OENBQ1Q0RCxDQUFBQSxpQ0FBQUEsMkNBQUFBLHFCQUFzQjRGLEtBQUssS0FBSTs7Ozs7OzhDQUVsQyw4REFBQ3ZKLHlKQUFhQTs7c0RBQ1osOERBQUNKLHNKQUFVQTs0Q0FBQ2lKLFNBQVE7c0RBQ2pCbEYsQ0FBQUEsaUNBQUFBLDJDQUFBQSxxQkFBc0JxQixZQUFZLE1BQUlyQixpQ0FBQUEsMkNBQUFBLHFCQUFzQjJHLE9BQU8sS0FBSTs7Ozs7O3dDQUV6RTNHLENBQUFBLGlDQUFBQSwyQ0FBQUEscUJBQXNCNEQsSUFBSSxtQkFDekIsOERBQUMzSCxzSkFBVUE7NENBQUNpSixTQUFROzRDQUFReEgsT0FBTTs7Z0RBQWdCO2dEQUN6Q1AsNkVBQWdCQSxDQUFDNkMscUJBQXFCNEQsSUFBSTtnREFBRTtnREFBRXhHLHNFQUFTQSxDQUFDLElBQUk2RixLQUFLakQscUJBQXFCNEQsSUFBSTs7Ozs7Ozt3Q0FHcEc1RCxDQUFBQSxpQ0FBQUEsMkNBQUFBLHFCQUFzQjhELFNBQVMsbUJBQzlCLDhEQUFDN0gsc0pBQVVBOzRDQUFDaUosU0FBUTs0Q0FBUXhILE9BQU07O2dEQUFnQjtnREFDbkNQLDZFQUFnQkEsQ0FBQzZDLHFCQUFxQjhELFNBQVM7Z0RBQUU7Z0RBQUUxRyxzRUFBU0EsQ0FBQyxJQUFJNkYsS0FBS2pELHFCQUFxQjhELFNBQVM7Ozs7Ozs7d0NBR3BIOUQsQ0FBQUEsaUNBQUFBLDJDQUFBQSxxQkFBc0JtRSxTQUFTLEtBQUksRUFBQ25FLGlDQUFBQSwyQ0FBQUEscUJBQXNCNEQsSUFBSSxLQUFJLEVBQUM1RCxpQ0FBQUEsMkNBQUFBLHFCQUFzQjhELFNBQVMsbUJBQ2pHLDhEQUFDN0gsc0pBQVVBOzRDQUFDaUosU0FBUTs0Q0FBUXhILE9BQU07O2dEQUFnQjtnREFDekNQLDZFQUFnQkEsQ0FBQzZDLHFCQUFxQm1FLFNBQVM7Z0RBQUU7Z0RBQUUvRyxzRUFBU0EsQ0FBQyxJQUFJNkYsS0FBS2pELHFCQUFxQm1FLFNBQVM7Ozs7Ozs7Ozs7Ozs7OENBSWpILDhEQUFDN0gseUpBQWFBOzhDQUNaLDRFQUFDSixrSkFBTUE7d0NBQUNvSixTQUFTdkM7d0NBQWtCckYsT0FBTTtrREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVuRTtHQXRxQk1zQjs7UUFDV3pCLG1EQUFTQTtRQUUyQmIscURBQVFBO1FBQ01BLHFEQUFRQTtRQUNsQkEscURBQVFBO1FBVzFDRCxvRUFBYUE7UUFDWkEsb0VBQWFBO1FBQ2xCQSxvRUFBYUE7UUFDUkEsb0VBQWFBO1FBQ2RBLG9FQUFhQTtRQUNYQSxvRUFBYUE7UUFDYkEsb0VBQWFBO1FBQ1ZBLG9FQUFhQTtRQUV6QlgsMkRBQVFBO1FBQ0ppQiw4REFBUUE7UUFDSkMseUVBQWdCQTs7O01BM0JsQ2dDO0FBd3FCTiwrREFBZSxNQUFBMUIsc0VBQVFBLENBQUMwQixjQUFjQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9wYWdlcy9ub3RpZmljYXRpb25zLmpzPzA5NDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ3FsIH0gZnJvbSBcIkBhcG9sbG8vY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHN0eWxlZCwgdXNlVGhlbWUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbC9zdHlsZXNcIjtcclxuaW1wb3J0IHtcclxuICBCb3gsXHJcbiAgR3JpZCxcclxuICBUeXBvZ3JhcGh5LFxyXG4gIEJ1dHRvbixcclxuICBEaWFsb2csXHJcbiAgRGlhbG9nVGl0bGUsXHJcbiAgRGlhbG9nQ29udGVudCxcclxuICBEaWFsb2dBY3Rpb25zLFxyXG59IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCBJY29uQnV0dG9uIGZyb20gXCJAbXVpL21hdGVyaWFsL0ljb25CdXR0b25cIjtcclxuaW1wb3J0IERpdmlkZXIgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvRGl2aWRlclwiO1xyXG5pbXBvcnQgdXNlTWVkaWFRdWVyeSBmcm9tIFwiQG11aS9tYXRlcmlhbC91c2VNZWRpYVF1ZXJ5XCI7XHJcbmltcG9ydCB7IHVzZVF1ZXJ5IH0gZnJvbSBcIkBhcG9sbG8vY2xpZW50XCI7XHJcbmltcG9ydCBOb3RpZmljYXRpb25CdXR0b25zIGZyb20gXCJAL2NvbXBvbmVudHMvTm90aWZpY2F0aW9uQnV0dG9uc1wiO1xyXG5pbXBvcnQgQW5ub3VuY2VtZW50Q2FyZCBmcm9tIFwiLi4vY29tcG9uZW50cy9Bbm5vdW5jZW1lbnRDYXJkXCI7XHJcbmltcG9ydCBOb3RpZmljYXRpb25DYXJkIGZyb20gXCIuLi9jb21wb25lbnRzL05vdGlmaWNhdGlvbkNhcmRcIjtcclxuaW1wb3J0IERhc2hib2FyZCBmcm9tIFwiQC9jb21wb25lbnRzL0Rhc2hib2FyZFwiO1xyXG5pbXBvcnQgeyB1c2VDb2xvciB9IGZyb20gXCJAL2NvbXBvbmVudHMvQ29sb3JDb250ZXh0XCI7XHJcbmltcG9ydCB7IHVzZVNlbGVjdGVkQ29sb3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL0hlbHBlckZ1bmN0aW9uc1wiO1xyXG5pbXBvcnQgeyBnZXRFdmVudHMgfSBmcm9tIFwiQC9EYXRhL0V2ZW50c1wiO1xyXG5pbXBvcnQgeyBnZXROb3RpZmljYXRpb25zIH0gZnJvbSBcIkAvRGF0YS9Ob3RpZmljYXRpb25cIjtcclxuaW1wb3J0IHsgZ2V0Rm9ybWF0dGVkRGF0ZSwgbW9udGhOYW1lIH0gZnJvbSBcIkAvY29tcG9uZW50cy9IZWxwZXJGdW5jdGlvbnNcIjtcclxuaW1wb3J0IHsgZ2V0Qm9va2luZ3MgfSBmcm9tIFwiQC9EYXRhL0Jvb2tpbmdcIjtcclxuaW1wb3J0IHdpdGhBdXRoIGZyb20gXCJAL2NvbXBvbmVudHMvYXV0aC93aXRoQXV0aFwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9yb3V0ZXJcIjtcclxuXHJcbmNvbnN0IFJlc3BvbnNpdmVUZXh0ID0gc3R5bGVkKFwiZGl2XCIpKCh7IHRoZW1lIH0pID0+ICh7XHJcbiAgY29sb3I6IFwiIzFCMzc0NVwiLFxyXG4gIGZvbnRTaXplOiBcIjE2LjAycHhcIixcclxuICBmb250RmFtaWx5OiBcIkhlbHZldGljYVwiLFxyXG4gIGZvbnRXZWlnaHQ6IDQwMCxcclxuICB3b3JkV3JhcDogXCJicmVhay13b3JkXCIsXHJcbiAgbWF4V2lkdGg6IFwiMTAwJVwiLFxyXG59KSk7XHJcblxyXG5jb25zdCBSZXNwb25zaXZlQm94ID0gc3R5bGVkKEJveCkoXHJcbiAgKHsgdGhlbWUsIGJhY2tncm91bmRDb2xvciwgZGlzYWJsZUJvcmRlciwgaXNDdXJyZW50Tm90aWZpY2F0aW9uIH0pID0+ICh7XHJcbiAgICB3aWR0aDogXCIxMDAlXCIsXHJcbiAgICBoZWlnaHQ6IFwiYXV0byAhaW1wb3J0YW50XCIsXHJcbiAgICBiYWNrZ3JvdW5kOiBiYWNrZ3JvdW5kQ29sb3IgfHwgXCJ3aGl0ZVwiLFxyXG4gICAgYmFja2dyb3VuZENvbG9yOiAhaXNDdXJyZW50Tm90aWZpY2F0aW9uXHJcbiAgICAgID8gdGhlbWUucGFsZXR0ZS50eXBlID09PSBcImxpZ2h0XCJcclxuICAgICAgICA/IFwiI0Y2RjVGRFwiXHJcbiAgICAgICAgOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuc2Vjb25kYXJ5XHJcbiAgICAgIDogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnNlY29uZGFyeSxcclxuICAgIGJveFNoYWRvdzogXCIwcHggNHB4IDEwcHggcmdiYSgwLCAwLCAwLCAwLjA1KVwiLFxyXG4gICAgYm9yZGVyUmFkaXVzOiAxMCxcclxuICAgIHBvc2l0aW9uOiBcInJlbGF0aXZlXCIsXHJcbiAgICBcIiY6OmJlZm9yZVwiOiAhZGlzYWJsZUJvcmRlciAmJiB7XHJcbiAgICAgIGNvbnRlbnQ6ICdcIlwiJyxcclxuICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcclxuICAgICAgdG9wOiAwLFxyXG4gICAgICBsZWZ0OiAwLjYsXHJcbiAgICAgIHdpZHRoOiBcIjVweFwiLFxyXG4gICAgICBoZWlnaHQ6IFwiMTAwJVwiLFxyXG4gICAgICBiYWNrZ3JvdW5kOiAhaXNDdXJyZW50Tm90aWZpY2F0aW9uXHJcbiAgICAgICAgPyB0aGVtZS5wYWxldHRlLnR5cGUgPT09IFwibGlnaHRcIlxyXG4gICAgICAgICAgPyBcIiNGNkY1RkRcIlxyXG4gICAgICAgICAgOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuc2Vjb25kYXJ5XHJcbiAgICAgICAgOiBpc0N1cnJlbnROb3RpZmljYXRpb25cclxuICAgICAgICAgID8gYGxpbmVhci1ncmFkaWVudCgxODBkZWcsICNBNjY1RTEgMCUsICM2MkI2RjMgOTkuOTklKWBcclxuICAgICAgICAgIDogXCJub25lXCIsXHJcbiAgICAgIGJvcmRlclJhZGl1czogXCIyMHB4IDAgMCAyMHB4XCIsXHJcbiAgICB9LFxyXG4gIH0pXHJcbik7XHJcblxyXG5jb25zdCBOb3RpZmljYXRpb25zID0gKCkgPT4ge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHsgbm90aWZpY2F0aW9uSWQgfSA9IHJvdXRlci5xdWVyeTsgLy8gR2V0IG5vdGlmaWNhdGlvbklkIGZyb20gcXVlcnkgcGFyYW1zXHJcbiAgY29uc3QgeyBsb2FkaW5nOiBldmVudExvYWRpbmcsIGRhdGE6IGV2ZW50RGF0YSB9ID0gdXNlUXVlcnkoZ2V0RXZlbnRzKTtcclxuICBjb25zdCB7IGxvYWRpbmc6IG5vdGlmaWNhdGlvbkxvYWRpbmcsIGRhdGE6IG5vdGlmaWNhdGlvbkRhdGEgfSA9IHVzZVF1ZXJ5KGdldE5vdGlmaWNhdGlvbnMpO1xyXG4gIGNvbnN0IHsgbG9hZGluZzogYm9va2luZ0xvYWRpbmcsIGRhdGE6IGJvb2tpbmdEYXRhIH0gPSB1c2VRdWVyeShnZXRCb29raW5ncyk7XHJcblxyXG4gIGNvbnN0IFtzZWxlY3RlZENhcmQsIHNldFNlbGVjdGVkQ2FyZF0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbb3Blbk1vZGFsLCBzZXRPcGVuTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEFubm91bmNlbWVudCwgc2V0U2VsZWN0ZWRBbm5vdW5jZW1lbnRdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2V2ZW50cywgc2V0RXZlbnRzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbbm90aWZpY2F0aW9ucywgc2V0Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW21lZXRpbmdzLCBzZXRNZWV0aW5nc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2FjdGl2ZUJ1dHRvbiwgc2V0QWN0aXZlQnV0dG9uXSA9IHVzZVN0YXRlKFwiYWxsXCIpO1xyXG4gIGNvbnN0IFtkaXNwbGF5LCBzZXREaXNwbGF5XSA9IHVzZVN0YXRlKFwiYWxsXCIpO1xyXG5cclxuICBjb25zdCBpc1dpZGVTY3JlZW4gPSB1c2VNZWRpYVF1ZXJ5KFwiKG1heC13aWR0aDoxMTUwcHgpXCIpO1xyXG4gIGNvbnN0IGlzTGFyZ2VUYWJsZXQgPSB1c2VNZWRpYVF1ZXJ5KFwiKG1heC13aWR0aDoxMDc5cHgpXCIpO1xyXG4gIGNvbnN0IGlzVGFibGV0ID0gdXNlTWVkaWFRdWVyeShcIihtYXgtd2lkdGg6MTA2MHB4KVwiKTtcclxuICBjb25zdCBpc1NtYWxsVGFibGV0ID0gdXNlTWVkaWFRdWVyeShcIihtYXgtd2lkdGg6OTY3cHgpXCIpO1xyXG4gIGNvbnN0IGlzV2lkZU1vYmlsZSA9IHVzZU1lZGlhUXVlcnkoXCIobWF4LXdpZHRoOjg2OXB4KVwiKTtcclxuICBjb25zdCBpc01lZGl1bU1vYmlsZSA9IHVzZU1lZGlhUXVlcnkoXCIobWF4LXdpZHRoOjgyM3B4KVwiKTtcclxuICBjb25zdCBpc05hcnJvd01vYmlsZSA9IHVzZU1lZGlhUXVlcnkoXCIobWF4LXdpZHRoOjUyOHB4KVwiKTtcclxuICBjb25zdCBpc1N0YW5kYXJkRGVza3RvcCA9IHVzZU1lZGlhUXVlcnkoXCIobWF4LXdpZHRoOjE0MzlweClcIik7XHJcblxyXG4gIGNvbnN0IHRoZW1lID0gdXNlVGhlbWUoKTtcclxuICBjb25zdCB7IGNvbG9yIH0gPSB1c2VDb2xvcigpO1xyXG4gIGNvbnN0IHNlbGVjdGVkQ29sb3IgPSB1c2VTZWxlY3RlZENvbG9yKGNvbG9yKTtcclxuXHJcbiAgLy8gSGFuZGxlIG5hdmlnYXRpb24gZnJvbSBxdWVyeSBwYXJhbXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKG5vdGlmaWNhdGlvbklkICYmIG5vdGlmaWNhdGlvbkRhdGE/Lm5vdGlmaWNhdGlvbnMpIHtcclxuICAgICAgLy8gRmluZCB0aGUgbm90aWZpY2F0aW9uIGRpcmVjdGx5IGluIG5vdGlmaWNhdGlvbnMgZGF0YVxyXG4gICAgICBjb25zdCBub3RpZmljYXRpb24gPSBub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnMuZmluZChcclxuICAgICAgICAobm90aWYpID0+IG5vdGlmLmlkID09PSBub3RpZmljYXRpb25JZFxyXG4gICAgICApO1xyXG5cclxuICAgICAgaWYgKG5vdGlmaWNhdGlvbikge1xyXG4gICAgICAgIC8vIFN3aXRjaCB0byBub3RpZmljYXRpb25zIHRhYiBhbmQgc2VsZWN0IHRoZSBub3RpZmljYXRpb25cclxuICAgICAgICBzZXREaXNwbGF5KFwibm90aWZpY2F0aW9uc1wiKTtcclxuICAgICAgICBzZXRBY3RpdmVCdXR0b24oXCJub3RpZmljYXRpb25zXCIpO1xyXG5cclxuICAgICAgICAvLyBGaW5kIHRoZSBpbmRleCBpbiB0aGUgbm90aWZpY2F0aW9ucyBhcnJheVxyXG4gICAgICAgIGNvbnN0IG5vdGlmSW5kZXggPSBub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnMuZmluZEluZGV4KFxyXG4gICAgICAgICAgKG5vdGlmKSA9PiBub3RpZi5pZCA9PT0gbm90aWZpY2F0aW9uSWRcclxuICAgICAgICApO1xyXG5cclxuICAgICAgICAvLyBBZGp1c3QgaW5kZXggYmFzZWQgb24gaG93IG5vdGlmaWNhdGlvbnMgYXJlIGRpc3BsYXllZCAobmV3ZXN0IGZpcnN0KVxyXG4gICAgICAgIGNvbnN0IGFkanVzdGVkSW5kZXggPSBub3RpZkluZGV4ID09PSBub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnMubGVuZ3RoIC0gMSA/IDAgOiBub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnMubGVuZ3RoIC0gMSAtIG5vdGlmSW5kZXg7XHJcblxyXG4gICAgICAgIHNldFNlbGVjdGVkQ2FyZChhZGp1c3RlZEluZGV4KTtcclxuICAgICAgICBzZXRTZWxlY3RlZEFubm91bmNlbWVudChub3RpZmljYXRpb24pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW25vdGlmaWNhdGlvbklkLCBub3RpZmljYXRpb25EYXRhXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJ1dHRvbkNsaWNrID0gKGJ1dHRvblR5cGUpID0+IHtcclxuICAgIHNldERpc3BsYXkoYnV0dG9uVHlwZSk7XHJcbiAgICBzZXRBY3RpdmVCdXR0b24oYnV0dG9uVHlwZSk7XHJcbiAgICBzZXRTZWxlY3RlZENhcmQobnVsbCk7XHJcbiAgICBzZXRTZWxlY3RlZEFubm91bmNlbWVudChudWxsKTtcclxuICB9O1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFub3RpZmljYXRpb25Mb2FkaW5nICYmIG5vdGlmaWNhdGlvbkRhdGE/Lm5vdGlmaWNhdGlvbnMpIHtcclxuICAgICAgc2V0Tm90aWZpY2F0aW9ucyhub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnMpO1xyXG4gICAgfVxyXG4gIH0sIFtub3RpZmljYXRpb25Mb2FkaW5nLCBub3RpZmljYXRpb25EYXRhXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoZXZlbnREYXRhICYmIGV2ZW50RGF0YS5ldmVudHMpIHtcclxuICAgICAgc2V0RXZlbnRzKGV2ZW50RGF0YS5ldmVudHMpO1xyXG4gICAgfVxyXG4gICAgaWYgKGJvb2tpbmdEYXRhICYmIGJvb2tpbmdEYXRhLmJvb2tpbmdzKSB7XHJcbiAgICAgIHNldE1lZXRpbmdzKGJvb2tpbmdEYXRhLmJvb2tpbmdzKTtcclxuICAgIH1cclxuICAgIGlmIChub3RpZmljYXRpb25EYXRhICYmIG5vdGlmaWNhdGlvbkRhdGEubm90aWZpY2F0aW9ucykge1xyXG4gICAgICBzZXROb3RpZmljYXRpb25zKG5vdGlmaWNhdGlvbkRhdGEubm90aWZpY2F0aW9ucyk7XHJcbiAgICB9XHJcbiAgfSwgW2V2ZW50RGF0YSwgYm9va2luZ0RhdGEsIG5vdGlmaWNhdGlvbkRhdGFdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ2FyZENsaWNrID0gKGluZGV4KSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENhcmQoaW5kZXgpO1xyXG4gICAgbGV0IHNlbGVjdGVkSXRlbSA9IG51bGw7XHJcbiAgICBpZiAoZGlzcGxheSA9PT0gXCJhbGxcIikge1xyXG4gICAgICBzZWxlY3RlZEl0ZW0gPSBjb21iaW5lZEl0ZW1zW2luZGV4XTtcclxuICAgIH0gZWxzZSBpZiAoZGlzcGxheSA9PT0gXCJldmVudHNcIikge1xyXG4gICAgICBzZWxlY3RlZEl0ZW0gPSBpbmRleCA8IGN1cnJlbnRFdmVudHMubGVuZ3RoID8gY3VycmVudEV2ZW50c1tpbmRleF0gOiBwcmV2aW91c0V2ZW50c1tpbmRleCAtIGN1cnJlbnRFdmVudHMubGVuZ3RoXTtcclxuICAgIH0gZWxzZSBpZiAoZGlzcGxheSA9PT0gXCJub3RpZmljYXRpb25zXCIpIHtcclxuICAgICAgc2VsZWN0ZWRJdGVtID0gaW5kZXggPT09IDAgPyBub3RpZmljYXRpb25zW25vdGlmaWNhdGlvbnMubGVuZ3RoIC0gMV0gOiBub3RpZmljYXRpb25zLnNsaWNlKDAsIC0xKVtpbmRleCAtIDFdO1xyXG4gICAgfSBlbHNlIGlmIChkaXNwbGF5ID09PSBcIm1lZXRpbmdzXCIpIHtcclxuICAgICAgc2VsZWN0ZWRJdGVtID0gaW5kZXggPCB1cGNvbWluZ01lZXRpbmdzLmxlbmd0aCA/IHVwY29taW5nTWVldGluZ3NbaW5kZXhdIDogcHJldmlvdXNNZWV0aW5nc1tpbmRleCAtIHVwY29taW5nTWVldGluZ3MubGVuZ3RoXTtcclxuICAgIH1cclxuICAgIHNldFNlbGVjdGVkQW5ub3VuY2VtZW50KHNlbGVjdGVkSXRlbSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlT3Blbk1vZGFsID0gKGFubm91bmNlbWVudCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRBbm5vdW5jZW1lbnQoYW5ub3VuY2VtZW50KTtcclxuICAgIHNldE9wZW5Nb2RhbCh0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSZXNldCA9ICgpID0+IHtcclxuICAgIHNldFNlbGVjdGVkQ2FyZChudWxsKTtcclxuICAgIHNldFNlbGVjdGVkQW5ub3VuY2VtZW50KG51bGwpO1xyXG4gICAgLy8gUmVtb3ZlIG5vdGlmaWNhdGlvbklkIGZyb20gVVJMXHJcbiAgICByb3V0ZXIucHVzaChcIi9ub3RpZmljYXRpb25zXCIsIHVuZGVmaW5lZCwgeyBzaGFsbG93OiB0cnVlIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsb3NlTW9kYWwgPSAoKSA9PiB7XHJcbiAgICBzZXRPcGVuTW9kYWwoZmFsc2UpO1xyXG4gICAgc2V0U2VsZWN0ZWRBbm5vdW5jZW1lbnQobnVsbCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgY3VycmVudERhdGUgPSBuZXcgRGF0ZSgpO1xyXG4gIGNvbnN0IHN0YXJ0T2ZXZWVrID0gY3VycmVudERhdGUuZ2V0RGF0ZSgpIC0gY3VycmVudERhdGUuZ2V0RGF5KCk7XHJcbiAgY29uc3QgZW5kT2ZXZWVrID0gc3RhcnRPZldlZWsgKyA2O1xyXG5cclxuICBjb25zdCBjdXJyZW50V2Vla1N0YXJ0ID0gbmV3IERhdGUoY3VycmVudERhdGUpO1xyXG4gIGN1cnJlbnRXZWVrU3RhcnQuc2V0RGF0ZShzdGFydE9mV2Vlayk7XHJcbiAgY29uc3QgY3VycmVudFdlZWtFbmQgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZSk7XHJcbiAgY3VycmVudFdlZWtFbmQuc2V0RGF0ZShlbmRPZldlZWspO1xyXG5cclxuICBjb25zdCBjdXJyZW50RXZlbnRzID0gZXZlbnRzLmZpbHRlcigoZXZlbnQpID0+IHtcclxuICAgIGNvbnN0IGV2ZW50RGF0ZSA9IG5ldyBEYXRlKGV2ZW50LmRhdGUpO1xyXG4gICAgcmV0dXJuIGV2ZW50RGF0ZSA+PSBjdXJyZW50V2Vla1N0YXJ0ICYmIGV2ZW50RGF0ZSA8PSBjdXJyZW50V2Vla0VuZDtcclxuICB9KTtcclxuXHJcbiAgY29uc3QgcHJldmlvdXNFdmVudHMgPSBldmVudHMuZmlsdGVyKChldmVudCkgPT4ge1xyXG4gICAgY29uc3QgZXZlbnREYXRlID0gbmV3IERhdGUoZXZlbnQuZGF0ZSk7XHJcbiAgICByZXR1cm4gZXZlbnREYXRlIDwgY3VycmVudFdlZWtTdGFydDtcclxuICB9KTtcclxuXHJcbiAgY29uc3QgdXBjb21pbmdNZWV0aW5ncyA9IG1lZXRpbmdzXHJcbiAgICAuZmlsdGVyKChtZWV0aW5nKSA9PiBuZXcgRGF0ZShtZWV0aW5nLnN0YXJ0VGltZSkgPj0gY3VycmVudERhdGUpXHJcbiAgICAuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYS5zdGFydFRpbWUpIC0gbmV3IERhdGUoYi5zdGFydFRpbWUpKTtcclxuXHJcbiAgY29uc3QgcHJldmlvdXNNZWV0aW5ncyA9IG1lZXRpbmdzXHJcbiAgICAuZmlsdGVyKChtZWV0aW5nKSA9PiBuZXcgRGF0ZShtZWV0aW5nLnN0YXJ0VGltZSkgPCBjdXJyZW50RGF0ZSlcclxuICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLnN0YXJ0VGltZSkgLSBuZXcgRGF0ZShhLnN0YXJ0VGltZSkpO1xyXG5cclxuICBjb25zdCBjdXJyZW50Tm90aWZpY2F0aW9ucyA9IG5vdGlmaWNhdGlvbnNcclxuICAgIC5maWx0ZXIoKG5vdGlmaWNhdGlvbikgPT4gbmV3IERhdGUobm90aWZpY2F0aW9uLmNyZWF0ZWRBdCkgPj0gY3VycmVudFdlZWtTdGFydClcclxuICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkgLSBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkpO1xyXG5cclxuICBjb25zdCBwcmV2aW91c05vdGlmaWNhdGlvbnMgPSBub3RpZmljYXRpb25zXHJcbiAgICAuZmlsdGVyKChub3RpZmljYXRpb24pID0+IG5ldyBEYXRlKG5vdGlmaWNhdGlvbi5jcmVhdGVkQXQpIDwgY3VycmVudFdlZWtTdGFydClcclxuICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkgLSBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkpO1xyXG5cclxuICBjb25zdCBjb21iaW5lZEl0ZW1zID0gW1xyXG4gICAgLi4uZXZlbnRzLm1hcCgoaXRlbSkgPT4gKHsgLi4uaXRlbSwgdHlwZTogXCJldmVudFwiIH0pKSxcclxuICAgIC4uLm1lZXRpbmdzLm1hcCgoaXRlbSkgPT4gKHsgLi4uaXRlbSwgdHlwZTogXCJtZWV0aW5nXCIgfSkpLFxyXG4gICAgLi4ubm90aWZpY2F0aW9ucy5tYXAoKGl0ZW0pID0+ICh7IC4uLml0ZW0sIHR5cGU6IFwibm90aWZpY2F0aW9uXCIgfSkpLFxyXG4gIF0uc29ydCgoYSwgYikgPT4ge1xyXG4gICAgY29uc3QgZGF0ZUEgPSBhLmNyZWF0ZWRBdCB8fCBhLnN0YXJ0VGltZSB8fCBhLmRhdGU7XHJcbiAgICBjb25zdCBkYXRlQiA9IGIuY3JlYXRlZEF0IHx8IGIuc3RhcnRUaW1lIHx8IGIuZGF0ZTtcclxuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlQikgLSBuZXcgRGF0ZShkYXRlQSk7XHJcbiAgfSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8RGFzaGJvYXJkPlxyXG4gICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogXCJmbGV4XCIgfX0+XHJcbiAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgIGNvbXBvbmVudD1cIm1haW5cIlxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZENvbG9yID09PSB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQucHJpbWFyeVxyXG4gICAgICAgICAgICAgICAgICA/IHRoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5zZWNvbmRhcnlcclxuICAgICAgICAgICAgICAgICAgOiBzZWxlY3RlZENvbG9yLFxyXG4gICAgICAgICAgICAgIHBhZGRpbmdMZWZ0OiBpc0xhcmdlVGFibGV0ID8gXCI1MHB4XCIgOiBcIlwiLFxyXG4gICAgICAgICAgICAgIHBhZGRpbmdSaWdodDogaXNMYXJnZVRhYmxldCA/IFwiNTBweFwiIDogXCJcIixcclxuICAgICAgICAgICAgICB3aWR0aDogaXNNZWRpdW1Nb2JpbGVcclxuICAgICAgICAgICAgICAgID8gXCIxMDAlXCJcclxuICAgICAgICAgICAgICAgIDogaXNXaWRlTW9iaWxlXHJcbiAgICAgICAgICAgICAgICAgID8gXCI0NXJlbVwiXHJcbiAgICAgICAgICAgICAgICAgIDogaXNTbWFsbFRhYmxldFxyXG4gICAgICAgICAgICAgICAgICAgID8gXCI0OHJlbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBpc1RhYmxldFxyXG4gICAgICAgICAgICAgICAgICAgICAgPyBcIjU0cmVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogXCI2MHZ3XCIsXHJcbiAgICAgICAgICAgICAgbWFyZ2luOiBcImF1dG9cIixcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPEJveD5cclxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkxlZnQ6IFwiMTVweFwiIH19PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImg1XCJcclxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMzJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE2LjAyNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luTGVmdDogaXNTdGFuZGFyZERlc2t0b3AgPyBcIjE2cHhcIiA6IFwiXCIsXHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIExhdGVzdCB1cGRhdGVzIGZyb20gSGFzc2FuYVxyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiBcIjIwcHhcIiB9fT5cclxuICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJoMVwiXHJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IGlzV2lkZVNjcmVlbiA/IFwiMjJweFwiIDogXCIyNy40N3B4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogXCI3MDBcIixcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbkxlZnQ6IGlzU3RhbmRhcmREZXNrdG9wID8gXCIzMHB4XCIgOiBcIjE1cHhcIixcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgTm90aWZpY2F0aW9uc1xyXG4gICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcInNwYWNlLWJldHdlZW5cIixcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nUmlnaHQ6IFwiMjVweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbkxlZnQ6IGlzU3RhbmRhcmREZXNrdG9wID8gXCIzMHB4XCIgOiBcIjIwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uQnV0dG9uc1xyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZUJ1dHRvbkNsaWNrPXtoYW5kbGVCdXR0b25DbGlja31cclxuICAgICAgICAgICAgICAgICAgICBhY3RpdmVCdXR0b249e2FjdGl2ZUJ1dHRvbn1cclxuICAgICAgICAgICAgICAgICAgICBpc05hcnJvd01vYmlsZT17aXNOYXJyb3dNb2JpbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgaXNXaWRlU2NyZWVuPXtpc1dpZGVTY3JlZW59XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgIHtzZWxlY3RlZENhcmQgIT09IG51bGwgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogXCJmbGV4XCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsIG1hcmdpblRvcDogXCIyMHB4XCIgfX0+XHJcbiAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXNldH1cclxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW5MZWZ0OiBpc1N0YW5kYXJkRGVza3RvcCA/IFwiMjBweFwiIDogXCI1cHhcIixcclxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE2cHhcIixcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjODg4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZ2FwOiA1LFxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9pY29ucy9hcnJvdy1sZWZ0LnN2Z1wiIGFsdD1cImFycm93XCIgaGVpZ2h0PXsxNn0gd2lkdGggcGFyYW1ldGVybj17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFubm91bmNlbWVudD8udGl0bGUgfHwgXCJOb3RpZmljYXRpb24gRGV0YWlsc1wifVxyXG4gICAgICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGdhcDogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE2cHhcIixcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPERpdmlkZXJcclxuICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiMTVweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNBN0E3QTdcIixcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LmNyZWF0ZWRBdCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM4ODhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRGb3JtYXR0ZWREYXRlKHNlbGVjdGVkQW5ub3VuY2VtZW50LmNyZWF0ZWRBdCl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LmRhdGUgJiYgIXNlbGVjdGVkQW5ub3VuY2VtZW50Py5jcmVhdGVkQXQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY2FwdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjODg4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0Rm9ybWF0dGVkRGF0ZShzZWxlY3RlZEFubm91bmNlbWVudC5kYXRlKX0g4oCUe1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAge21vbnRoTmFtZShuZXcgRGF0ZShzZWxlY3RlZEFubm91bmNlbWVudC5kYXRlKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LnN0YXJ0VGltZSAmJiAhc2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LmNyZWF0ZWRBdCAmJiAhc2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LmRhdGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY2FwdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjODg4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiBcIjRweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0Rm9ybWF0dGVkRGF0ZShzZWxlY3RlZEFubm91bmNlbWVudC5zdGFydFRpbWUpfSDigJR7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bW9udGhOYW1lKG5ldyBEYXRlKHNlbGVjdGVkQW5ub3VuY2VtZW50LnN0YXJ0VGltZSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8R3JpZFxyXG4gICAgICAgICAgICAgIGNvbnRhaW5lclxyXG4gICAgICAgICAgICAgIHNwYWNpbmc9ezN9XHJcbiAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IFwiMyVcIixcclxuICAgICAgICAgICAgICAgIGhlaWdodDogXCI3MHZoXCIsXHJcbiAgICAgICAgICAgICAgICBvdmVyZmxvdzogXCJhdXRvXCIsXHJcbiAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8R3JpZFxyXG4gICAgICAgICAgICAgICAgaXRlbVxyXG4gICAgICAgICAgICAgICAgeHM9ezEyfVxyXG4gICAgICAgICAgICAgICAgbWQ9ezEyfVxyXG4gICAgICAgICAgICAgICAgbGc9ezEyfVxyXG4gICAgICAgICAgICAgICAgb3JkZXI9e3sgeHM6IDIsIHNtOiAyLCBtZDogMiwgbGc6IDEsIHhsOiAxIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2Rpc3BsYXkgPT09IFwiYWxsXCIgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25Mb2FkaW5nIHx8IGV2ZW50TG9hZGluZyB8fCBib29raW5nTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5PkxvYWRpbmcuLi48L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb21iaW5lZEl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0V2ZW50ID0gaXRlbS50eXBlID09PSBcImV2ZW50XCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNNZWV0aW5nID0gaXRlbS50eXBlID09PSBcIm1lZXRpbmdcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc05vdGlmaWNhdGlvbiA9IGl0ZW0udHlwZSA9PT0gXCJub3RpZmljYXRpb25cIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJlc3BvbnNpdmVCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLmlkIHx8IGluZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0N1cnJlbnROb3RpZmljYXRpb249e2lzTm90aWZpY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IFwiMzAuNDFweCAxNi45MXB4IDI5LjA0cHggMTYuOTFweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblRvcDogXCIyMHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCAhPT0gbnVsbCAmJiBzZWxlY3RlZENhcmQgIT09IGluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJsb2NrXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkICE9PSBudWxsICYmIHNlbGVjdGVkQ2FyZCA9PT0gaW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImF1dG9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiMTcwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBpc05vdGlmaWNhdGlvbiB8fCBpc01lZXRpbmcgPyB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuc2Vjb25kYXJ5IDogdW5kZWZpbmVkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDYXJkQ2xpY2soaW5kZXgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNFdmVudCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFubm91bmNlbWVudENhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXtpdGVtLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGV0YWlscz17aXRlbS5kZXRhaWxzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNTZWxlY3RlZD17c2VsZWN0ZWRDYXJkID09PSBpbmRleH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGU9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXggPyBpdGVtLmRhdGUgOiBudWxsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc01lZXRpbmcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBbm5vdW5jZW1lbnRDYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17aXRlbS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRldGFpbHM9e2l0ZW0uZGV0YWlsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQ9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlPXtzZWxlY3RlZENhcmQgPT09IGluZGV4ID8gaXRlbS5zdGFydFRpbWUgOiBudWxsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpc05vdGlmaWNhdGlvbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvbkNhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5vdGlmaWNhdGlvbj17aXRlbS5ub3RpZmljYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkPXtzZWxlY3RlZENhcmQgPT09IGluZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDdXJyZW50Tm90aWZpY2F0aW9uPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hvd0RhdGU9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkID09PSBpbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7Z2V0Rm9ybWF0dGVkRGF0ZShpdGVtLmNyZWF0ZWRBdCl9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogbnVsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Jlc3BvbnNpdmVCb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICB7ZGlzcGxheSA9PT0gXCJldmVudHNcIiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAge2N1cnJlbnRFdmVudHMubWFwKChldmVudCwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxSZXNwb25zaXZlQm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17ZXZlbnQuaWQgfHwgaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQ3VycmVudE5vdGlmaWNhdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjMwLjQxcHggMTYuOTFweCAyOS4wNHB4IDE2LjkxcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhcmQgIT09IG51bGwgJiYgc2VsZWN0ZWRDYXJkICE9PSBpbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJibG9ja1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCAhPT0gbnVsbCAmJiBzZWxlY3RlZENhcmQgPT09IGluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJhdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjE3MHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNhcmRDbGljayhpbmRleCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbm5vdW5jZW1lbnRDYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNDdXJyZW50Tm90aWZpY2F0aW9uPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXtldmVudC50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkZXRhaWxzPXtldmVudC5kZXRhaWxzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGU9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXggPyBldmVudC5kYXRlIDogbnVsbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkPXtzZWxlY3RlZENhcmQgPT09IGluZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9SZXNwb25zaXZlQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIHtwcmV2aW91c0V2ZW50cy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheT1cImZsZXhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zPVwiY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tPVwiMjRweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblRvcD1cIjI0cHhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tPVwiMnB4IHNvbGlkICNFMkUwRjFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID48L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduPVwiY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbXg6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjOTQ5NDk0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxMnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBQcmV2aW91cyBFdmVudHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Qm94IGJvcmRlclRvcD1cIjJweCBzb2xpZCAjRTJFMEYxXCIgd2lkdGg9XCIxMDAlXCI+PC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIHtwcmV2aW91c0V2ZW50cy5tYXAoKGV2ZW50LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPFJlc3BvbnNpdmVCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtldmVudC5pZCB8fCBpbmRleH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuc2Vjb25kYXJ5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IFwiMzAuNDFweCAxNi45MXB4IDI5LjA0cHggMTYuOTFweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblRvcDogXCIyMHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCAhPT0gbnVsbCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhcmQgIT09IGluZGV4ICsgY3VycmVudEV2ZW50cy5sZW5ndGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIm5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmxvY2tcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhcmQgIT09IG51bGwgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkID09PSBpbmRleCArIGN1cnJlbnRFdmVudHMubGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJhdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjE3MHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ2FyZENsaWNrKGluZGV4ICsgY3VycmVudEV2ZW50cy5sZW5ndGgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEFubm91bmNlbWVudENhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17ZXZlbnQudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGV0YWlscz17ZXZlbnQuZGV0YWlsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCA9PT0gaW5kZXggKyBjdXJyZW50RXZlbnRzLmxlbmd0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlPXtzZWxlY3RlZENhcmQgPT09IGluZGV4ICsgY3VycmVudEV2ZW50cy5sZW5ndGggPyBldmVudC5kYXRlIDogbnVsbH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUJveD5cclxuICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAge2Rpc3BsYXkgPT09IFwibm90aWZpY2F0aW9uc1wiICYmIChcclxuICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5PkxvYWRpbmcuLi48L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IG5vdGlmaWNhdGlvbkRhdGE/Lm5vdGlmaWNhdGlvbnM/Lmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzQ3VycmVudE5vdGlmaWNhdGlvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCIxNnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogc2VsZWN0ZWRDYXJkICE9PSBudWxsICYmIHNlbGVjdGVkQ2FyZCAhPT0gMCA/IFwibm9uZVwiIDogXCJibG9ja1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQ2FyZENsaWNrKDApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvbkNhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5vdGlmaWNhdGlvbj17bm90aWZpY2F0aW9uRGF0YS5ub3RpZmljYXRpb25zW25vdGlmaWNhdGlvbkRhdGEubm90aWZpY2F0aW9ucy5sZW5ndGggLSAxXS5ub3RpZmljYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkPXtzZWxlY3RlZENhcmQgPT09IDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0N1cnJlbnROb3RpZmljYXRpb249e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93RGF0ZT17c2VsZWN0ZWRDYXJkID09PSAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCA9PT0gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7Z2V0Rm9ybWF0dGVkRGF0ZShub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbnNbbm90aWZpY2F0aW9uRGF0YS5ub3RpZmljYXRpb25zLmxlbmd0aCAtIDFdLmNyZWF0ZWRBdCl9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogbnVsbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUJveD5cclxuICAgICAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbkRhdGEubm90aWZpY2F0aW9ucy5sZW5ndGggPiAxICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IGRpc3BsYXk9XCJmbGV4XCIgYWxpZ25JdGVtcz1cImNlbnRlclwiIG1hcmdpbkJvdHRvbT1cIjI0cHhcIiBtYXJnaW5Ub3A9XCIyNHB4XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IGJvcmRlckJvdHRvbT1cIjJweCBzb2xpZCAjRTJFMEYxXCIgd2lkdGg9XCIxMDAlXCI+PC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBhbGlnbj1cImNlbnRlclwiIHN4PXt7IG14OiAwLCBjb2xvcjogXCIjOTQ5NDk0XCIsIGZvbnRTaXplOiBcIjEycHhcIiwgd2hpdGVTcGFjZTogXCJub3dyYXBcIiB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUHJldmlvdXMgTm90aWZpY2F0aW9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBib3JkZXJUb3A9XCIycHggc29saWQgI0UyRTBGMVwiIHdpZHRoPVwiMTAwJVwiPjwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9uRGF0YS5ub3RpZmljYXRpb25zLnNsaWNlKDAsIC0xKS5tYXAoKG5vdGlmaWNhdGlvbiwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtub3RpZmljYXRpb24uaWQgfHwgaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQuc2Vjb25kYXJ5LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjMwLjQxcHggMTYuOTFweCAyOS4wNHB4IDE2LjkxcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luVG9wOiBcIjIwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogc2VsZWN0ZWRDYXJkICE9PSBudWxsICYmIHNlbGVjdGVkQ2FyZCAhPT0gaW5kZXggKyAxID8gXCJub25lXCIgOiBcImJsb2NrXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogc2VsZWN0ZWRDYXJkID09PSBpbmRleCArIDEgPyBcImF1dG9cIiA6IFwiMTcwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDYXJkQ2xpY2soaW5kZXggKyAxKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uQ2FyZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpZmljYXRpb249e25vdGlmaWNhdGlvbi5ub3RpZmljYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQ9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXggKyAxfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0N1cnJlbnROb3RpZmljYXRpb249e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaG93RGF0ZT17c2VsZWN0ZWRDYXJkID09PSBpbmRleCArIDF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCA9PT0gaW5kZXggKyAxXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGAke2dldEZvcm1hdHRlZERhdGUobm90aWZpY2F0aW9uLmNyZWF0ZWRBdCl9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9SZXNwb25zaXZlQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeT5ObyBub3RpZmljYXRpb25zIGF2YWlsYWJsZTwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICB7ZGlzcGxheSA9PT0gXCJtZWV0aW5nc1wiICYmIChcclxuICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICB7dXBjb21pbmdNZWV0aW5ncy5tYXAoKG1lZXRpbmcsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e21lZXRpbmcuaWQgfHwgaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnNlY29uZGFyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjMwLjQxcHggMTYuOTFweCAyOS4wNHB4IDE2LjkxcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhcmQgIT09IG51bGwgJiYgc2VsZWN0ZWRDYXJkICE9PSBpbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJibG9ja1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCAhPT0gbnVsbCAmJiBzZWxlY3RlZENhcmQgPT09IGluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJhdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjE3MHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNhcmRDbGljayhpbmRleCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImJvZHkyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbj1cImNlbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG14OiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzk0OTQ5NFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogXCJub3dyYXBcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgVXBjb21pbmcgTWVldGluZ3NcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QW5ub3VuY2VtZW50Q2FyZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXttZWV0aW5nLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRldGFpbHM9e21lZXRpbmcuZGV0YWlsc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRlPXtzZWxlY3RlZENhcmQgPT09IGluZGV4ID8gbWVldGluZy5zdGFydFRpbWUgOiBudWxsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQ9e3NlbGVjdGVkQ2FyZCA9PT0gaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlT3Blbk1vZGFsKG1lZXRpbmcpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9SZXNwb25zaXZlQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIHtwcmV2aW91c01lZXRpbmdzLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5PVwiZmxleFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM9XCJjZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b209XCIyNHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luVG9wPVwiMjRweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b209XCIycHggc29saWQgI0UyRTBGMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPjwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ249XCJjZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBteDogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcIiM5NDk0OTRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6IFwibm93cmFwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFByZXZpb3VzIE1lZXRpbmdzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJveCBib3JkZXJUb3A9XCIycHggc29saWQgI0UyRTBGMVwiIHdpZHRoPVwiMTAwJVwiPjwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICB7cHJldmlvdXNNZWV0aW5ncy5tYXAoKG1lZXRpbmcsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e21lZXRpbmcuaWQgfHwgaW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnNlY29uZGFyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjMwLjQxcHggMTYuOTFweCAyOS4wNHB4IDE2LjkxcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENhcmQgIT09IG51bGwgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkICE9PSBpbmRleCArIHVwY29taW5nTWVldGluZ3MubGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJsb2NrXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkICE9PSBudWxsICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2FyZCA9PT0gaW5kZXggKyB1cGNvbWluZ01lZXRpbmdzLmxlbmd0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYXV0b1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIxNzBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNhcmRDbGljayhpbmRleCArIHVwY29taW5nTWVldGluZ3MubGVuZ3RoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbm5vdW5jZW1lbnRDYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e21lZXRpbmcudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGV0YWlscz17bWVldGluZy5kZXRhaWxzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkID09PSBpbmRleCArIHVwY29taW5nTWVldGluZ3MubGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gbWVldGluZy5zdGFydFRpbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzU2VsZWN0ZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXJkID09PSBpbmRleCArIHVwY29taW5nTWVldGluZ3MubGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU9wZW5Nb2RhbChtZWV0aW5nKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUJveD5cclxuICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICA8RGlhbG9nIG9wZW49e29wZW5Nb2RhbH0gb25DbG9zZT17aGFuZGxlQ2xvc2VNb2RhbH0gbWF4V2lkdGg9XCJzbVwiIGZ1bGxXaWR0aD5cclxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGU+XHJcbiAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LnRpdGxlIHx8IFwiTm90aWZpY2F0aW9uIERldGFpbHNcIn1cclxuICAgICAgICAgICAgICA8L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICAgIDxEaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkxXCI+XHJcbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFubm91bmNlbWVudD8ubm90aWZpY2F0aW9uIHx8IHNlbGVjdGVkQW5ub3VuY2VtZW50Py5kZXRhaWxzIHx8IFwiTm8gZGV0YWlscyBhdmFpbGFibGVcIn1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFubm91bmNlbWVudD8uZGF0ZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dFNlY29uZGFyeVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIERhdGU6IHtnZXRGb3JtYXR0ZWREYXRlKHNlbGVjdGVkQW5ub3VuY2VtZW50LmRhdGUpfSB7bW9udGhOYW1lKG5ldyBEYXRlKHNlbGVjdGVkQW5ub3VuY2VtZW50LmRhdGUpKX1cclxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFubm91bmNlbWVudD8uc3RhcnRUaW1lICYmIChcclxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0U2Vjb25kYXJ5XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgU3RhcnQgVGltZToge2dldEZvcm1hdHRlZERhdGUoc2VsZWN0ZWRBbm5vdW5jZW1lbnQuc3RhcnRUaW1lKX0ge21vbnRoTmFtZShuZXcgRGF0ZShzZWxlY3RlZEFubm91bmNlbWVudC5zdGFydFRpbWUpKX1cclxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFubm91bmNlbWVudD8uY3JlYXRlZEF0ICYmICFzZWxlY3RlZEFubm91bmNlbWVudD8uZGF0ZSAmJiAhc2VsZWN0ZWRBbm5vdW5jZW1lbnQ/LnN0YXJ0VGltZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dFNlY29uZGFyeVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIERhdGU6IHtnZXRGb3JtYXR0ZWREYXRlKHNlbGVjdGVkQW5ub3VuY2VtZW50LmNyZWF0ZWRBdCl9IHttb250aE5hbWUobmV3IERhdGUoc2VsZWN0ZWRBbm5vdW5jZW1lbnQuY3JlYXRlZEF0KSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxyXG4gICAgICAgICAgICAgIDxEaWFsb2dBY3Rpb25zPlxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDbG9zZU1vZGFsfSBjb2xvcj1cInByaW1hcnlcIj5cclxuICAgICAgICAgICAgICAgICAgQ2xvc2VcclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvRGlhbG9nQWN0aW9ucz5cclxuICAgICAgICAgICAgPC9EaWFsb2c+XHJcbiAgICAgICAgICA8L0JveD5cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9EYXNoYm9hcmQ+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgd2l0aEF1dGgoTm90aWZpY2F0aW9ucyk7Il0sIm5hbWVzIjpbImdxbCIsIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJzdHlsZWQiLCJ1c2VUaGVtZSIsIkJveCIsIkdyaWQiLCJUeXBvZ3JhcGh5IiwiQnV0dG9uIiwiRGlhbG9nIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nQWN0aW9ucyIsIkljb25CdXR0b24iLCJEaXZpZGVyIiwidXNlTWVkaWFRdWVyeSIsInVzZVF1ZXJ5IiwiTm90aWZpY2F0aW9uQnV0dG9ucyIsIkFubm91bmNlbWVudENhcmQiLCJOb3RpZmljYXRpb25DYXJkIiwiRGFzaGJvYXJkIiwidXNlQ29sb3IiLCJ1c2VTZWxlY3RlZENvbG9yIiwiZ2V0RXZlbnRzIiwiZ2V0Tm90aWZpY2F0aW9ucyIsImdldEZvcm1hdHRlZERhdGUiLCJtb250aE5hbWUiLCJnZXRCb29raW5ncyIsIndpdGhBdXRoIiwidXNlUm91dGVyIiwiUmVzcG9uc2l2ZVRleHQiLCJ0aGVtZSIsImNvbG9yIiwiZm9udFNpemUiLCJmb250RmFtaWx5IiwiZm9udFdlaWdodCIsIndvcmRXcmFwIiwibWF4V2lkdGgiLCJSZXNwb25zaXZlQm94IiwiYmFja2dyb3VuZENvbG9yIiwiZGlzYWJsZUJvcmRlciIsImlzQ3VycmVudE5vdGlmaWNhdGlvbiIsIndpZHRoIiwiaGVpZ2h0IiwiYmFja2dyb3VuZCIsInBhbGV0dGUiLCJ0eXBlIiwic2Vjb25kYXJ5IiwiYm94U2hhZG93IiwiYm9yZGVyUmFkaXVzIiwicG9zaXRpb24iLCJjb250ZW50IiwidG9wIiwibGVmdCIsIk5vdGlmaWNhdGlvbnMiLCJub3RpZmljYXRpb25EYXRhIiwicm91dGVyIiwibm90aWZpY2F0aW9uSWQiLCJxdWVyeSIsImxvYWRpbmciLCJldmVudExvYWRpbmciLCJkYXRhIiwiZXZlbnREYXRhIiwibm90aWZpY2F0aW9uTG9hZGluZyIsImJvb2tpbmdMb2FkaW5nIiwiYm9va2luZ0RhdGEiLCJzZWxlY3RlZENhcmQiLCJzZXRTZWxlY3RlZENhcmQiLCJvcGVuTW9kYWwiLCJzZXRPcGVuTW9kYWwiLCJzZWxlY3RlZEFubm91bmNlbWVudCIsInNldFNlbGVjdGVkQW5ub3VuY2VtZW50IiwiZXZlbnRzIiwic2V0RXZlbnRzIiwibm90aWZpY2F0aW9ucyIsInNldE5vdGlmaWNhdGlvbnMiLCJtZWV0aW5ncyIsInNldE1lZXRpbmdzIiwiYWN0aXZlQnV0dG9uIiwic2V0QWN0aXZlQnV0dG9uIiwiZGlzcGxheSIsInNldERpc3BsYXkiLCJpc1dpZGVTY3JlZW4iLCJpc0xhcmdlVGFibGV0IiwiaXNUYWJsZXQiLCJpc1NtYWxsVGFibGV0IiwiaXNXaWRlTW9iaWxlIiwiaXNNZWRpdW1Nb2JpbGUiLCJpc05hcnJvd01vYmlsZSIsImlzU3RhbmRhcmREZXNrdG9wIiwic2VsZWN0ZWRDb2xvciIsIm5vdGlmaWNhdGlvbiIsImZpbmQiLCJub3RpZiIsImlkIiwibm90aWZJbmRleCIsImZpbmRJbmRleCIsImFkanVzdGVkSW5kZXgiLCJsZW5ndGgiLCJoYW5kbGVCdXR0b25DbGljayIsImJ1dHRvblR5cGUiLCJib29raW5ncyIsImhhbmRsZUNhcmRDbGljayIsImluZGV4Iiwic2VsZWN0ZWRJdGVtIiwiY29tYmluZWRJdGVtcyIsImN1cnJlbnRFdmVudHMiLCJwcmV2aW91c0V2ZW50cyIsInNsaWNlIiwidXBjb21pbmdNZWV0aW5ncyIsInByZXZpb3VzTWVldGluZ3MiLCJoYW5kbGVPcGVuTW9kYWwiLCJhbm5vdW5jZW1lbnQiLCJoYW5kbGVSZXNldCIsInB1c2giLCJ1bmRlZmluZWQiLCJzaGFsbG93IiwiaGFuZGxlQ2xvc2VNb2RhbCIsImN1cnJlbnREYXRlIiwiRGF0ZSIsInN0YXJ0T2ZXZWVrIiwiZ2V0RGF0ZSIsImdldERheSIsImVuZE9mV2VlayIsImN1cnJlbnRXZWVrU3RhcnQiLCJzZXREYXRlIiwiY3VycmVudFdlZWtFbmQiLCJmaWx0ZXIiLCJldmVudCIsImV2ZW50RGF0ZSIsImRhdGUiLCJtZWV0aW5nIiwic3RhcnRUaW1lIiwic29ydCIsImEiLCJiIiwiY3VycmVudE5vdGlmaWNhdGlvbnMiLCJjcmVhdGVkQXQiLCJwcmV2aW91c05vdGlmaWNhdGlvbnMiLCJtYXAiLCJpdGVtIiwiZGF0ZUEiLCJkYXRlQiIsInN4IiwiY29tcG9uZW50IiwicHJpbWFyeSIsInBhZGRpbmdMZWZ0IiwicGFkZGluZ1JpZ2h0IiwibWFyZ2luIiwiZGl2Iiwic3R5bGUiLCJtYXJnaW5MZWZ0IiwidmFyaWFudCIsIm1hcmdpblRvcCIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm9uQ2xpY2siLCJnYXAiLCJpbWciLCJzcmMiLCJhbHQiLCJwYXJhbWV0ZXJuIiwidGl0bGUiLCJtYXJnaW5Cb3R0b20iLCJjb250YWluZXIiLCJzcGFjaW5nIiwicGFkZGluZyIsIm92ZXJmbG93IiwieHMiLCJtZCIsImxnIiwib3JkZXIiLCJzbSIsInhsIiwiaXNFdmVudCIsImlzTWVldGluZyIsImlzTm90aWZpY2F0aW9uIiwiZGV0YWlscyIsImlzU2VsZWN0ZWQiLCJzaG93RGF0ZSIsImJvcmRlckJvdHRvbSIsImFsaWduIiwibXgiLCJ3aGl0ZVNwYWNlIiwiYm9yZGVyVG9wIiwib3BlbiIsIm9uQ2xvc2UiLCJmdWxsV2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/notifications.js\n"));

/***/ })

});