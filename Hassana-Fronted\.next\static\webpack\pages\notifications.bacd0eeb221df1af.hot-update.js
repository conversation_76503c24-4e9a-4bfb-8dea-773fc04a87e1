"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/AnnouncementCard.js":
/*!********************************************!*\
  !*** ./src/components/AnnouncementCard.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Close */ \"./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AnnouncementCard = (param)=>{\n    let { title, details, isSelected, onClick, borderLeft, isCurrentAnnouncement, showDate, date, image } = param;\n    _s();\n    const [openImageModal, setOpenImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width: 1200px)\");\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // const cleanImagePath = image ? image.replace('http://localhost:3009', '') : null;\n    // const imgUpdate = 'https://hassana-api.360xpertsolutions.com' + cleanImagePath;\n    const handleCardClick = (e)=>{\n        if (image && !e.target.closest(\"a, button\")) {\n            setOpenImageModal(true);\n        }\n        if (onClick) onClick(e);\n    };\n    const handleCloseModal = (e)=>{\n        e.stopPropagation();\n        setOpenImageModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n            gap: \"15px\",\n            height: \"100%\",\n            backgroundColor: \"blue\",\n            cursor: image ? \"pointer\" : \"default\"\n        },\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                sx: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n                    gap: \"7.5px\",\n                    width: \"100%\",\n                    padding: \"0 16px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        sx: {\n                            fontWeight: 700,\n                            fontSize: \"16px\",\n                            fontFamily: \"Urbanist\",\n                            wordWrap: \"break-word\",\n                            width: \"100%\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                        sx: {\n                            width: \"122px\",\n                            border: isCurrentAnnouncement ? \"1px solid #A665E1\" : \"1px solid #00BC82\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"body2\",\n                sx: {\n                    color: theme.palette.text.primary,\n                    fontWeight: 500,\n                    fontFamily: \"Inter\",\n                    wordWrap: \"normal\",\n                    fontSize: \"14px\",\n                    wordWrap: \"break-word\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: details\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"caption\",\n                sx: {\n                    color: theme.palette.text.secondary,\n                    fontFamily: \"Inter\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: new Date(date).toLocaleDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, undefined),\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                open: openImageModal,\n                onClose: handleCloseModal,\n                \"aria-labelledby\": \"image-modal\",\n                \"aria-describedby\": \"image-modal-description\",\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backdropFilter: \"blur(5px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                    sx: {\n                        position: \"relative\",\n                        width: isMediumScreen ? \"90%\" : \"70%\",\n                        height: isMediumScreen ? \"auto\" : \"80%\",\n                        bgcolor: \"background.paper\",\n                        boxShadow: 24,\n                        p: 2,\n                        display: \"flex\",\n                        flexDirection: isMediumScreen ? \"column\" : \"row\",\n                        borderRadius: \"8px\",\n                        overflow: \"hidden\"\n                    },\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                            onClick: handleCloseModal,\n                            sx: {\n                                position: \"absolute\",\n                                right: 10,\n                                top: 10,\n                                zIndex: 1,\n                                backgroundColor: \"rgba(0,0,0,0.5)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(0,0,0,0.7)\"\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                height: isMediumScreen ? \"300px\" : \"100%\",\n                                overflow: \"hidden\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                backgroundColor: \"#f5f5f5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imgUpdate,\n                                alt: title,\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                padding: \"20px\",\n                                overflowY: \"auto\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h5\",\n                                    gutterBottom: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined),\n                                showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"subtitle1\",\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    children: new Date(date).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                                    sx: {\n                                        my: 0\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"body1\",\n                                    children: details\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 140,\n                columnNumber: 15\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementCard, \"KuuCFbfhsAlXP8yulIWuHTzVnBM=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = AnnouncementCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementCard);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementCard.js\n"));

/***/ })

});