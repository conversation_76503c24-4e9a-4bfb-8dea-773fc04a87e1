import React, { useState, useEffect } from "react";
import Fullcalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

import {
  Modal,
  TextField,
  Button,
  Typography,
  Grid,
  CircularProgress,
  FormControlLabel,
  FormGroup,
  Checkbox,
  Hidden,
  Input,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Box } from "@mui/system";
import { useTheme } from "@mui/system";
import useMediaQuery from "@mui/material/useMediaQuery";
import dayjs from "dayjs";
import Stack from "@mui/material/Stack";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { useMutation, useQuery } from "@apollo/client";
import { getResources } from "@/Data/Resource";
import {
  GET_BOOKINGS_OF_USER,
  MutationCreateBooking,
  createBooking,
  getBookings,
} from "@/Data/Booking";
import {
  convertUtcToLocal,
  formatTime,
  getDateFromPicker,
  getDateFromzPicker,
  mergeArrays,
} from "./HelperFunctions";
import { useSession } from "next-auth/react";
import axios from "axios";
import { baseUrl } from "@/Data/ApolloClient";
import SnackbarComponent from "./SnackBar";
import {
  Cancel,
  Coffee,
  LocalShipping,
  LocationCity,
  LocationOn,
  ManageAccounts,
  PhotoCamera,
  PinDrop,
} from "@mui/icons-material";

const Schedule2 = () => {
  const [registrationDoc, setRegistrationDoc] = useState("");
  const [image, setImage] = useState(registrationDoc);
  const [isImageChanged, setIsImageChanged] = useState(false);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      console.log(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result);
        setRegistrationDoc(file);
      };
      reader.readAsDataURL(file);
    }
    setIsImageChanged(true);
    console.log("isimagechange:", isImageChanged);
  };

  const handleImageRemove = () => {
    setImage(null);
    // setImage(registrationDoc: null );
  };

  const { data: session } = useSession();

  if (session) {
    console.log("User ID:", session.user.id);
    console.log("session found with Role:", session.user.role);
  }
  const [events, setEvents] = useState([]);
  // console.log(events);

  const [openModal, setOpenModal] = useState(false);

  const [meetingTitle, setMeetingTitle] = useState("");
  const [id, setId] = useState(0);
  const [uid, setUid] = useState("");
  const [meetingDescription, setMeetingDescription] = useState("");
  const [selectedRoom, setSelectedRoom] = useState(null);
  const theme = useTheme();
  const [startTime, setStartTime] = React.useState(dayjs());
  const [endTime, setEndTime] = React.useState(startTime);
  const [schedules, setSchedules] = React.useState([]);

  const [isParkingSelected, setParkingSelected] = useState(false);
  const [isTeaBoySelected, setTeaBoySelected] = useState(false);
  const [isITTechnicianSelected, setITTechnicianSelected] = useState(false);
  const [location, setLocation] = useState("");

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState(
    "Please Register Meeting form Exchange First!"
  );
  const [snackbarSeverity, setSnackbarSeverity] = useState();

  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
  } = useQuery(GET_BOOKINGS_OF_USER, {
    variables: { user_id: session.user.id },
  });

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbarOpen(false);
  };

  const user_id = session && session.user.id;
  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        const response = await axios.get(`${baseUrl}/exchange-info/${user_id}`);
        // console.log("response.data", response.data)
        const transformedSchedules = response.data.map((item) => ({
          title: item.title,
          status: item.status,
          start: item.t_start,
          end: item.t_end,
          uid: item.uid,
          location: item.location,
          // description: `Starts at ${convertUtcToLocal(
          //   item.t_start
          // )} and ends at ${convertUtcToLocal(item.t_end)}`,
        }));
        setSchedules(transformedSchedules);
        // console.log("transformedSchedules", transformedSchedules);
      } catch (error) {
        console.error("Error fetching schedules:", error);
      }
    };

    fetchSchedules();
  }, []);
  useEffect(() => {
    if (!queryLoading && !queryError && queryData && queryData.bookingsOfUser) {
      const bookingData = queryData.bookingsOfUser.map((item) => ({
        // id: item.id,
        title: item.title,
        description: item.details,
        start: item.start,
        end: item.end,
        uid: item.uid,
        location: item.location,
        user_id: item.user_id,
        teaBoy: item.teaBoy,
        parking: item.parking,
        itTechnician: item.itTechnician,
        registrationDoc: item.registrationDoc,
      }));
      setEvents(bookingData);
    }
  }, [queryLoading, queryError, queryData]);

  const handleDateClick = (arg) => {
    setSnackbarOpen(true);
    // setSnackbarMessage()
    setSnackbarSeverity("warning");
  };

  const handleCloseModal = () => {
    setMeetingTitle("");
    setMeetingDescription("");
    setSelectedRoom("");
    setOpenModal(false);
  };

  // const [createBooking] = useMutation(MutationCreateBooking);

  const handleBooking = async (e) => {
    e.preventDefault();
    console.log("hit");
    const newBooking = {
      // variables: {
      // id: id,
      location: location,
      title: meetingTitle,
      start: getDateFromPicker(startTime),
      end: getDateFromPicker(endTime),
      details: meetingDescription,
      teaBoy: isTeaBoySelected == undefined ? false : isTeaBoySelected,
      parking: isParkingSelected == undefined ? false : isParkingSelected,
      itTechnician:
        isITTechnicianSelected == undefined ? false : isITTechnicianSelected,
      uid: uid,
      user_id: session.user.id,
      registrationDoc: registrationDoc,
      // },
    };
    // console.log(newBooking);
    let response = await createBooking(newBooking, isImageChanged);
    console.log(response);
    if (response?.code == 200) {
      let data = response.data;
      setEvents([
        ...events,
        {
          id: data.id,
          title: data.title,
          start: data.start,
          user_id: data.user_id,
          end: data.end,
          description: data.details,
          parking: data.parking,
          teaBoy: data.teaBoy,
          itTechnician: data.itTechnician,
          uid: data.uid,
        },
      ]);
      setSnackbarMessage(`Booking successfully`);
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } else {
      console.log(response?.error);
      setSnackbarMessage(
        `Failed to Book: ${response?.code ? response?.code : response}`
      );
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
    handleCloseModal();
  };

  const [value, setValue] = React.useState("1");
  // const [eventOpen, handleEventOpen] = React.useState(false);
  // const [eventInfo, handleEventInfo] = React.useState(null);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const handleEventClick = (clickInfo) => {
    // Handle event click here
    console.log(clickInfo);
    const id = clickInfo.event.extendedProps.id
      ? clickInfo.event.extendedProps.id
      : 0;
    const eventTitle = clickInfo.event.title;
    const eventStart = clickInfo.event.start;
    const eventEnd = clickInfo.event.end;
    const parking = clickInfo.event.extendedProps.parking;
    const teaBoy = clickInfo.event.extendedProps.teaBoy;
    const iTTechnician = clickInfo.event.extendedProps.itTechnician;
    const location = clickInfo.event.extendedProps.location;
    const description = clickInfo.event.extendedProps.description;
    const uid = clickInfo.event.extendedProps.uid;
    const image = clickInfo.event.extendedProps.registrationDoc;

    setId(id);
    setUid(uid);
    setMeetingTitle(eventTitle);
    setMeetingDescription(description);
    setStartTime(dayjs(eventStart));
    setEndTime(dayjs(eventEnd));
    setOpenModal(true);
    setParkingSelected(parking == "true" ? true : false);
    setTeaBoySelected(teaBoy == "true" ? true : false);
    setITTechnicianSelected(iTTechnician == "true" ? true : false);
    setLocation(location);
    setImage(image);
  };
  // console.log(mergeArrays(schedules, events));
  return (
    <div>
      {queryLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <CircularProgress color="secondary" />
        </Box>
      ) : (
        <>
          {schedules && events && (
            <Box>
              <Fullcalendar
                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                initialView={"dayGridMonth"}
                weekends={true}
                // events={exchangeBooking}
                // events={schedules}
                // events={tempBooking}
                events={mergeArrays(schedules, events)}
                height={"75vh"}
                selectable="true"
                selectHelper="true"
                headerToolbar={{
                  start: "prev,title,next",
                  end: "dayGridMonth,timeGridWeek,timeGridDay",
                  width: "100%",
                }}
                dateClick={handleDateClick}
                eventTimeFormat={{
                  hour: "2-digit",
                  minute: "2-digit",
                }}
                eventClick={handleEventClick}
                eventContent={(eventInfo) => {
                  return (
                    <div
                      style={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        padding: "10px",
                      }}
                    >
                      <p>{eventInfo.event.title} </p>
                      <p>
                        {" "}
                        {" (" +
                          eventInfo.event.start.toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}{" "}
                        -{" "}
                        {eventInfo.event.end.toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        }) + ")"}
                      </p>
                      {eventInfo.event.extendedProps.location && (
                        <p style={{ fontWeight: "900" }}>
                          <PinDrop fontSize="small" sx={{ color: "#00BC82" }} />
                          {eventInfo.event.extendedProps.location}
                        </p>
                      )}
                      {eventInfo.event.extendedProps.teaBoy == "true" && (
                        <Coffee style={{ color: "#c499ea" }} />
                      )}
                      {eventInfo.event.extendedProps.parking == "true" && (
                        <LocalShipping style={{ color: "#c499ea" }} />
                      )}
                      {eventInfo.event.extendedProps.itTechnician == "true" && (
                        <ManageAccounts style={{ color: "#c499ea" }} />
                      )}
                    </div>
                  );
                }}
                style={{ zIndex: 1 }}
              />

              {/* slack bar */}
              <SnackbarComponent
                open={snackbarOpen}
                handleClose={handleCloseSnackbar}
                severity={snackbarSeverity}
                message={snackbarMessage}
              />
              {/* Material-UI Modal */}
              <Modal open={openModal} onClose={handleCloseModal}>
                <Box
                  style={{
                    padding: "20px",
                    backgroundColor: "white",
                    borderRadius: "8px",
                    width: "50%",
                    margin: "auto",
                    marginTop: "60px",
                    backgroundColor: theme.palette.background.primary,
                  }}
                >
                  <Typography variant="h5">Event</Typography>
                  <form onSubmit={handleBooking}>
                    <TextField
                      label="Meeting Title"
                      id="title"
                      variant="outlined"
                      fullWidth
                      margin="normal"
                      value={meetingTitle}
                      required
                      onChange={(e) => setMeetingTitle(e.target.value)}
                      disabled
                    />
                    <Grid container spacing={2}>
                      {/* First row: Start and End fields */}
                      <Grid item sm={12} md={6}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <Stack spacing={2} sx={{ width: "100%" }}>
                            <DateTimePicker
                              value={startTime}
                              onChange={setStartTime}
                              minDate={dayjs()}
                              disabled
                            />
                          </Stack>
                        </LocalizationProvider>
                      </Grid>
                      <Grid item sm={12} md={6}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <Stack spacing={2} sx={{ width: "100%" }}>
                            <DateTimePicker
                              value={endTime}
                              onChange={setEndTime}
                              referenceDate={dayjs("2022-04-17T15:30")}
                              minDate={dayjs()}
                              disabled
                            />
                          </Stack>
                        </LocalizationProvider>
                      </Grid>
                      <Grid item xs={12} sm={12}>
                        <FormGroup aria-label="position" row>
                          <FormControlLabel
                            value="parking"
                            control={
                              <Checkbox
                                checked={isParkingSelected}
                                onChange={() =>
                                  setParkingSelected(!isParkingSelected)
                                }
                                name="parking"
                              />
                            }
                            label="Parking"
                            labelPlacement="end"
                          />
                          <FormControlLabel
                            value="teaBoy"
                            control={
                              <Checkbox
                                checked={isTeaBoySelected}
                                onChange={() =>
                                  setTeaBoySelected(!isTeaBoySelected)
                                }
                                name="teaBoy"
                              />
                            }
                            label="Tea-Boy"
                            labelPlacement="end"
                          />
                          <FormControlLabel
                            value="itTechnician"
                            control={
                              <Checkbox
                                checked={isITTechnicianSelected}
                                onChange={() =>
                                  setITTechnicianSelected(
                                    !isITTechnicianSelected
                                  )
                                }
                                name="itTechnician"
                              />
                            }
                            label="IT-Technician"
                            labelPlacement="end"
                          />
                        </FormGroup>
                        {isParkingSelected == true && (
                          <Box>
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={handleImageChange}
                              endAdornment={
                                <InputAdornment position="end">
                                  <IconButton
                                    component="label"
                                    htmlFor="imageInput"
                                  >
                                    <PhotoCamera />
                                  </IconButton>
                                </InputAdornment>
                              }
                              inputProps={{
                                id: "imageInput",
                                style: { display: "none" },
                              }}
                            />
                            {image && (
                              <div sx={{ margin: "auto" }}>
                                <img
                                  src={image}
                                  alt="Selected"
                                  style={{
                                    maxWidth: "100%",
                                    maxHeight: "200px",
                                  }}
                                />
                                <IconButton onClick={handleImageRemove}>
                                  <Cancel />
                                </IconButton>
                              </div>
                            )}
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                    <TextField
                      label="Meeting Description"
                      variant="outlined"
                      fullWidth
                      margin="normal"
                      multiline
                      rows={4}
                      value={meetingDescription}
                      onChange={(e) => setMeetingDescription(e.target.value)}
                      required
                    />
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Button
                          type=""
                          variant="outlined"
                          fullWidth
                          sx={{
                            color: "#A665E1",
                            borderColor: "#A665E1",
                          }}
                          onClick={handleCloseModal}
                        >
                          Cancel
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Button
                          type="submit"
                          variant="contained"
                          fullWidth
                          onClick={handleBooking}
                          sx={{
                            backgroundColor: "#A665E1!important",
                          }}
                        >
                          Add
                        </Button>
                      </Grid>
                    </Grid>
                  </form>
                </Box>
              </Modal>
            </Box>
          )}
        </>
      )}
    </div>
  );
};

export default Schedule2;
