"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/components/AnnouncementDialog.jsx":
/*!***********************************************!*\
  !*** ./src/components/AnnouncementDialog.jsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Dialog,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Dialog,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @emotion/react */ \"./node_modules/@emotion/react/dist/emotion-react.browser.development.esm.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/imageUtils */ \"./src/utils/imageUtils.js\");\n// File: AnnouncementDialog.js\n\nvar _s = $RefreshSig$();\n\n\n\n// import ResponsiveBox from './ResponsiveBox';\n\n\nconst AnnouncementDialog = (param)=>{\n    let { open, handleCloseModal, title, details, ResponsiveBox, imageUrl, image } = param;\n    _s();\n    const theme = (0,_emotion_react__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    // Use the utility function to get proper image URL\n    const imageDisplayUrl = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_3__.getAnnouncementImageUrl)(image);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n        open: open,\n        onClose: handleCloseModal,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n            sx: {\n                height: image ? \"auto\" : \"425px\",\n                minHeight: \"425px\"\n            },\n            disableBorder: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    style: {\n                        backgroundColor: \"#003e53\",\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        // marginTop: \"10px\",\n                        padding: \"15px\",\n                        borderTop: \"3px solid #b484cc\",\n                        borderBottom: \"3px solid #b484cc\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: \"/images/HassanaLogos.png\",\n                            alt: \"Hassana Logos\",\n                            width: 100,\n                            height: 50\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                            style: {\n                                color: \"white\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        \" \"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    sx: {\n                        width: \"100%\",\n                        maxHeight: \"300px\",\n                        overflow: \"hidden\",\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        alignItems: \"center\",\n                        backgroundColor: \"#f5f5f5\",\n                        padding: \"20px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: getImageDisplayUrl(),\n                        alt: title,\n                        style: {\n                            maxWidth: \"100%\",\n                            maxHeight: \"280px\",\n                            objectFit: \"contain\",\n                            borderRadius: \"8px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    sx: {\n                        // position:\"absolute\",\n                        padding: {\n                            lg: \"70px 29px 100px 25px\",\n                            xl: \"70px 29px 100px 25px\",\n                            md: \"70px 29px 100px 25px\",\n                            sm: \"70px 29px 90px 25px\",\n                            xs: \"70px 29px 70px 25px\"\n                        }\n                    },\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"3px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                            style: {\n                                // color: \"#1B3745\",\n                                marginBottom: \"8px\",\n                                fontSize: \"16px\",\n                                fontWeight: \"bold\"\n                            },\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                            borderTop: \"3px solid #b484cc\",\n                            width: \"40%\",\n                            marginBottom: \"10px\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                            style: {\n                                fontSize: \"12px\",\n                                marginBottom: \"8px\",\n                                color: \"black\",\n                                color: \"#BBB\"\n                            },\n                            children: details\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Dialog_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                        sx: {\n                            marginTop: \"8px\",\n                            paddingLeft: \"27px\",\n                            // color: \"#1B3745\",\n                            fontSize: \"13px\"\n                        },\n                        children: \"Human Resources Department\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementDialog.jsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementDialog, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _emotion_react__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = AnnouncementDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementDialog);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementDialog.jsx\n"));

/***/ })

});