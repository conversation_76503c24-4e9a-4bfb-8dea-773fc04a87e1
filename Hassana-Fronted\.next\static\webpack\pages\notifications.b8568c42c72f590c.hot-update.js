"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/WeatherComponent.jsx":
/*!*********************************************!*\
  !*** ./src/components/WeatherComponent.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Data/ApolloClient */ \"./src/Data/ApolloClient.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,CircularProgress,Container,Typography,useTheme!=!@mui/material */ \"__barrel_optimize__?names=Alert,Box,CircularProgress,Container,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HelperFunctions */ \"./src/components/HelperFunctions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst WeatherComponent = ()=>{\n    _s();\n    const [weatherData, setWeatherData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const theme = (0,_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (navigator.geolocation) {\n            navigator.geolocation.getCurrentPosition((position)=>{\n                const lat = position.coords.latitude;\n                const lon = position.coords.longitude;\n                fetchWeatherData(lat, lon);\n            }, ()=>{\n                // this function will be called if the user denies permission\n                const defaultLat = 24.6877;\n                const defaultLon = 46.7219;\n                fetchWeatherData(defaultLat, defaultLon);\n            });\n        } else {\n            setError(\"Geolocation is not supported by this browser.\");\n        }\n    }, []);\n    function fetchWeatherData(lat, lon) {\n        // Check if the backend weather endpoint exists, otherwise use mock data\n        fetch(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__.baseUrl, \"/weather?lat=\").concat(lat, \"&lon=\").concat(lon) // Updated to a more standard endpoint\n        ).then((response)=>{\n            if (!response.ok) {\n                // If the endpoint doesn't exist, use mock data\n                if (response.status === 404) {\n                    throw new Error(\"Weather service not available\");\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            return response.json();\n        }).then((data)=>{\n            setWeatherData(data.data || data);\n            setLoading(false);\n        }).catch((error)=>{\n            console.warn(\"Weather API not available, using mock data:\", error.message);\n            setLoading(false);\n        });\n    }\n    const formatDateTime = (unixTime)=>{\n        const date = new Date(unixTime * 1000);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 71,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n            severity: \"error\",\n            children: error\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 75,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!weatherData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"No weather data available.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 79,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!weatherData.weather || !weatherData.main) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Weather data is incomplete.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, undefined);\n    }\n    const weatherIconUrl = \"/weatherIcons/\".concat(weatherData.weather[0].icon, \".svg\");\n    // const weatherIconUrl = `/weatherIcons/01d.png`;\n    return(// <Container>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n        sx: {\n            background: theme.palette.background.secondary,\n            borderRadius: \"10px\",\n            boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n            padding: \"20px\",\n            // margin: \"75px 0px 20px 0px\", \n            height: \"100%\",\n            marginTop: \"20px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                variant: \"h6\",\n                fontSize: \"1.3rem\",\n                fontWeight: \"700\",\n                marginX: \"5px\",\n                children: \"Weather \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                sx: {\n                    // display: \"flex\",\n                    // flexDirection: \"column\",\n                    // justifyContent: \"center\",\n                    // background: theme.palette.background.secondary,\n                    // borderRadius: \"10px\",\n                    // boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n                    // padding: \"20px\",\n                    margin: \"75px 0px 20px 0px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                        variant: \"body1\",\n                        sx: {\n                            fontWeight: \"600\",\n                            fontSize: \"1rem\"\n                        },\n                        children: [\n                            \"Current Weather in \",\n                            weatherData.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                        sx: {\n                            margin: \"10px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                    variant: \"body2\",\n                                    sx: {\n                                        fontWeight: \"600\",\n                                        lineHeight: \"0px\",\n                                        fontSize: \"1rem\"\n                                    },\n                                    children: (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_4__.getCurrentTime)()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                sx: {\n                                    margin: \"30px 0px 0px\",\n                                    display: \"flex\",\n                                    justifyContent: \"center\",\n                                    alignItems: \"center\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    // src='/weatherIcons/09d.svg'\n                                    src: weatherIconUrl,\n                                    alt: weatherData.weather[0].description,\n                                    width: 125,\n                                    height: 125,\n                                    style: {\n                                        WebkitFilter: \"drop-shadow(5px 5px 5px #666666)\",\n                                        filter: \"drop-shadow(5px 5px 5px #666666)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                sx: {\n                                    marginTop: \"10px\",\n                                    display: \"flex\",\n                                    justifyContent: \"space-around\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        marginLeft: \"20px\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                            variant: \"h3\",\n                                            children: [\n                                                Math.round(weatherData.main.temp - 273.15),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sup\", {\n                                                    children: \"o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"C\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Box, {\n                                        sx: {\n                                            marginLeft: \"10px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    fontSize: \"1rem\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                children: weatherData.weather[0].main\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                sx: {\n                                                    width: \"100%\",\n                                                    fontSize: \"1rem\",\n                                                    fontWeight: \"600\"\n                                                },\n                                                variant: \"body2\",\n                                                children: [\n                                                    \"RealFeel \",\n                                                    Math.round(weatherData.main.feels_like - 273.15),\n                                                    \"\\xb0C\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\WeatherComponent.jsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined));\n};\n_s(WeatherComponent, \"b1U2Hh1bAxGZAriWxe/OVE1OvOg=\", false, function() {\n    return [\n        _barrel_optimize_names_Alert_Box_CircularProgress_Container_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = WeatherComponent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherComponent);\nvar _c;\n$RefreshReg$(_c, \"WeatherComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/WeatherComponent.jsx\n"));

/***/ })

});