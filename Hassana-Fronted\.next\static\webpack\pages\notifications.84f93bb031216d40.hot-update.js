"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/AnnouncementCard.js":
/*!********************************************!*\
  !*** ./src/components/AnnouncementCard.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Close */ \"./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AnnouncementCard = (param)=>{\n    let { title, details, isSelected, onClick, borderLeft, isCurrentAnnouncement, showDate, date, image } = param;\n    _s();\n    const [openImageModal, setOpenImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width: 1200px)\");\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    //\n    const handleCardClick = (e)=>{\n        if (image && !e.target.closest(\"a, button\")) {\n            setOpenImageModal(true);\n        }\n        if (onClick) onClick(e);\n    };\n    const handleCloseModal = (e)=>{\n        e.stopPropagation();\n        setOpenImageModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n            gap: \"15px\",\n            height: \"100%\",\n            backgroundColor: \"blue\",\n            cursor: image ? \"pointer\" : \"default\"\n        },\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                sx: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n                    gap: \"7.5px\",\n                    width: \"100%\",\n                    padding: \"0 16px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        sx: {\n                            fontWeight: 700,\n                            fontSize: \"16px\",\n                            fontFamily: \"Urbanist\",\n                            wordWrap: \"break-word\",\n                            width: \"100%\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                        sx: {\n                            width: \"122px\",\n                            border: isCurrentAnnouncement ? \"1px solid #A665E1\" : \"1px solid #00BC82\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"body2\",\n                sx: {\n                    color: theme.palette.text.primary,\n                    fontWeight: 500,\n                    fontFamily: \"Inter\",\n                    wordWrap: \"normal\",\n                    fontSize: \"14px\",\n                    wordWrap: \"break-word\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: details\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"caption\",\n                sx: {\n                    color: theme.palette.text.secondary,\n                    fontFamily: \"Inter\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: new Date(date).toLocaleDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined),\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                open: openImageModal,\n                onClose: handleCloseModal,\n                \"aria-labelledby\": \"image-modal\",\n                \"aria-describedby\": \"image-modal-description\",\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backdropFilter: \"blur(5px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                    sx: {\n                        position: \"relative\",\n                        width: isMediumScreen ? \"90%\" : \"70%\",\n                        height: isMediumScreen ? \"auto\" : \"80%\",\n                        bgcolor: \"background.paper\",\n                        boxShadow: 24,\n                        p: 2,\n                        display: \"flex\",\n                        flexDirection: isMediumScreen ? \"column\" : \"row\",\n                        borderRadius: \"8px\",\n                        overflow: \"hidden\"\n                    },\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                            onClick: handleCloseModal,\n                            sx: {\n                                position: \"absolute\",\n                                right: 10,\n                                top: 10,\n                                zIndex: 1,\n                                backgroundColor: \"rgba(0,0,0,0.5)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(0,0,0,0.7)\"\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                height: isMediumScreen ? \"300px\" : \"100%\",\n                                overflow: \"hidden\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                backgroundColor: \"#f5f5f5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imgUpdate,\n                                alt: title,\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                padding: \"20px\",\n                                overflowY: \"auto\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h5\",\n                                    gutterBottom: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined),\n                                showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"subtitle1\",\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    children: new Date(date).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                                    sx: {\n                                        my: 0\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"body1\",\n                                    children: details\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 139,\n                columnNumber: 15\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementCard, \"KuuCFbfhsAlXP8yulIWuHTzVnBM=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = AnnouncementCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementCard);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementCard.js\n"));

/***/ })

});