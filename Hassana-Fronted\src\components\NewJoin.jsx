import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ade,
  Box,
  Typography,
  Avatar,
  CircularProgress,
  Button,
} from "@mui/material";
import { useTheme } from "@mui/system";
import { getAllUsers } from "@/Data/User";
import { Grid } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useSession } from "next-auth/react";
import UserCard from "./UserCard";

// Define breakpoints for responsive design
const breakpoints = {
  smallScreen: "(max-width: 600px)",
  mediumScreen: "(max-width: 960px)",
  largeScreen: "(max-width: 1280px)",
  xLargeScreen: "(max-width: 1440px)",
  xxLargeScreen: "(max-width: 1920px)",
  xxxLargeScreen: "(max-width: 2560px)",
  xxxxLargeScreen: "(min-width: 2561px)",
};

// JoinerBox component to display users
const JoinerBox = ({ title, subtitle, users = [], loading, error }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState({});
  const [userCardOpen, setUserCardOpen] = useState({});
  const [showAllUsers, setShowAllUsers] = useState(false);
  const Baseurl = "https://hassana-api.360xpertsolutions.com/v1/"; // Ensure trailing slash

  // Debug logs
  console.log(`=== JoinerBox (${title}) ===`);
  console.log("Users:", users);
  console.log("Loading:", loading);
  console.log("Error:", error);
  console.log("showAllUsers:", showAllUsers);

  const handlePopoverOpen = (event, index) => {
    setAnchorEl((prev) => ({ ...prev, [index]: event.currentTarget }));
  };

  const handlePopoverClose = (index) => {
    setAnchorEl((prev) => ({ ...prev, [index]: null }));
  };

  const visibleUsers = showAllUsers ? users : users.slice(0, 4);
  const extraUsersCount = users.length - 4;

  console.log("visibleUsers:", visibleUsers);
  console.log("extraUsersCount:", extraUsersCount);

  return (
    <Box
      sx={{
        background: theme.palette.background.secondary,
        borderRadius: "10px",
        boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
        padding: { xs: "15px", sm: "20px" },
        cursor: "default",
        minHeight: { xs: "180px", sm: "200px", md: "220px" },
        height: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontSize: { xs: "1.1rem", sm: "1.2rem", md: "1.3rem" },
          fontWeight: "700",
          lineHeight: 1.2,
        }}
      >
        {title}
      </Typography>
      <Typography
        sx={{
          fontSize: { xs: "0.85rem", sm: "0.9rem", md: "1rem" },
          fontWeight: "100",
          lineHeight: 1.3,
          marginTop: { xs: "5px", sm: "8px" },
        }}
      >
        {subtitle}
      </Typography>

      <Box
        sx={{
          marginTop: { xs: "15px", sm: "20px" },
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        {loading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
            }}
          >
            <CircularProgress color="secondary" />
          </Box>
        ) : error ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
            }}
          >
            <Typography
              color="error"
              sx={{ marginY: "10px", fontSize: { xs: "0.9rem", sm: "1rem" } }}
            >
              Error: {error}
            </Typography>
          </Box>
        ) : users.length > 0 ? (
          <>
            <Box
              sx={{
                display: "flex",
                overflowX: "auto", // Slider effect
                gap: { xs: 0.5, sm: 1 },
                maxWidth: "100%",
                padding: "10px",
                flexWrap: "nowrap", // Reverted to nowrap for slider
                justifyContent: { xs: "flex-start", sm: "flex-start" },
                "&::-webkit-scrollbar": {
                  height: "8px", // Increased for visibility
                },
                "&::-webkit-scrollbar-track": {
                  backgroundColor: "rgba(0,0,0,0.1)",
                  borderRadius: "3px",
                },
                "&::-webkit-scrollbar-thumb": {
                  backgroundColor: "#A665E1",
                  borderRadius: "3px",
                  "&:hover": {
                    backgroundColor: "#9555d1",
                  },
                },
              }}
            >
              {visibleUsers.map((user, index) => {
                // Validate user data
                if (!user?.id || !user?.name) {
                  console.warn(`Invalid user data at index ${index}:`, user);
                  return null;
                }
                const avatarSrc = user.profile
                  ? `${Baseurl}${user.profile}`
                  : "/default-avatar.png";
                console.log(`Avatar src for ${user.name}:`, avatarSrc);

                return (
                  <React.Fragment key={user.id}>
                    <Avatar
                      alt={user.name}
                      src={avatarSrc}
                      onMouseEnter={(e) => handlePopoverOpen(e, index)}
                      onMouseLeave={() => handlePopoverClose(index)}
                      onClick={() =>
                        setUserCardOpen((prev) => ({
                          ...prev,
                          [index]: true,
                        }))
                      }
                      sx={{
                        cursor: "pointer",
                        flexShrink: 0,
                        width: { xs: 40, sm: 45, md: 50 }, // Slightly larger
                        height: { xs: 40, sm: 45, md: 50 },
                      }}
                    />
                    <Popper
                      open={Boolean(anchorEl[index])}
                      anchorEl={anchorEl[index]}
                      placement="top"
                      transition
                      disablePortal
                    >
                      {({ TransitionProps }) => (
                        <Fade {...TransitionProps} timeout={350}>
                          <Box
                            sx={{
                              border: 1,
                              padding: "8px",
                              backgroundColor: theme.palette.background.primary,
                              borderRadius: "8px",
                            }}
                          >
                            <Typography>{user.name}</Typography>
                          </Box>
                        </Fade>
                      )}
                    </Popper>
                    <UserCard
                      open={userCardOpen[index] || false}
                      setOpen={(value) =>
                        setUserCardOpen((prev) => ({
                          ...prev,
                          [index]: value,
                        }))
                      }
                      data={user}
                    />
                  </React.Fragment>
                );
              })}
              {!showAllUsers && extraUsersCount > 0 && (
                <Avatar
                  sx={{
                    bgcolor: "#aaa",
                    cursor: "pointer",
                    flexShrink: 0,
                    width: { xs: 40, sm: 45, md: 50 },
                    height: { xs: 40, sm: 45, md: 50 },
                    fontSize: { xs: "0.8rem", sm: "0.9rem", md: "1rem" },
                  }}
                  onClick={() => {
                    console.log("Toggling showAllUsers to true");
                    setShowAllUsers(true);
                  }}
                >
                  +{extraUsersCount}
                </Avatar>
              )}
            </Box>
            {showAllUsers && extraUsersCount > 0 && (
              <Button
                onClick={() => {
                  console.log("Toggling showAllUsers to false");
                  setShowAllUsers(false);
                }}
                sx={{ marginTop: "10px", color: "#A665E1" }}
              >
                Show Less
              </Button>
            )}
          </>
        ) : (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
            }}
          >
            <Typography
              variant="h5"
              sx={{
                marginY: "10px",
                fontSize: { xs: "1rem", sm: "1.2rem", md: "1.5rem" },
                color: "text.secondary",
              }}
            >
              No users found. Check back later!
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

// Main NewJoin component
const NewJoin = () => {
  const [newJoiners, setNewJoiners] = useState([]);
  const [culturalAmbassadors, setCulturalAmbassadors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { data: session, status: sessionStatus } = useSession();
  const [page] = useState(1); // Single page fetch with pageSize 100
  const [pageSize] = useState(100); // Set to 100 as requested

  // Responsive breakpoints
  const isSmallScreen = useMediaQuery(breakpoints.smallScreen);

  useEffect(() => {
    const fetchUsers = async () => {
      if (sessionStatus === "loading" || !session?.accessToken) return;

      setLoading(true);
      setError(null);

      try {
        console.log("=== fetchUsers Debug ===");
        console.log("Page:", page);
        console.log("PageSize:", pageSize);
        console.log("Token:", session.accessToken?.substring(0, 20) + "...");

        const response = await getAllUsers(session.accessToken, page, pageSize);
        console.log("getAllUsers response:", response);

        if (response.error) {
          setError(response.error);
          setNewJoiners([]);
          setCulturalAmbassadors([]);
        } else {
          const users = response.data || [];
          console.log("Fetched users:", users);

          // Filter users
          const newJoiners = users.filter(
            (user) => user.is_cultural_ambassador !== "true" && user.id && user.name
          );
          const culturalAmbassadors = users.filter(
            (user) => user.is_cultural_ambassador === "true" && user.id && user.name
          );

          console.log("Filtered newJoiners:", newJoiners);
          console.log("Filtered culturalAmbassadors:", culturalAmbassadors);

          setNewJoiners(newJoiners);
          setCulturalAmbassadors(culturalAmbassadors);
        }
      } catch (err) {
        console.error("Fetch users error:", err);
        setError(err.message || "Failed to fetch users");
        setNewJoiners([]);
        setCulturalAmbassadors([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [session, sessionStatus, page, pageSize]);

  // Debug logs
  console.log("=== NewJoin Render ===");
  console.log("Session status:", sessionStatus);
  console.log("Session:", session);
  console.log("newJoiners:", newJoiners);
  console.log("culturalAmbassadors:", culturalAmbassadors);
  console.log("Loading:", loading);
  console.log("Error:", error);
  console.log("Page:", page);
  console.log("PageSize:", pageSize);

  if (sessionStatus === "loading") {
    return (
      <Box sx={{ marginY: "20px", display: "flex", justifyContent: "center" }}>
        <CircularProgress color="secondary" />
      </Box>
    );
  }

  if (!session) {
    return (
      <Box sx={ { marginY: "20px", textAlign: "center" }}>
        <Typography color="error">
          Please log in to view new joiners and cultural ambassadors.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ marginY: "20px" }}>
      {error && (
        <Typography color="error" sx={{ textAlign: "center", marginY: "10px" }}>
          Error: {error}
        </Typography>
      )}
      <Grid container spacing={isSmallScreen ? 1 : 3}>
        <Grid item xs={12} sm={12} md={6} lg={6} sx={{ cursor: "default" }}>
          <JoinerBox
            title="Welcome Hassana New Joiners"
            subtitle="Welcome Hassana and our new team members! We're excited to start this journey together."
            users={newJoiners}
            loading={loading}
            error={error}
          />
        </Grid>
        <Grid item xs={12} sm={12} md={6} lg={6}>
          <JoinerBox
            title="Cultural Ambassadors"
            subtitle="Celebrating our team members who promote our culture and values."
            users={culturalAmbassadors}
            loading={loading}
            error={error}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default NewJoin;