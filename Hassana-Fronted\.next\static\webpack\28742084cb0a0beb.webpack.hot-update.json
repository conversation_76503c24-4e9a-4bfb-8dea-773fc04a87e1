{"c": ["webpack"], "r": ["pages/notifications", "/_error"], "m": ["./node_modules/@mui/icons-material/Close.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CDELL%5CDesktop%5Chassana%5CHassana-Fronted%5Csrc%5Cpages%5Cnotifications.js&page=%2Fnotifications!", "./src/components/AnnouncementCard.js", "./src/components/NotificationButtons.jsx", "./src/components/NotificationCard.jsx", "./src/pages/notifications.js", "__barrel_optimize__?names=Box,Button!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=<PERSON>,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=<PERSON>,<PERSON><PERSON>r,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=Box,Divider,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CDELL%5CDesktop%5Chassana%5CHassana-Fronted%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}