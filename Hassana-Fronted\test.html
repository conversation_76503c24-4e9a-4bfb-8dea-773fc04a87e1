<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Call Example</title>
</head>
<body>

<script>
  function makeAPICall() {
    const url = 'http://localhost:3001/test2';
    const xhr = new XMLHttpRequest();

    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
        //   const response = JSON.parse(xhr.responseText);
          console.log(xhr.responseText);
        } else {
          console.error('Error:', xhr.status);
        }
      }
    };

    xhr.open('GET', url, true);
    xhr.send();
  }

  // Call the function when the page loads or when needed
  makeAPICall();
</script>

</body>
</html>
