"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/components/Header/Header.js":
/*!*****************************************!*\
  !*** ./src/components/Header/Header.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/material/Drawer */ \"./node_modules/@mui/material/Drawer/index.js\");\n/* harmony import */ var _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _mui_material_Box__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Box */ \"./node_modules/@mui/material/Box/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/material/AppBar */ \"./node_modules/@mui/material/AppBar/index.js\");\n/* harmony import */ var _mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/material/Toolbar */ \"./node_modules/@mui/material/Toolbar/index.js\");\n/* harmony import */ var _mui_material_List__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/material/List */ \"./node_modules/@mui/material/List/index.js\");\n/* harmony import */ var _mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/material/Typography */ \"./node_modules/@mui/material/Typography/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!@mui/icons-material */ \"__barrel_optimize__?names=Campaign,Celebration,DarkMode,FormatQuote,LightMode,LocalOffer,Notifications,NotificationsActive,NotificationsActiveRounded,Task!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/icons-material/ArrowForward */ \"./node_modules/@mui/icons-material/ArrowForward.js\");\n/* harmony import */ var _mui_icons_material_ArrowBack__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/ArrowBack */ \"./node_modules/@mui/icons-material/ArrowBack.js\");\n/* harmony import */ var _mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/Menu */ \"./node_modules/@mui/icons-material/Menu.js\");\n/* harmony import */ var _mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @mui/icons-material/ExpandLess */ \"./node_modules/@mui/icons-material/ExpandLess.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"./node_modules/@mui/icons-material/ExpandMore.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"./node_modules/@mui/icons-material/Settings.js\");\n/* harmony import */ var _mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @mui/icons-material/Newspaper */ \"./node_modules/@mui/icons-material/Newspaper.js\");\n/* harmony import */ var _mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/Circle */ \"./node_modules/@mui/icons-material/Circle.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/Badge */ \"./node_modules/@mui/material/Badge/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!@mui/material */ \"__barrel_optimize__?names=ClickAwayListener,Collapse,ListItem,ListItemButton,ListItemIcon,ListItemText,MenuItem,MenuList,Paper,Popper,Slide,Zoom,keyframes!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _DrawerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DrawerContext */ \"./src/components/Header/DrawerContext.js\");\n/* harmony import */ var _ModeContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ModeContext */ \"./src/components/ModeContext.jsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Announcement__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Announcement */ \"./src/Data/Announcement.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,CircularProgress,Divider,Snackbar!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _ListItems__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ListItems */ \"./src/components/ListItems.jsx\");\n/* harmony import */ var _Profile__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../Profile */ \"./src/components/Profile.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_14__);\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { transform: scale(1); }\\n  50% { transform: scale(1.1); }\\n  100% { transform: scale(1); }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0%, 100% { transform: translateX(0); }\\n  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }\\n  20%, 40%, 60%, 80% { transform: translateX(2px); }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  0% { box-shadow: 0 0 5px #ff4444; }\\n  50% { box-shadow: 0 0 20px #ff4444, 0 0 30px #ff4444; }\\n  100% { box-shadow: 0 0 5px #ff4444; }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst drawerWidth = \"17rem\";\n// Keyframes for notification animations\nconst pulse = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject());\nconst shake = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject1());\nconst glow = (0,_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.keyframes)(_templateObject2());\nconst AnimatedBadge = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Badge__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        \"& .MuiBadge-badge\": {\n            backgroundColor: \"#ff4444\",\n            color: \"white\",\n            fontWeight: \"bold\",\n            fontSize: \"12px\",\n            minWidth: \"20px\",\n            height: \"20px\",\n            borderRadius: \"10px\",\n            border: \"2px solid white\",\n            animation: hasNewNotifications ? \"\".concat(pulse, \" 2s infinite, \").concat(glow, \" 2s infinite\") : \"none\",\n            boxShadow: \"0 2px 8px rgba(255, 68, 68, 0.3)\"\n        }\n    };\n});\n_c = AnimatedBadge;\nconst AnimatedNotificationIcon = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"])((param)=>{\n    let { theme, hasNewNotifications } = param;\n    return {\n        animation: hasNewNotifications ? \"\".concat(shake, \" 0.5s ease-in-out\") : \"none\",\n        \"&:hover\": {\n            transform: \"scale(1.1)\",\n            transition: \"transform 0.2s ease-in-out\"\n        }\n    };\n});\n_c1 = AnimatedNotificationIcon;\nconst AppBar = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_AppBar__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        zIndex: theme.zIndex.drawer + 1,\n        transition: theme.transitions.create([\n            \"width\",\n            \"margin\"\n        ], {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.leavingScreen\n        }),\n        marginLeft: open ? drawerWidth : 0,\n        width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"100%\",\n        [theme.breakpoints.up(\"sm\")]: {\n            marginLeft: open ? drawerWidth : theme.spacing(9),\n            width: open ? \"calc(100% - \".concat(drawerWidth, \")\") : \"calc(100% - \".concat(theme.spacing(9), \")\")\n        },\n        [theme.breakpoints.down(\"xs\")]: {\n            marginLeft: 0,\n            width: \"100%\"\n        },\n        ...open && {\n            transition: theme.transitions.create([\n                \"width\",\n                \"margin\"\n            ], {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.enteringScreen\n            })\n        }\n    };\n});\n_c2 = AppBar;\nconst Drawer = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.styled)(_mui_material_Drawer__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n    shouldForwardProp: (prop)=>prop !== \"open\"\n})((param)=>{\n    let { theme, open } = param;\n    return {\n        \"& .MuiDrawer-paper\": {\n            backgroundColor: theme.palette.background.secondary,\n            position: \"relative\",\n            whiteSpace: \"nowrap\",\n            width: open ? drawerWidth : theme.spacing(7),\n            transition: theme.transitions.create(\"width\", {\n                easing: theme.transitions.easing.sharp,\n                duration: theme.transitions.duration.complex\n            }),\n            boxSizing: \"border-box\",\n            ...!open && {\n                overflowX: \"hidden\",\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.leavingScreen\n                }),\n                width: theme.spacing(7),\n                [theme.breakpoints.up(\"sm\")]: {\n                    width: theme.spacing(9)\n                },\n                [theme.breakpoints.down(\"xs\")]: {\n                    width: \"100%\"\n                }\n            }\n        }\n    };\n});\n_c3 = Drawer;\n// Enhanced Social Media Style Notification Popper\nconst SocialNotificationPopper = (param)=>/*#__PURE__*/ {\n    let { open, anchorEl, onClose, notifications, loading, removeHandler, selectedColor, theme } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n        open: open,\n        anchorEl: anchorEl,\n        role: undefined,\n        transition: true,\n        sx: {\n            maxHeight: notifications.length > 4 ? \"70vh\" : \"auto\",\n            overflowY: notifications.length > 4 ? \"auto\" : \"visible\",\n            zIndex: 9999,\n            width: \"400px\",\n            maxWidth: \"90vw\"\n        },\n        disablePortal: true,\n        popperOptions: {\n            modifiers: [\n                {\n                    name: \"offset\",\n                    options: {\n                        offset: [\n                            0,\n                            15\n                        ]\n                    }\n                },\n                {\n                    name: \"preventOverflow\",\n                    options: {\n                        padding: 20\n                    }\n                }\n            ]\n        },\n        children: (param)=>/*#__PURE__*/ {\n            let { TransitionProps } = param;\n            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Zoom, {\n                ...TransitionProps,\n                timeout: 300,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                    elevation: 24,\n                    sx: {\n                        borderRadius: \"16px\",\n                        overflow: \"hidden\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                        backdropFilter: \"blur(10px)\",\n                        background: \"linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%)\",\n                        boxShadow: \"0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                        onClickAway: onClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                maxWidth: \"400px\",\n                                minWidth: \"320px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        p: 2,\n                                        borderBottom: \"1px solid rgba(0,0,0,0.1)\",\n                                        background: theme.palette.background.header,\n                                        color: theme.palette.text.white\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            fontWeight: 600,\n                                            fontSize: \"16px\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                sx: {\n                                                    fontSize: \"20px\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Notifications\",\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    backgroundColor: \"rgba(255,255,255,0.2)\",\n                                                    borderRadius: \"12px\",\n                                                    px: 1,\n                                                    py: 0.5,\n                                                    fontSize: \"12px\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: notifications.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        maxHeight: \"400px\",\n                                        overflowY: \"auto\"\n                                    },\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            justifyContent: \"center\",\n                                            p: 4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 19\n                                    }, undefined) : selectedNotification ? // Show notification details\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        sx: {\n                                            p: 2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    mb: 2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        onClick: onBackToList,\n                                                        sx: {\n                                                            mr: 1,\n                                                            p: 0.5\n                                                        },\n                                                        size: \"small\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowBack__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: \"18px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            fontSize: \"16px\",\n                                                            fontWeight: 600\n                                                        },\n                                                        children: \"Notification Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Divider, {\n                                                sx: {\n                                                    mb: 2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                variant: \"body1\",\n                                                sx: {\n                                                    fontSize: \"14px\",\n                                                    lineHeight: 1.6,\n                                                    color: theme.palette.text.primary,\n                                                    mb: 2\n                                                },\n                                                children: selectedNotification.notification\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                variant: \"body2\",\n                                                sx: {\n                                                    fontSize: \"12px\",\n                                                    color: theme.palette.text.secondary,\n                                                    fontStyle: \"italic\"\n                                                },\n                                                children: new Date(selectedNotification.createdAt).toLocaleDateString(\"en-US\", {\n                                                    year: \"numeric\",\n                                                    month: \"long\",\n                                                    day: \"numeric\",\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 268,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                        children: [\n                                            notifications.length > 0 ? notifications.map((notificationData, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notification-details/\".concat(notificationData.id),\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        onClick: ()=>removeHandler(notificationData.id),\n                                                        sx: {\n                                                            p: 2,\n                                                            borderBottom: index < notifications.length - 1 ? \"1px solid rgba(0,0,0,0.05)\" : \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(102, 126, 234, 0.05)\",\n                                                                cursor: \"pointer\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\",\n                                                            position: \"relative\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 2,\n                                                                    alignItems: \"flex-start\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            width: 40,\n                                                                            height: 40,\n                                                                            borderRadius: \"50%\",\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            justifyContent: \"center\",\n                                                                            flexShrink: 0,\n                                                                            boxShadow: \"0 4px 12px rgba(102, 126, 234, 0.3)\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                                            sx: {\n                                                                                color: \"white\",\n                                                                                fontSize: \"20px\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 31\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1,\n                                                                            minWidth: 0\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"body1\",\n                                                                                sx: {\n                                                                                    fontSize: \"14px\",\n                                                                                    fontWeight: 500,\n                                                                                    lineHeight: \"20px\",\n                                                                                    color: \"#333\",\n                                                                                    mb: 0.5,\n                                                                                    wordBreak: \"break-word\"\n                                                                                },\n                                                                                children: notificationData.notification\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                variant: \"caption\",\n                                                                                sx: {\n                                                                                    fontSize: \"12px\",\n                                                                                    color: \"#666\",\n                                                                                    display: \"flex\",\n                                                                                    alignItems: \"center\",\n                                                                                    gap: 0.5\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        sx: {\n                                                                                            fontSize: \"4px\"\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                        lineNumber: 379,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.formatDateTimeUTC)(notificationData.createdAt)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                sx: {\n                                                                    position: \"absolute\",\n                                                                    left: 0,\n                                                                    top: 0,\n                                                                    bottom: 0,\n                                                                    width: \"3px\",\n                                                                    borderRadius: \"0 2px 2px 0\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, notificationData.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 25\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"center\",\n                                                    p: 4,\n                                                    textAlign: \"center\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                        sx: {\n                                                            fontSize: \"48px\",\n                                                            color: \"#ccc\",\n                                                            mb: 2\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"h6\",\n                                                        sx: {\n                                                            color: \"#666\",\n                                                            mb: 1\n                                                        },\n                                                        children: \"No new notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            color: \"#999\"\n                                                        },\n                                                        children: \"You're all caught up!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                sx: {\n                                                    p: 2,\n                                                    borderTop: \"1px solid rgba(0,0,0,0.1)\",\n                                                    background: \"rgba(102, 126, 234, 0.02)\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                    href: \"/notifications\",\n                                                    passHref: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        component: \"a\",\n                                                        variant: \"body2\",\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\",\n                                                            fontSize: \"14px\",\n                                                            fontWeight: 600,\n                                                            textDecoration: \"none\",\n                                                            color: \"#667eea\",\n                                                            \"&:hover\": {\n                                                                color: \"#764ba2\",\n                                                                transform: \"translateX(2px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: [\n                                                            \"View All Notifications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ArrowForward__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: \"16px\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 422,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined);\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 188,\n        columnNumber: 3\n    }, undefined);\n};\n_c4 = SocialNotificationPopper;\nfunction Header() {\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const { open, setOpen } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_DrawerContext__WEBPACK_IMPORTED_MODULE_7__.DrawerContext);\n    const { mode, setMode } = (0,_ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode)();\n    const { setGlobalColor } = (0,_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const isMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession)();\n    const isAdmin = (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === \"ADMIN\";\n    const notificationAnchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [moreItem, setMoreItem] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [notificationOpen, setNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [selectedIcon, setSelectedIcon] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n        lineNumber: 478,\n        columnNumber: 52\n    }, this));\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"success\");\n    const [hasNewNotifications, setHasNewNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [previousNotificationCount, setPreviousNotificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastNotificationTime, setLastNotificationTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [enablePolling, setEnablePolling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedNotification1, setSelectedNotification] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const logoSource = \"/HassanaLogoD.png\";\n    const drawerVariant = isMobile && !open ? \"temporary\" : \"permanent\";\n    const selectedColor = (0,_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor)(_HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.color);\n    const userId = (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id) || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.user_id);\n    const { loading, error, data } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 30000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\",\n        notifyOnNetworkStatusChange: true\n    });\n    const { data: unseenCountData, loading: unseenCountLoading, refetch: refetchUnseenCount } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getUnseenNotificationsCount, {\n        variables: {\n            userId: userId ? userId : undefined\n        },\n        skip: !userId || status !== \"authenticated\",\n        pollInterval: enablePolling ? 10000 : 0,\n        fetchPolicy: \"cache-and-network\",\n        errorPolicy: \"all\"\n    });\n    const [addNotificationView] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useMutation)(_Data_Announcement__WEBPACK_IMPORTED_MODULE_10__.mutationAddNotificationView);\n    const [markAllAsSeen] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useMutation)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.mutationMarkAllNotificationsAsSeen);\n    const playNotificationSound = ()=>{\n        try {\n            const audio = new Audio(\"/sounds/notification.mp3\");\n            audio.volume = 0.5;\n            audio.play().catch((e)=>console.log(\"Could not play notification sound:\", e));\n        } catch (error) {\n            console.log(\"Notification sound not available:\", error);\n        }\n    };\n    const showBrowserNotification = (message)=>{\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(\"Hassana Portal\", {\n                body: message,\n                icon: \"/favicon.ico\",\n                badge: \"/favicon.ico\",\n                tag: \"hassana-notification\",\n                requireInteraction: false,\n                silent: false\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"Notification\" in window && Notification.permission === \"default\") {\n            Notification.requestPermission();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (status === \"authenticated\" && !userId) {\n            console.warn(\"User ID is missing in authenticated session:\", session === null || session === void 0 ? void 0 : session.user);\n        }\n        console.log(\"=== Session Debug ===\");\n        console.log(\"Session Status:\", status);\n        console.log(\"User Object:\", session === null || session === void 0 ? void 0 : session.user);\n        console.log(\"User ID:\", userId);\n    }, [\n        session,\n        status,\n        userId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        console.log(\"=== Notification Backend Debug ===\");\n        console.log(\"Loading:\", loading);\n        console.log(\"Error:\", error);\n        console.log(\"Data:\", data === null || data === void 0 ? void 0 : data.notifications);\n        console.log(\"User ID:\", userId);\n        if (!loading && !error && (data === null || data === void 0 ? void 0 : data.notifications)) {\n            const allNotifications = data.notifications;\n            const currentCount = allNotifications.length;\n            console.log(\"Backend Connected Successfully!\");\n            console.log(\"All notifications received:\", allNotifications);\n            console.log(\"Count:\", currentCount);\n            setEnablePolling(true);\n            if (currentCount > previousNotificationCount && previousNotificationCount > 0) {\n                setHasNewNotifications(true);\n                setLastNotificationTime(Date.now());\n                playNotificationSound();\n                if (currentCount > previousNotificationCount) {\n                    const newNotificationCount = currentCount - previousNotificationCount;\n                    const message = newNotificationCount === 1 ? \"You have a new notification!\" : \"You have \".concat(newNotificationCount, \" new notifications!\");\n                    showBrowserNotification(message);\n                    setSnackbarMessage(message);\n                    setSnackbarSeverity(\"info\");\n                    setSnackbarOpen(true);\n                }\n                setTimeout(()=>{\n                    setHasNewNotifications(false);\n                }, 1000);\n            }\n            setNotifications(allNotifications);\n            setPreviousNotificationCount(currentCount);\n            console.log(\"Notification count updated to: \".concat(currentCount));\n        } else if (error) {\n            console.error(\"Backend Connection Error:\", error);\n            if (error.graphQLErrors) {\n                console.error(\"GraphQL Errors:\", error.graphQLErrors.map((e)=>e.message));\n            }\n            if (error.networkError) {\n                console.error(\"Network Error:\", error.networkError);\n            }\n            setEnablePolling(false);\n            setSnackbarMessage(\"Failed to load notifications. Retrying in 30 seconds...\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            setTimeout(()=>{\n                setEnablePolling(true);\n            }, 30000);\n        } else if (!userId) {\n            console.warn(\"No user ID found in session\");\n        } else if (!loading && !data) {\n            console.warn(\"No data received from backend\");\n        }\n    }, [\n        loading,\n        error,\n        data,\n        previousNotificationCount,\n        userId\n    ]);\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                p: 4\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.CircularProgress, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 630,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n            lineNumber: 629,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleDrawer = ()=>setOpen(!open);\n    const handleSetMoreItemClick = ()=>setMoreItem(!moreItem);\n    const handleClick = (event)=>setAnchorEl(event.currentTarget);\n    const handleClose = ()=>setAnchorEl(null);\n    const handleNotificationToggle = async ()=>{\n        const wasOpen = notificationOpen;\n        setNotificationOpen((prev)=>!prev);\n        if (!wasOpen && userId) {\n            try {\n                await markAllAsSeen({\n                    variables: {\n                        userId\n                    }\n                });\n                refetchUnseenCount();\n                console.log(\"All notifications marked as seen\");\n            } catch (error) {\n                console.error(\"Error marking notifications as seen:\", error);\n            }\n        }\n    };\n    const handleNotificationClose = (event)=>{\n        var _notificationAnchorRef_current;\n        if ((_notificationAnchorRef_current = notificationAnchorRef.current) === null || _notificationAnchorRef_current === void 0 ? void 0 : _notificationAnchorRef_current.contains(event.target)) return;\n        setNotificationOpen(false);\n        setSelectedNotification(null); // Reset selected notification when closing\n    };\n    const handleNotificationClick = (notification)=>{\n        setSelectedNotification(notification);\n    };\n    const handleBackToList = ()=>{\n        setSelectedNotification(null);\n    };\n    const handleThemeChange = (theme, icon)=>{\n        setMode(theme);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const handleColorChange = (color, icon)=>{\n        setGlobalColor(color);\n        setSelectedIcon(icon);\n        handleClose();\n    };\n    const removeAnnouncementHandler = async (notificationId)=>{\n        if (!userId) {\n            setSnackbarMessage(\"User not authenticated\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n            return;\n        }\n        try {\n            const response = await addNotificationView({\n                variables: {\n                    notificationId: notificationId,\n                    userId: userId\n                }\n            });\n            if (response.data.addNotificationView) {\n                const updatedNotifications = notifications.filter((n)=>n.id !== notificationId);\n                setNotifications(updatedNotifications);\n                setPreviousNotificationCount(updatedNotifications.length);\n                setSnackbarMessage(\"Notification marked as viewed\");\n                setSnackbarSeverity(\"success\");\n                setSnackbarOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Error marking notification:\", error);\n            setSnackbarMessage(\"Failed to mark notification\");\n            setSnackbarSeverity(\"error\");\n            setSnackbarOpen(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (mode === \"light\" || mode === \"dark\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AppBar, {\n                position: \"fixed\",\n                open: open,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    sx: {\n                        position: \"absolute\",\n                        width: \"100%\",\n                        backgroundColor: theme.palette.background.header,\n                        zIndex: 1,\n                        borderTop: \"4px solid \".concat(theme.palette.text.purple),\n                        borderBottom: \"4px solid \".concat(theme.palette.text.purple),\n                        [theme.breakpoints.down(\"xs\")]: {\n                            flexDirection: \"column\"\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            edge: \"start\",\n                            \"aria-label\": \"Toggle drawer\",\n                            onClick: toggleDrawer,\n                            sx: {\n                                marginRight: \"15px\"\n                            },\n                            children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                src: \"/NavIcons/left_hamburger.svg\",\n                                alt: \"Close drawer\",\n                                width: 24,\n                                height: 24\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 743,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Menu__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                sx: {\n                                    color: theme.palette.text.white\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 750,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Typography__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            component: \"h1\",\n                            variant: \"h6\",\n                            color: \"inherit\",\n                            noWrap: true,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    src: logoSource,\n                                    alt: \"Hassana Logo\",\n                                    loading: \"lazy\",\n                                    width: 180,\n                                    height: 42\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 761,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 760,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 753,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: {\n                                    xs: 0.5,\n                                    sm: 1,\n                                    md: 1.5\n                                },\n                                flexShrink: 0,\n                                [theme.breakpoints.down(\"xs\")]: {\n                                    flexDirection: \"row\",\n                                    gap: 0.25\n                                }\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\",\n                                        \"&::after\": {\n                                            content: \"''\",\n                                            position: \"absolute\",\n                                            right: \"-8px\",\n                                            top: \"50%\",\n                                            transform: \"translateY(-50%)\",\n                                            width: \"1px\",\n                                            height: \"24px\",\n                                            backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                            [theme.breakpoints.down(\"sm\")]: {\n                                                display: \"none\"\n                                            }\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        \"aria-label\": \"Change theme or color\",\n                                        \"aria-controls\": \"theme-menu\",\n                                        \"aria-haspopup\": \"true\",\n                                        onClick: handleClick,\n                                        sx: {\n                                            color: \"inherit\",\n                                            padding: {\n                                                xs: \"6px\",\n                                                sm: \"8px\"\n                                            },\n                                            \"&:hover\": {\n                                                backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                transform: \"scale(1.05)\"\n                                            },\n                                            transition: \"all 0.2s ease-in-out\"\n                                        },\n                                        children: selectedIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 800,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 782,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Popper, {\n                                    id: \"theme-menu\",\n                                    open: Boolean(anchorEl),\n                                    anchorEl: anchorEl,\n                                    placement: \"bottom-end\",\n                                    transition: true,\n                                    sx: {\n                                        zIndex: 10000\n                                    },\n                                    children: (param)=>/*#__PURE__*/ {\n                                        let { TransitionProps } = param;\n                                        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Slide, {\n                                            ...TransitionProps,\n                                            direction: \"down\",\n                                            timeout: 350,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Paper, {\n                                                sx: {\n                                                    background: theme.palette.background.secondary,\n                                                    borderRadius: \"25px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ClickAwayListener, {\n                                                    onClickAway: handleClose,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuList, {\n                                                        autoFocusItem: Boolean(anchorEl),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"light\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LightMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleThemeChange(\"dark\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.DarkMode, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"blue\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.blue.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.blue.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"green\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.green.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.green.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.MenuItem, {\n                                                                onClick: ()=>handleColorChange(\"purple\", /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        sx: {\n                                                                            fill: theme.palette.purple.main\n                                                                        }\n                                                                    }, void 0, false, void 0, void 0)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Circle__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    sx: {\n                                                                        fill: theme.palette.purple.main\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 828,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, this);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    sx: {\n                                        position: \"relative\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedNotificationIcon, {\n                                        hasNewNotifications: hasNewNotifications,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            color: \"inherit\",\n                                            ref: notificationAnchorRef,\n                                            onClick: handleNotificationToggle,\n                                            \"aria-label\": \"Show \".concat(notifications.length, \" notifications} notifications\"),\n                                            sx: {\n                                                position: \"relative\",\n                                                color: \"inherit\",\n                                                padding: {\n                                                    xs: \"8px\",\n                                                    sm: \"10px\"\n                                                },\n                                                \"&:hover\": {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\",\n                                                    transform: \"scale(1.05)\"\n                                                },\n                                                transition: \"all 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(AnimatedBadge, {\n                                                badgeContent: (unseenCountData === null || unseenCountData === void 0 ? void 0 : unseenCountData.unseenNotificationsCount) || 0,\n                                                hasNewNotifications: hasNewNotifications,\n                                                max: 99,\n                                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActive, {\n                                                    sx: {\n                                                        color: hasNewNotifications ? \"#ff4444\" : \"inherit\",\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        },\n                                                        filter: hasNewNotifications ? \"drop-shadow(0 0 8px rgba(255, 68, 70, 0.5))\" : \"none\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Notifications, {\n                                                    sx: {\n                                                        fontSize: {\n                                                            xs: \"20px\",\n                                                            sm: \"24px\"\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 875,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(SocialNotificationPopper, {\n                                    open: notificationOpen,\n                                    anchorEl: notificationAnchorRef.current,\n                                    onClose: handleNotificationClose,\n                                    notifications: notifications,\n                                    loading: loading,\n                                    removeHandler: removeAnnouncementHandler,\n                                    selectedColor: selectedColor,\n                                    theme: theme,\n                                    selectedNotification: selectedNotification1,\n                                    onNotificationClick: handleNotificationClick,\n                                    onBackToList: handleBackToList\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 770,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 723,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 722,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Drawer, {\n                variant: drawerVariant,\n                open: open,\n                sx: {\n                    zIndex: 2,\n                    borderRight: mode === \"light\" ? \"1px solid white\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    sx: {\n                        backgroundColor: theme.palette.background.primary,\n                        margin: \"10px\",\n                        borderRadius: \"0.625rem\",\n                        height: \"100%\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Toolbar__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"auto\",\n                                justifyContent: \"flex-end\",\n                                px: [\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_Profile__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                lineNumber: 955,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 946,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                            component: \"nav\",\n                            sx: {\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                justifyContent: \"space-between\",\n                                height: \"80vh\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.MainListItems, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                    onClick: handleSetMoreItemClick,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                sx: {\n                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                            primary: \"Admin Settings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        moreItem ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandLess__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 50\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.Collapse, {\n                                                    in: moreItem,\n                                                    timeout: \"auto\",\n                                                    unmountOnExit: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_List__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        component: \"div\",\n                                                        disablePadding: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/news\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_icons_material_Newspaper__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 984,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"News\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 989,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/announcements\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Campaign, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 995,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Announcements\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 999,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/events\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Celebration, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1005,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1004,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Events\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1003,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/quotes\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.FormatQuote, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1015,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1014,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Quotes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/adminOffer\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.LocalOffer, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1025,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1024,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Offers\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1029,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1022,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/notifications\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.NotificationsActiveRounded, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1035,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1034,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1033,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/admin/leaves\",\n                                                                passHref: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemButton, {\n                                                                    sx: {\n                                                                        height: 30\n                                                                    },\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemIcon, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Campaign_Celebration_DarkMode_FormatQuote_LightMode_LocalOffer_Notifications_NotificationsActive_NotificationsActiveRounded_Task_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__.Task, {\n                                                                                sx: {\n                                                                                    color: mode !== \"light\" ? \"white\" : \"inherit\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                                lineNumber: 1045,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1044,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_ClickAwayListener_Collapse_ListItem_ListItemButton_ListItemIcon_ListItemText_MenuItem_MenuList_Paper_Popper_Slide_Zoom_keyframes_mui_material__WEBPACK_IMPORTED_MODULE_15__.ListItemText, {\n                                                                            primary: \"Leaves\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                            lineNumber: 1049,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                    lineNumber: 1043,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                                lineNumber: 1042,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                        lineNumber: 981,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 967,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_mui_material_Box__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_ListItems__WEBPACK_IMPORTED_MODULE_12__.SecondaryListItems, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                        lineNumber: 1058,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                                    lineNumber: 1057,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                            lineNumber: 957,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 938,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 930,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Snackbar, {\n                open: snackbarOpen,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbarOpen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Alert_CircularProgress_Divider_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_23__.Alert, {\n                    severity: snackbarSeverity,\n                    onClose: ()=>setSnackbarOpen(false),\n                    sx: {\n                        width: \"100%\"\n                    },\n                    children: snackbarMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                    lineNumber: 1068,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\Header\\\\Header.js\",\n                lineNumber: 1063,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"wJg79I1fszMX9D61H1BtuoMZjmQ=\", false, function() {\n    return [\n        _ModeContext__WEBPACK_IMPORTED_MODULE_8__.useMode,\n        _ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_16__.useTheme,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_11__.useSession,\n        _HelperFunctions__WEBPACK_IMPORTED_MODULE_3__.useSelectedColor,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useMutation\n    ];\n});\n_c5 = Header;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"AnimatedBadge\");\n$RefreshReg$(_c1, \"AnimatedNotificationIcon\");\n$RefreshReg$(_c2, \"AppBar\");\n$RefreshReg$(_c3, \"Drawer\");\n$RefreshReg$(_c4, \"SocialNotificationPopper\");\n$RefreshReg$(_c5, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9IZWFkZXIvSGVhZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3FDO0FBQ2tDO0FBQ2Y7QUFDWDtBQUtqQjtBQUNRO0FBQ29CO0FBQ1g7QUFDRDtBQUNOO0FBQ1k7QUFDQTtBQVlyQjtBQUNtQztBQUNOO0FBQ1Y7QUFDUTtBQUNBO0FBQ0E7QUFDRTtBQUNOO0FBQ3JCO0FBQ1M7QUFDWDtBQWVOO0FBQ29CO0FBQ0s7QUFDUDtBQUNjO0FBTTFCO0FBQ3FDO0FBQ3JCO0FBQzhCO0FBQ1Y7QUFDaEM7QUFDTztBQUV4QyxNQUFNd0UsY0FBYztBQUVwQix3Q0FBd0M7QUFDeEMsTUFBTUMsUUFBUXJCLGdOQUFTQTtBQU12QixNQUFNc0IsUUFBUXRCLGdOQUFTQTtBQU12QixNQUFNdUIsT0FBT3ZCLGdOQUFTQTtBQU10QixNQUFNd0IsZ0JBQWdCdEUsNkRBQU1BLENBQUNnQyw0REFBS0EsRUFBRTtRQUFDLEVBQUV1QyxLQUFLLEVBQUVDLG1CQUFtQixFQUFFO1dBQU07UUFDdkUscUJBQXFCO1lBQ25CQyxpQkFBaUI7WUFDakJyRSxPQUFPO1lBQ1BzRSxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEMsUUFBUTtZQUNSQyxXQUFXUixzQkFDUCxHQUF5QkgsT0FBdEJGLE9BQU0sa0JBQXFCLE9BQUxFLE1BQUssa0JBQzlCO1lBQ0pZLFdBQVc7UUFDYjtJQUNGO0FBQUE7S0FmTVg7QUFpQk4sTUFBTVksMkJBQTJCbEYsNkRBQU1BLENBQUNNLDBEQUFHQSxFQUFFO1FBQUMsRUFBRWlFLEtBQUssRUFBRUMsbUJBQW1CLEVBQUU7V0FBTTtRQUNoRlEsV0FBV1Isc0JBQXNCLEdBQVMsT0FBTkosT0FBTSx1QkFBcUI7UUFDL0QsV0FBVztZQUNUZSxXQUFXO1lBQ1hDLFlBQVk7UUFDZDtJQUNGO0FBQUE7TUFOTUY7QUFRTixNQUFNRyxTQUFTckYsNkRBQU1BLENBQUNRLDZEQUFTQSxFQUFFO0lBQy9COEUsbUJBQW1CLENBQUNDLE9BQVNBLFNBQVM7QUFDeEMsR0FBRztRQUFDLEVBQUVoQixLQUFLLEVBQUVpQixJQUFJLEVBQUU7V0FBTTtRQUN2QkMsUUFBUWxCLE1BQU1rQixNQUFNLENBQUNDLE1BQU0sR0FBRztRQUM5Qk4sWUFBWWIsTUFBTW9CLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO1lBQUM7WUFBUztTQUFTLEVBQUU7WUFDeERDLFFBQVF0QixNQUFNb0IsV0FBVyxDQUFDRSxNQUFNLENBQUNDLEtBQUs7WUFDdENDLFVBQVV4QixNQUFNb0IsV0FBVyxDQUFDSSxRQUFRLENBQUNDLGFBQWE7UUFDcEQ7UUFDQUMsWUFBWVQsT0FBT3RCLGNBQWM7UUFDakNnQyxPQUFPVixPQUFPLGVBQTJCLE9BQVp0QixhQUFZLE9BQUs7UUFDOUMsQ0FBQ0ssTUFBTTRCLFdBQVcsQ0FBQ0MsRUFBRSxDQUFDLE1BQU0sRUFBRTtZQUM1QkgsWUFBWVQsT0FBT3RCLGNBQWNLLE1BQU04QixPQUFPLENBQUM7WUFDL0NILE9BQU9WLE9BQ0gsZUFBMkIsT0FBWnRCLGFBQVksT0FDM0IsZUFBZ0MsT0FBakJLLE1BQU04QixPQUFPLENBQUMsSUFBRztRQUN0QztRQUNBLENBQUM5QixNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUMsTUFBTSxFQUFFO1lBQzlCTCxZQUFZO1lBQ1pDLE9BQU87UUFDVDtRQUNBLEdBQUlWLFFBQVE7WUFDVkosWUFBWWIsTUFBTW9CLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDO2dCQUFDO2dCQUFTO2FBQVMsRUFBRTtnQkFDeERDLFFBQVF0QixNQUFNb0IsV0FBVyxDQUFDRSxNQUFNLENBQUNDLEtBQUs7Z0JBQ3RDQyxVQUFVeEIsTUFBTW9CLFdBQVcsQ0FBQ0ksUUFBUSxDQUFDUSxjQUFjO1lBQ3JEO1FBQ0YsQ0FBQztJQUNIO0FBQUE7TUExQk1sQjtBQTRCTixNQUFNbUIsU0FBU3hHLDZEQUFNQSxDQUFDRSw2REFBU0EsRUFBRTtJQUMvQm9GLG1CQUFtQixDQUFDQyxPQUFTQSxTQUFTO0FBQ3hDLEdBQUc7UUFBQyxFQUFFaEIsS0FBSyxFQUFFaUIsSUFBSSxFQUFFO1dBQU07UUFDdkIsc0JBQXNCO1lBQ3BCZixpQkFBaUJGLE1BQU1rQyxPQUFPLENBQUNDLFVBQVUsQ0FBQ0MsU0FBUztZQUNuREMsVUFBVTtZQUNWQyxZQUFZO1lBQ1pYLE9BQU9WLE9BQU90QixjQUFjSyxNQUFNOEIsT0FBTyxDQUFDO1lBQzFDakIsWUFBWWIsTUFBTW9CLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDLFNBQVM7Z0JBQzVDQyxRQUFRdEIsTUFBTW9CLFdBQVcsQ0FBQ0UsTUFBTSxDQUFDQyxLQUFLO2dCQUN0Q0MsVUFBVXhCLE1BQU1vQixXQUFXLENBQUNJLFFBQVEsQ0FBQ2UsT0FBTztZQUM5QztZQUNBQyxXQUFXO1lBQ1gsR0FBSSxDQUFDdkIsUUFBUTtnQkFDWHdCLFdBQVc7Z0JBQ1g1QixZQUFZYixNQUFNb0IsV0FBVyxDQUFDQyxNQUFNLENBQUMsU0FBUztvQkFDNUNDLFFBQVF0QixNQUFNb0IsV0FBVyxDQUFDRSxNQUFNLENBQUNDLEtBQUs7b0JBQ3RDQyxVQUFVeEIsTUFBTW9CLFdBQVcsQ0FBQ0ksUUFBUSxDQUFDQyxhQUFhO2dCQUNwRDtnQkFDQUUsT0FBTzNCLE1BQU04QixPQUFPLENBQUM7Z0JBQ3JCLENBQUM5QixNQUFNNEIsV0FBVyxDQUFDQyxFQUFFLENBQUMsTUFBTSxFQUFFO29CQUM1QkYsT0FBTzNCLE1BQU04QixPQUFPLENBQUM7Z0JBQ3ZCO2dCQUNBLENBQUM5QixNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUMsTUFBTSxFQUFFO29CQUM5QkosT0FBTztnQkFDVDtZQUNGLENBQUM7UUFDSDtJQUNGO0FBQUE7TUE1Qk1NO0FBOEJOLGtEQUFrRDtBQUNsRCxNQUFNUywyQkFBMkI7UUFBQyxFQUNoQ3pCLElBQUksRUFDSjBCLFFBQVEsRUFDUkMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLE9BQU8sRUFDUEMsYUFBYSxFQUNiQyxhQUFhLEVBQ2JoRCxLQUFLLEVBQ047V0FDQyw4REFBQ3JDLHlNQUFNQTtRQUNMc0QsTUFBTUE7UUFDTjBCLFVBQVVBO1FBQ1ZNLE1BQU1DO1FBQ05yQyxVQUFVO1FBQ1ZzQyxJQUFJO1lBQ0ZDLFdBQVdQLGNBQWNRLE1BQU0sR0FBRyxJQUFJLFNBQVM7WUFDL0NDLFdBQVdULGNBQWNRLE1BQU0sR0FBRyxJQUFJLFNBQVM7WUFDL0NuQyxRQUFRO1lBQ1JTLE9BQU87WUFDUDRCLFVBQVU7UUFDWjtRQUNBQyxhQUFhO1FBQ2JDLGVBQWU7WUFDYkMsV0FBVztnQkFDVDtvQkFBRUMsTUFBTTtvQkFBVUMsU0FBUzt3QkFBRUMsUUFBUTs0QkFBQzs0QkFBRzt5QkFBRztvQkFBQztnQkFBRTtnQkFDL0M7b0JBQUVGLE1BQU07b0JBQW1CQyxTQUFTO3dCQUFFRSxTQUFTO29CQUFHO2dCQUFFO2FBQ3JEO1FBQ0g7a0JBRUM7Z0JBQUMsRUFBRUMsZUFBZSxFQUFFO21CQUNuQiw4REFBQ3pGLHVNQUFJQTtnQkFBRSxHQUFHeUYsZUFBZTtnQkFBRUMsU0FBUzswQkFDbEMsNEVBQUNwRyx3TUFBS0E7b0JBQ0pxRyxXQUFXO29CQUNYZCxJQUFJO3dCQUNGNUMsY0FBYzt3QkFDZDJELFVBQVU7d0JBQ1YxRCxRQUFRO3dCQUNSMkQsZ0JBQWdCO3dCQUNoQmhDLFlBQ0U7d0JBQ0Z6QixXQUNFO29CQUNKOzhCQUVBLDRFQUFDN0Msb05BQWlCQTt3QkFBQ3VHLGFBQWF4QjtrQ0FDOUIsNEVBQUM3RywwREFBR0E7NEJBQUNvSCxJQUFJO2dDQUFFSSxVQUFVO2dDQUFTbEQsVUFBVTs0QkFBUTs7OENBQzlDLDhEQUFDdEUsMERBQUdBO29DQUNGb0gsSUFBSTt3Q0FDRmtCLEdBQUc7d0NBQ0hDLGNBQWM7d0NBQ2RuQyxZQUFZbkMsTUFBTWtDLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDb0MsTUFBTTt3Q0FDM0MxSSxPQUFPbUUsTUFBTWtDLE9BQU8sQ0FBQ3NDLElBQUksQ0FBQ0MsS0FBSztvQ0FDakM7OENBRUEsNEVBQUNySSxpRUFBVUE7d0NBQ1RzSSxTQUFRO3dDQUNSdkIsSUFBSTs0Q0FDRmhELFlBQVk7NENBQ1pDLFVBQVU7NENBQ1Z1RSxTQUFTOzRDQUNUQyxZQUFZOzRDQUNaQyxLQUFLO3dDQUNQOzswREFFQSw4REFBQzlILDROQUFtQkE7Z0RBQUNvRyxJQUFJO29EQUFFL0MsVUFBVTtnREFBTzs7Ozs7OzRDQUFLOzRDQUVoRHlDLGNBQWNRLE1BQU0sR0FBRyxtQkFDdEIsOERBQUN0SCwwREFBR0E7Z0RBQ0ZvSCxJQUFJO29EQUNGakQsaUJBQWlCO29EQUNqQkssY0FBYztvREFDZHVFLElBQUk7b0RBQ0pDLElBQUk7b0RBQ0ozRSxVQUFVO29EQUNWRCxZQUFZO2dEQUNkOzBEQUVDMEMsY0FBY1EsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzdCLDhEQUFDdEgsMERBQUdBO29DQUFDb0gsSUFBSTt3Q0FBRUMsV0FBVzt3Q0FBU0UsV0FBVztvQ0FBTzs4Q0FDOUNSLHdCQUNDLDhEQUFDL0csMERBQUdBO3dDQUFDb0gsSUFBSTs0Q0FBRXdCLFNBQVM7NENBQVFLLGdCQUFnQjs0Q0FBVVgsR0FBRzt3Q0FBRTtrREFDekQsNEVBQUNsRiwwSEFBZ0JBOzRDQUFDdEQsT0FBTTs7Ozs7Ozs7OztvREFFeEJvSix1QkFDRiw0QkFBNEI7a0RBQzVCLDhEQUFDbEosMERBQUdBO3dDQUFDb0gsSUFBSTs0Q0FBRWtCLEdBQUc7d0NBQUU7OzBEQUNkLDhEQUFDdEksMERBQUdBO2dEQUFDb0gsSUFBSTtvREFBRXdCLFNBQVM7b0RBQVFDLFlBQVk7b0RBQVVNLElBQUk7Z0RBQUU7O2tFQUN0RCw4REFBQzdJLGlFQUFVQTt3REFDVDhJLFNBQVNDO3dEQUNUakMsSUFBSTs0REFBRWtDLElBQUk7NERBQUdoQixHQUFHO3dEQUFJO3dEQUNwQmlCLE1BQUs7a0VBRUwsNEVBQUNySSxzRUFBYUE7NERBQUNrRyxJQUFJO2dFQUFFL0MsVUFBVTs0REFBTzs7Ozs7Ozs7Ozs7a0VBRXhDLDhEQUFDaEUsaUVBQVVBO3dEQUFDc0ksU0FBUTt3REFBS3ZCLElBQUk7NERBQUUvQyxVQUFVOzREQUFRRCxZQUFZO3dEQUFJO2tFQUFHOzs7Ozs7Ozs7Ozs7MERBSXRFLDhEQUFDYixpSEFBT0E7Z0RBQUM2RCxJQUFJO29EQUFFK0IsSUFBSTtnREFBRTs7Ozs7OzBEQUNyQiw4REFBQzlJLGlFQUFVQTtnREFDVHNJLFNBQVE7Z0RBQ1J2QixJQUFJO29EQUNGL0MsVUFBVTtvREFDVm1GLFlBQVk7b0RBQ1oxSixPQUFPbUUsTUFBTWtDLE9BQU8sQ0FBQ3NDLElBQUksQ0FBQ2dCLE9BQU87b0RBQ2pDTixJQUFJO2dEQUNOOzBEQUVDRCxxQkFBcUJRLFlBQVk7Ozs7OzswREFFcEMsOERBQUNySixpRUFBVUE7Z0RBQ1RzSSxTQUFRO2dEQUNSdkIsSUFBSTtvREFDRi9DLFVBQVU7b0RBQ1Z2RSxPQUFPbUUsTUFBTWtDLE9BQU8sQ0FBQ3NDLElBQUksQ0FBQ3BDLFNBQVM7b0RBQ25Dc0QsV0FBVztnREFDYjswREFFQyxJQUFJQyxLQUFLVixxQkFBcUJXLFNBQVMsRUFBRUMsa0JBQWtCLENBQUMsU0FBUztvREFDcEVDLE1BQU07b0RBQ05DLE9BQU87b0RBQ1BDLEtBQUs7b0RBQ0xDLE1BQU07b0RBQ05DLFFBQVE7Z0RBQ1Y7Ozs7Ozs7Ozs7O2tFQUlKOzs0Q0FDR3JELGNBQWNRLE1BQU0sR0FBRyxJQUN0QlIsY0FBY3NELEdBQUcsQ0FBQyxDQUFDQyxrQkFBa0JDLHNCQUNuQyw4REFBQzNJLGtEQUFJQTtvREFFSDRJLE1BQU0seUJBQTZDLE9BQXBCRixpQkFBaUJHLEVBQUU7b0RBQ2xEQyxRQUFROzhEQUVSLDRFQUFDekssMERBQUdBO3dEQUNGb0osU0FBUyxJQUFNcEMsY0FBY3FELGlCQUFpQkcsRUFBRTt3REFDaERwRCxJQUFJOzREQUNGa0IsR0FBRzs0REFDSEMsY0FDRStCLFFBQVF4RCxjQUFjUSxNQUFNLEdBQUcsSUFDM0IsK0JBQ0E7NERBQ04sV0FBVztnRUFDVG5ELGlCQUFpQjtnRUFDakJ1RyxRQUFROzREQUNWOzREQUNBNUYsWUFBWTs0REFDWndCLFVBQVU7d0RBQ1o7OzBFQUVBLDhEQUFDdEcsMERBQUdBO2dFQUNGb0gsSUFBSTtvRUFBRXdCLFNBQVM7b0VBQVFFLEtBQUs7b0VBQUdELFlBQVk7Z0VBQWE7O2tGQUV4RCw4REFBQzdJLDBEQUFHQTt3RUFDRm9ILElBQUk7NEVBQ0Z4QixPQUFPOzRFQUNQckIsUUFBUTs0RUFDUkMsY0FBYzs0RUFDZG9FLFNBQVM7NEVBQ1RDLFlBQVk7NEVBQ1pJLGdCQUFnQjs0RUFDaEIwQixZQUFZOzRFQUNaaEcsV0FDRTt3RUFDSjtrRkFFQSw0RUFBQzNELDROQUFtQkE7NEVBQ2xCb0csSUFBSTtnRkFBRXRILE9BQU87Z0ZBQVN1RSxVQUFVOzRFQUFPOzs7Ozs7Ozs7OztrRkFHM0MsOERBQUNyRSwwREFBR0E7d0VBQUNvSCxJQUFJOzRFQUFFd0QsTUFBTTs0RUFBR3RHLFVBQVU7d0VBQUU7OzBGQUM5Qiw4REFBQ2pFLGlFQUFVQTtnRkFDVHNJLFNBQVE7Z0ZBQ1J2QixJQUFJO29GQUNGL0MsVUFBVTtvRkFDVkQsWUFBWTtvRkFDWm9GLFlBQVk7b0ZBQ1oxSixPQUFPO29GQUNQcUosSUFBSTtvRkFDSjBCLFdBQVc7Z0ZBQ2I7MEZBRUNSLGlCQUFpQlgsWUFBWTs7Ozs7OzBGQUVoQyw4REFBQ3JKLGlFQUFVQTtnRkFDVHNJLFNBQVE7Z0ZBQ1J2QixJQUFJO29GQUNGL0MsVUFBVTtvRkFDVnZFLE9BQU87b0ZBQ1A4SSxTQUFTO29GQUNUQyxZQUFZO29GQUNaQyxLQUFLO2dGQUNQOztrR0FFQSw4REFBQ3RILG1FQUFVQTt3RkFBQzRGLElBQUk7NEZBQUUvQyxVQUFVO3dGQUFNOzs7Ozs7b0ZBQ2pDdEUsbUVBQWlCQSxDQUFDc0ssaUJBQWlCUixTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzREQUlsRFMsVUFBVSxtQkFDVCw4REFBQ3RLLDBEQUFHQTtnRUFDRm9ILElBQUk7b0VBQ0ZkLFVBQVU7b0VBQ1Z3RSxNQUFNO29FQUNOQyxLQUFLO29FQUNMQyxRQUFRO29FQUNScEYsT0FBTztvRUFDUHBCLGNBQWM7Z0VBQ2hCOzs7Ozs7Ozs7Ozs7bURBOUVENkYsaUJBQWlCRyxFQUFFOzs7OytFQXFGNUIsOERBQUN4SywwREFBR0E7Z0RBQ0ZvSCxJQUFJO29EQUNGd0IsU0FBUztvREFDVHFDLGVBQWU7b0RBQ2ZwQyxZQUFZO29EQUNaSSxnQkFBZ0I7b0RBQ2hCWCxHQUFHO29EQUNINEMsV0FBVztnREFDYjs7a0VBRUEsOERBQUNsSyw0TkFBbUJBO3dEQUNsQm9HLElBQUk7NERBQUUvQyxVQUFVOzREQUFRdkUsT0FBTzs0REFBUXFKLElBQUk7d0RBQUU7Ozs7OztrRUFFL0MsOERBQUM5SSxpRUFBVUE7d0RBQUNzSSxTQUFRO3dEQUFLdkIsSUFBSTs0REFBRXRILE9BQU87NERBQVFxSixJQUFJO3dEQUFFO2tFQUFHOzs7Ozs7a0VBR3ZELDhEQUFDOUksaUVBQVVBO3dEQUFDc0ksU0FBUTt3REFBUXZCLElBQUk7NERBQUV0SCxPQUFPO3dEQUFPO2tFQUFHOzs7Ozs7Ozs7Ozs7NENBS3REZ0gsY0FBY1EsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ3RILDBEQUFHQTtnREFDRm9ILElBQUk7b0RBQ0ZrQixHQUFHO29EQUNINkMsV0FBVztvREFDWC9FLFlBQVk7Z0RBQ2Q7MERBRUEsNEVBQUN6RSxrREFBSUE7b0RBQUM0SSxNQUFLO29EQUFpQkUsUUFBUTs4REFDbEMsNEVBQUNwSyxpRUFBVUE7d0RBQ1QrSyxXQUFVO3dEQUNWekMsU0FBUTt3REFDUnZCLElBQUk7NERBQ0Z3QixTQUFTOzREQUNUQyxZQUFZOzREQUNaSSxnQkFBZ0I7NERBQ2hCNUUsVUFBVTs0REFDVkQsWUFBWTs0REFDWmlILGdCQUFnQjs0REFDaEJ2TCxPQUFPOzREQUNQLFdBQVc7Z0VBQ1RBLE9BQU87Z0VBQ1ArRSxXQUFXOzREQUNiOzREQUNBQyxZQUFZO3dEQUNkOzs0REFDRDswRUFFQyw4REFBQzdELHlFQUFnQkE7Z0VBQUNtRyxJQUFJO29FQUFFL0MsVUFBVTtnRUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7UUFXekQ7Ozs7OztBQUVGO01BNVJKc0M7QUErUlMsU0FBUzJFO1FBT05DLGVBcUJEQSxnQkFBcUJBOztJQTNCcEMsTUFBTSxFQUFFckcsSUFBSSxFQUFFc0csT0FBTyxFQUFFLEdBQUdqTSxpREFBVUEsQ0FBQ21ELHlEQUFhQTtJQUNsRCxNQUFNLEVBQUUrSSxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHL0kscURBQU9BO0lBQ2pDLE1BQU0sRUFBRWdKLGNBQWMsRUFBRSxHQUFHbEosdURBQVFBO0lBQ25DLE1BQU13QixRQUFRdEUsK0RBQVFBO0lBQ3RCLE1BQU1pTSxXQUFXM0wsd0VBQWFBLENBQUNnRSxNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUM7SUFDdEQsTUFBTSxFQUFFNkYsTUFBTU4sT0FBTyxFQUFFTyxNQUFNLEVBQUUsR0FBRzNJLDREQUFVQTtJQUM1QyxNQUFNNEksVUFBVVIsQ0FBQUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU1MsSUFBSSxjQUFiVCxvQ0FBQUEsY0FBZXJFLElBQUksTUFBSztJQUN4QyxNQUFNK0Usd0JBQXdCek0sNkNBQU1BLENBQUM7SUFFckMsTUFBTSxDQUFDME0sVUFBVUMsWUFBWSxHQUFHN00sK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDOE0sa0JBQWtCQyxvQkFBb0IsR0FBRy9NLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ3NILFVBQVUwRixZQUFZLEdBQUdoTiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpTixjQUFjQyxnQkFBZ0IsR0FBR2xOLCtDQUFRQSxlQUFDLDhEQUFDb0Isa05BQVNBOzs7OztJQUMzRCxNQUFNLENBQUNvRyxlQUFlMkYsaUJBQWlCLEdBQUduTiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3JELE1BQU0sQ0FBQ29OLGNBQWNDLGdCQUFnQixHQUFHck4sK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDc04saUJBQWlCQyxtQkFBbUIsR0FBR3ZOLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3dOLGtCQUFrQkMsb0JBQW9CLEdBQUd6TiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RSxxQkFBcUI4SSx1QkFBdUIsR0FBRzFOLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQzJOLDJCQUEyQkMsNkJBQTZCLEdBQUc1TiwrQ0FBUUEsQ0FBQztJQUMzRSxNQUFNLENBQUM2TixzQkFBc0JDLHdCQUF3QixHQUFHOU4sK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDK04sZUFBZUMsaUJBQWlCLEdBQUdoTywrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM0Six1QkFBc0JxRSx3QkFBd0IsR0FBR2pPLCtDQUFRQSxDQUFDO0lBRWpFLE1BQU1rTyxhQUFhO0lBQ25CLE1BQU1DLGdCQUFnQjdCLFlBQVksQ0FBQzFHLE9BQU8sY0FBYztJQUN4RCxNQUFNK0IsZ0JBQWdCcEgsa0VBQWdCQSxDQUFDQyxtREFBS0E7SUFFNUMsTUFBTTROLFNBQVNuQyxDQUFBQSxvQkFBQUEsK0JBQUFBLGlCQUFBQSxRQUFTUyxJQUFJLGNBQWJULHFDQUFBQSxlQUFlZixFQUFFLE1BQUllLG9CQUFBQSwrQkFBQUEsaUJBQUFBLFFBQVNTLElBQUksY0FBYlQscUNBQUFBLGVBQWVvQyxPQUFPO0lBRTFELE1BQU0sRUFBRTVHLE9BQU8sRUFBRTZHLEtBQUssRUFBRS9CLElBQUksRUFBRSxHQUFHaEoseURBQVFBLENBQUNFLGdFQUFnQkEsRUFBRTtRQUMxRDhLLFdBQVc7WUFBRUgsUUFBUUEsU0FBU0EsU0FBU3ZHO1FBQVU7UUFDakQyRyxNQUFNLENBQUNKLFVBQVU1QixXQUFXO1FBQzVCaUMsY0FBY1YsZ0JBQWdCLFFBQVE7UUFDdENXLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyw2QkFBNkI7SUFDL0I7SUFFQSxNQUFNLEVBQUVyQyxNQUFNc0MsZUFBZSxFQUFFcEgsU0FBU3FILGtCQUFrQixFQUFFQyxTQUFTQyxrQkFBa0IsRUFBRSxHQUFHekwseURBQVFBLENBQUNHLDJFQUEyQkEsRUFBRTtRQUNoSTZLLFdBQVc7WUFBRUgsUUFBUUEsU0FBU0EsU0FBU3ZHO1FBQVU7UUFDakQyRyxNQUFNLENBQUNKLFVBQVU1QixXQUFXO1FBQzVCaUMsY0FBY1YsZ0JBQWdCLFFBQVE7UUFDdENXLGFBQWE7UUFDYkMsYUFBYTtJQUNmO0lBRUEsTUFBTSxDQUFDTSxvQkFBb0IsR0FBRzNMLDREQUFXQSxDQUFDTSw0RUFBMkJBO0lBQ3JFLE1BQU0sQ0FBQ3NMLGNBQWMsR0FBRzVMLDREQUFXQSxDQUFDSyxrRkFBa0NBO0lBRXRFLE1BQU13TCx3QkFBd0I7UUFDNUIsSUFBSTtZQUNGLE1BQU1DLFFBQVEsSUFBSUMsTUFBTTtZQUN4QkQsTUFBTUUsTUFBTSxHQUFHO1lBQ2ZGLE1BQU1HLElBQUksR0FBR0MsS0FBSyxDQUFDLENBQUNDLElBQU1DLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NGO1FBQzlFLEVBQUUsT0FBT25CLE9BQU87WUFDZG9CLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUNyQjtRQUNuRDtJQUNGO0lBRUEsTUFBTXNCLDBCQUEwQixDQUFDQztRQUMvQixJQUFJLGtCQUFrQkMsVUFBVUMsYUFBYUMsVUFBVSxLQUFLLFdBQVc7WUFDckUsSUFBSUQsYUFBYSxrQkFBa0I7Z0JBQ2pDRSxNQUFNSjtnQkFDTkssTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsS0FBSztnQkFDTEMsb0JBQW9CO2dCQUNwQkMsUUFBUTtZQUNWO1FBQ0Y7SUFDRjtJQUVBblEsZ0RBQVNBLENBQUM7UUFDUixJQUFJLGtCQUFrQjJQLFVBQVVDLGFBQWFDLFVBQVUsS0FBSyxXQUFXO1lBQ3JFRCxhQUFhUSxpQkFBaUI7UUFDaEM7SUFDRixHQUFHLEVBQUU7SUFFTHBRLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXFNLFdBQVcsbUJBQW1CLENBQUM0QixRQUFRO1lBQ3pDc0IsUUFBUWMsSUFBSSxDQUFDLGdEQUFnRHZFLG9CQUFBQSw4QkFBQUEsUUFBU1MsSUFBSTtRQUM1RTtRQUNBZ0QsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJuRDtRQUMvQmtELFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0IxRCxvQkFBQUEsOEJBQUFBLFFBQVNTLElBQUk7UUFDekNnRCxRQUFRQyxHQUFHLENBQUMsWUFBWXZCO0lBQzFCLEdBQUc7UUFBQ25DO1FBQVNPO1FBQVE0QjtLQUFPO0lBRTVCak8sZ0RBQVNBLENBQUM7UUFDUnVQLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsWUFBWWxJO1FBQ3hCaUksUUFBUUMsR0FBRyxDQUFDLFVBQVVyQjtRQUN0Qm9CLFFBQVFDLEdBQUcsQ0FBQyxTQUFTcEQsaUJBQUFBLDJCQUFBQSxLQUFNL0UsYUFBYTtRQUN4Q2tJLFFBQVFDLEdBQUcsQ0FBQyxZQUFZdkI7UUFFeEIsSUFBSSxDQUFDM0csV0FBVyxDQUFDNkcsVUFBUy9CLGlCQUFBQSwyQkFBQUEsS0FBTS9FLGFBQWEsR0FBRTtZQUM3QyxNQUFNaUosbUJBQW1CbEUsS0FBSy9FLGFBQWE7WUFDM0MsTUFBTWtKLGVBQWVELGlCQUFpQnpJLE1BQU07WUFFNUMwSCxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLCtCQUErQmM7WUFDM0NmLFFBQVFDLEdBQUcsQ0FBQyxVQUFVZTtZQUV0QjFDLGlCQUFpQjtZQUVqQixJQUNFMEMsZUFBZS9DLDZCQUNmQSw0QkFBNEIsR0FDNUI7Z0JBQ0FELHVCQUF1QjtnQkFDdkJJLHdCQUF3QnhELEtBQUtxRyxHQUFHO2dCQUVoQ3hCO2dCQUVBLElBQUl1QixlQUFlL0MsMkJBQTJCO29CQUM1QyxNQUFNaUQsdUJBQXVCRixlQUFlL0M7b0JBQzVDLE1BQU1rQyxVQUNKZSx5QkFBeUIsSUFDckIsaUNBQ0EsWUFBaUMsT0FBckJBLHNCQUFxQjtvQkFFdkNoQix3QkFBd0JDO29CQUV4QnRDLG1CQUFtQnNDO29CQUNuQnBDLG9CQUFvQjtvQkFDcEJKLGdCQUFnQjtnQkFDbEI7Z0JBRUF3RCxXQUFXO29CQUNUbkQsdUJBQXVCO2dCQUN6QixHQUFHO1lBQ0w7WUFFQVAsaUJBQWlCc0Q7WUFDakI3Qyw2QkFBNkI4QztZQUM3QmhCLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBK0MsT0FBYmU7UUFDaEQsT0FBTyxJQUFJcEMsT0FBTztZQUNoQm9CLFFBQVFwQixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxJQUFJQSxNQUFNd0MsYUFBYSxFQUFFO2dCQUN2QnBCLFFBQVFwQixLQUFLLENBQUMsbUJBQW1CQSxNQUFNd0MsYUFBYSxDQUFDaEcsR0FBRyxDQUFDLENBQUMyRSxJQUFNQSxFQUFFSSxPQUFPO1lBQzNFO1lBQ0EsSUFBSXZCLE1BQU15QyxZQUFZLEVBQUU7Z0JBQ3RCckIsUUFBUXBCLEtBQUssQ0FBQyxrQkFBa0JBLE1BQU15QyxZQUFZO1lBQ3BEO1lBRUEvQyxpQkFBaUI7WUFFakJULG1CQUFtQjtZQUNuQkUsb0JBQW9CO1lBQ3BCSixnQkFBZ0I7WUFFaEJ3RCxXQUFXO2dCQUNUN0MsaUJBQWlCO1lBQ25CLEdBQUc7UUFDTCxPQUFPLElBQUksQ0FBQ0ksUUFBUTtZQUNsQnNCLFFBQVFjLElBQUksQ0FBQztRQUNmLE9BQU8sSUFBSSxDQUFDL0ksV0FBVyxDQUFDOEUsTUFBTTtZQUM1Qm1ELFFBQVFjLElBQUksQ0FBQztRQUNmO0lBQ0YsR0FBRztRQUFDL0k7UUFBUzZHO1FBQU8vQjtRQUFNb0I7UUFBMkJTO0tBQU87SUFFNUQsSUFBSTVCLFdBQVcsV0FBVztRQUN4QixxQkFDRSw4REFBQzlMLDBEQUFHQTtZQUFDb0gsSUFBSTtnQkFBRXdCLFNBQVM7Z0JBQVFLLGdCQUFnQjtnQkFBVVgsR0FBRztZQUFFO3NCQUN6RCw0RUFBQ2xGLDBIQUFnQkE7Z0JBQUN0RCxPQUFNOzs7Ozs7Ozs7OztJQUc5QjtJQUVBLE1BQU13USxlQUFlLElBQU05RSxRQUFRLENBQUN0RztJQUNwQyxNQUFNcUwseUJBQXlCLElBQU1wRSxZQUFZLENBQUNEO0lBQ2xELE1BQU1zRSxjQUFjLENBQUNDLFFBQVVuRSxZQUFZbUUsTUFBTUMsYUFBYTtJQUM5RCxNQUFNQyxjQUFjLElBQU1yRSxZQUFZO0lBQ3RDLE1BQU1zRSwyQkFBMkI7UUFDL0IsTUFBTUMsVUFBVXpFO1FBQ2hCQyxvQkFBb0IsQ0FBQ3lFLE9BQVMsQ0FBQ0E7UUFFL0IsSUFBSSxDQUFDRCxXQUFXbkQsUUFBUTtZQUN0QixJQUFJO2dCQUNGLE1BQU1jLGNBQWM7b0JBQ2xCWCxXQUFXO3dCQUFFSDtvQkFBTztnQkFDdEI7Z0JBRUFZO2dCQUVBVSxRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9yQixPQUFPO2dCQUNkb0IsUUFBUXBCLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3hEO1FBQ0Y7SUFDRjtJQUVBLE1BQU1tRCwwQkFBMEIsQ0FBQ047WUFDM0J4RTtRQUFKLEtBQUlBLGlDQUFBQSxzQkFBc0IrRSxPQUFPLGNBQTdCL0UscURBQUFBLCtCQUErQmdGLFFBQVEsQ0FBQ1IsTUFBTVMsTUFBTSxHQUFHO1FBQzNEN0Usb0JBQW9CO1FBQ3BCa0Isd0JBQXdCLE9BQU8sMkNBQTJDO0lBQzVFO0lBRUEsTUFBTTRELDBCQUEwQixDQUFDekg7UUFDL0I2RCx3QkFBd0I3RDtJQUMxQjtJQUVBLE1BQU0wSCxtQkFBbUI7UUFDdkI3RCx3QkFBd0I7SUFDMUI7SUFFQSxNQUFNOEQsb0JBQW9CLENBQUNwTixPQUFPdUw7UUFDaEM5RCxRQUFRekg7UUFDUnVJLGdCQUFnQmdEO1FBQ2hCbUI7SUFDRjtJQUVBLE1BQU1XLG9CQUFvQixDQUFDeFIsT0FBTzBQO1FBQ2hDN0QsZUFBZTdMO1FBQ2YwTSxnQkFBZ0JnRDtRQUNoQm1CO0lBQ0Y7SUFFQSxNQUFNWSw0QkFBNEIsT0FBT0M7UUFDdkMsSUFBSSxDQUFDOUQsUUFBUTtZQUNYYixtQkFBbUI7WUFDbkJFLG9CQUFvQjtZQUNwQkosZ0JBQWdCO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTThFLFdBQVcsTUFBTWxELG9CQUFvQjtnQkFDekNWLFdBQVc7b0JBQ1QyRCxnQkFBZ0JBO29CQUNoQjlELFFBQVFBO2dCQUNWO1lBQ0Y7WUFFQSxJQUFJK0QsU0FBUzVGLElBQUksQ0FBQzBDLG1CQUFtQixFQUFFO2dCQUNyQyxNQUFNbUQsdUJBQXVCNUssY0FBYzZLLE1BQU0sQ0FDL0MsQ0FBQ0MsSUFBTUEsRUFBRXBILEVBQUUsS0FBS2dIO2dCQUVsQi9FLGlCQUFpQmlGO2dCQUNqQnhFLDZCQUE2QndFLHFCQUFxQnBLLE1BQU07Z0JBRXhEdUYsbUJBQW1CO2dCQUNuQkUsb0JBQW9CO2dCQUNwQkosZ0JBQWdCO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPaUIsT0FBTztZQUNkb0IsUUFBUXBCLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDZixtQkFBbUI7WUFDbkJFLG9CQUFvQjtZQUNwQkosZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFDRTs7WUFDSWxCLENBQUFBLFNBQVMsV0FBV0EsU0FBUyxNQUFLLG1CQUNsQyw4REFBQzFHO2dCQUFPdUIsVUFBUztnQkFBUXBCLE1BQU1BOzBCQUM3Qiw0RUFBQy9FLDhEQUFPQTtvQkFDTmlILElBQUk7d0JBQ0ZkLFVBQVU7d0JBQ1ZWLE9BQU87d0JBQ1B6QixpQkFBaUJGLE1BQU1rQyxPQUFPLENBQUNDLFVBQVUsQ0FBQ29DLE1BQU07d0JBQ2hEckQsUUFBUTt3QkFDUmdHLFdBQVcsYUFBdUMsT0FBMUJsSCxNQUFNa0MsT0FBTyxDQUFDc0MsSUFBSSxDQUFDb0osTUFBTTt3QkFDakR0SixjQUFjLGFBQXVDLE9BQTFCdEUsTUFBTWtDLE9BQU8sQ0FBQ3NDLElBQUksQ0FBQ29KLE1BQU07d0JBQ3BELENBQUM1TixNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUMsTUFBTSxFQUFFOzRCQUM5QmlGLGVBQWU7d0JBQ2pCO29CQUNGOztzQ0FFQSw4REFBQzNLLGlFQUFVQTs0QkFDVHdSLE1BQUs7NEJBQ0xDLGNBQVc7NEJBQ1gzSSxTQUFTa0g7NEJBQ1RsSixJQUFJO2dDQUFFNEssYUFBYTs0QkFBTztzQ0FFekI5TSxxQkFDQyw4REFBQ3pELG1EQUFLQTtnQ0FDSndRLEtBQUk7Z0NBQ0pDLEtBQUk7Z0NBQ0p0TSxPQUFPO2dDQUNQckIsUUFBUTs7Ozs7cURBR1YsOERBQUNwRCxpRUFBUUE7Z0NBQUNpRyxJQUFJO29DQUFFdEgsT0FBT21FLE1BQU1rQyxPQUFPLENBQUNzQyxJQUFJLENBQUNDLEtBQUs7Z0NBQUM7Ozs7Ozs7Ozs7O3NDQUdwRCw4REFBQ3JJLGlFQUFVQTs0QkFDVCtLLFdBQVU7NEJBQ1Z6QyxTQUFROzRCQUNSN0ksT0FBTTs0QkFDTnFTLE1BQU07NEJBQ04vSyxJQUFJO2dDQUFFZ0wsVUFBVTs0QkFBRTtzQ0FFbEIsNEVBQUN6USxrREFBSUE7Z0NBQUM0SSxNQUFLOzBDQUNULDRFQUFDOUksbURBQUtBO29DQUNKd1EsS0FBS3pFO29DQUNMMEUsS0FBSTtvQ0FDSm5MLFNBQVE7b0NBQ1JuQixPQUFPO29DQUNQckIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJZCw4REFBQ3ZFLDBEQUFHQTs0QkFDRm9ILElBQUk7Z0NBQ0Z3QixTQUFTO2dDQUNUQyxZQUFZO2dDQUNaQyxLQUFLO29DQUFFdUosSUFBSTtvQ0FBS0MsSUFBSTtvQ0FBR0MsSUFBSTtnQ0FBSTtnQ0FDL0I1SCxZQUFZO2dDQUNaLENBQUMxRyxNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUMsTUFBTSxFQUFFO29DQUM5QmlGLGVBQWU7b0NBQ2ZuQyxLQUFLO2dDQUNQOzRCQUNGOzs4Q0FFQSw4REFBQzlJLDBEQUFHQTtvQ0FDRm9ILElBQUk7d0NBQ0ZkLFVBQVU7d0NBQ1YsWUFBWTs0Q0FDVmtNLFNBQVM7NENBQ1RsTSxVQUFVOzRDQUNWbU0sT0FBTzs0Q0FDUDFILEtBQUs7NENBQ0xsRyxXQUFXOzRDQUNYZSxPQUFPOzRDQUNQckIsUUFBUTs0Q0FDUkosaUJBQWlCOzRDQUNqQixDQUFDRixNQUFNNEIsV0FBVyxDQUFDRyxJQUFJLENBQUMsTUFBTSxFQUFFO2dEQUM5QjRDLFNBQVM7NENBQ1g7d0NBQ0Y7b0NBQ0Y7OENBRUEsNEVBQUN0SSxpRUFBVUE7d0NBQ1R5UixjQUFXO3dDQUNYVyxpQkFBYzt3Q0FDZEMsaUJBQWM7d0NBQ2R2SixTQUFTb0g7d0NBQ1RwSixJQUFJOzRDQUNGdEgsT0FBTzs0Q0FDUGlJLFNBQVM7Z0RBQUVzSyxJQUFJO2dEQUFPQyxJQUFJOzRDQUFNOzRDQUNoQyxXQUFXO2dEQUNUbk8saUJBQWlCO2dEQUNqQlUsV0FBVzs0Q0FDYjs0Q0FDQUMsWUFBWTt3Q0FDZDtrREFFQ3lIOzs7Ozs7Ozs7Ozs4Q0FHTCw4REFBQzNLLHlNQUFNQTtvQ0FDTDRJLElBQUc7b0NBQ0h0RixNQUFNME4sUUFBUWhNO29DQUNkQSxVQUFVQTtvQ0FDVmlNLFdBQVU7b0NBQ1YvTixVQUFVO29DQUNWc0MsSUFBSTt3Q0FBRWpDLFFBQVE7b0NBQU07OENBRW5COzRDQUFDLEVBQUU2QyxlQUFlLEVBQUU7K0NBQ25CLDhEQUFDMUYsd01BQUtBOzRDQUFFLEdBQUcwRixlQUFlOzRDQUFFOEssV0FBVTs0Q0FBTzdLLFNBQVM7c0RBQ3BELDRFQUFDcEcsd01BQUtBO2dEQUNKdUYsSUFBSTtvREFDRmhCLFlBQVluQyxNQUFNa0MsT0FBTyxDQUFDQyxVQUFVLENBQUNDLFNBQVM7b0RBQzlDN0IsY0FBYztnREFDaEI7MERBRUEsNEVBQUMxQyxvTkFBaUJBO29EQUFDdUcsYUFBYXNJOzhEQUM5Qiw0RUFBQzNPLDJNQUFRQTt3REFBQytRLGVBQWVILFFBQVFoTTs7MEVBQy9CLDhEQUFDN0UsMk1BQVFBO2dFQUNQcUgsU0FBUyxJQUFNaUksa0JBQWtCLHVCQUFTLDhEQUFDM1Esa05BQVNBOzBFQUVwRCw0RUFBQ0Esa05BQVNBOzs7Ozs7Ozs7OzBFQUVaLDhEQUFDcUIsMk1BQVFBO2dFQUNQcUgsU0FBUyxJQUFNaUksa0JBQWtCLHNCQUFRLDhEQUFDNVEsaU5BQVFBOzBFQUVsRCw0RUFBQ0EsaU5BQVFBOzs7Ozs7Ozs7OzBFQUVYLDhEQUFDc0IsMk1BQVFBO2dFQUNQcUgsU0FBUyxJQUNQa0ksa0JBQWtCLHNCQUFRLDhEQUFDOVAsbUVBQVVBO3dFQUFDNEYsSUFBSTs0RUFBRTRMLE1BQU0vTyxNQUFNa0MsT0FBTyxDQUFDOE0sSUFBSSxDQUFDQyxJQUFJO3dFQUFDOzswRUFHNUUsNEVBQUMxUixtRUFBVUE7b0VBQUM0RixJQUFJO3dFQUFFNEwsTUFBTS9PLE1BQU1rQyxPQUFPLENBQUM4TSxJQUFJLENBQUNDLElBQUk7b0VBQUM7Ozs7Ozs7Ozs7OzBFQUVsRCw4REFBQ25SLDJNQUFRQTtnRUFDUHFILFNBQVMsSUFDUGtJLGtCQUFrQix1QkFBUyw4REFBQzlQLG1FQUFVQTt3RUFBQzRGLElBQUk7NEVBQUU0TCxNQUFNL08sTUFBTWtDLE9BQU8sQ0FBQ2dOLEtBQUssQ0FBQ0QsSUFBSTt3RUFBQzs7MEVBRzlFLDRFQUFDMVIsbUVBQVVBO29FQUFDNEYsSUFBSTt3RUFBRTRMLE1BQU0vTyxNQUFNa0MsT0FBTyxDQUFDZ04sS0FBSyxDQUFDRCxJQUFJO29FQUFDOzs7Ozs7Ozs7OzswRUFFbkQsOERBQUNuUiwyTUFBUUE7Z0VBQ1BxSCxTQUFTLElBQ1BrSSxrQkFBa0Isd0JBQVUsOERBQUM5UCxtRUFBVUE7d0VBQUM0RixJQUFJOzRFQUFFNEwsTUFBTS9PLE1BQU1rQyxPQUFPLENBQUMwTCxNQUFNLENBQUNxQixJQUFJO3dFQUFDOzswRUFHaEYsNEVBQUMxUixtRUFBVUE7b0VBQUM0RixJQUFJO3dFQUFFNEwsTUFBTS9PLE1BQU1rQyxPQUFPLENBQUMwTCxNQUFNLENBQUNxQixJQUFJO29FQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FLckQ7Ozs7Ozs4Q0FHWCw4REFBQ2xULDBEQUFHQTtvQ0FBQ29ILElBQUk7d0NBQUVkLFVBQVU7b0NBQVc7OENBQzlCLDRFQUFDMUI7d0NBQXlCVixxQkFBcUJBO2tEQUM3Qyw0RUFBQzVELGlFQUFVQTs0Q0FDVFIsT0FBTTs0Q0FDTnNULEtBQUtuSDs0Q0FDTDdDLFNBQVN3SDs0Q0FDVG1CLGNBQVksUUFBNkIsT0FBckJqTCxjQUFjUSxNQUFNLEVBQUM7NENBQ3pDRixJQUFJO2dEQUNGZCxVQUFVO2dEQUNWeEcsT0FBTztnREFDUGlJLFNBQVM7b0RBQUVzSyxJQUFJO29EQUFPQyxJQUFJO2dEQUFPO2dEQUNqQyxXQUFXO29EQUNUbk8saUJBQWlCO29EQUNqQlUsV0FBVztnREFDYjtnREFDQUMsWUFBWTs0Q0FDZDtzREFFQSw0RUFBQ2Q7Z0RBQ0NxUCxjQUFjbEYsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJtRix3QkFBd0IsS0FBSTtnREFDM0RwUCxxQkFBcUJBO2dEQUNyQnFQLEtBQUs7MERBRUp6TSxjQUFjUSxNQUFNLEdBQUcsa0JBQ3RCLDhEQUFDdEcsNE5BQW1CQTtvREFDbEJvRyxJQUFJO3dEQUNGdEgsT0FBT29FLHNCQUFzQixZQUFZO3dEQUN6Q0csVUFBVTs0REFBRWdPLElBQUk7NERBQVFDLElBQUk7d0RBQU87d0RBQ25DWCxRQUFRek4sc0JBQ0osZ0RBQ0E7b0RBQ047Ozs7O3lFQUdGLDhEQUFDMUQsc05BQWFBO29EQUFDNEcsSUFBSTt3REFBRS9DLFVBQVU7NERBQUVnTyxJQUFJOzREQUFRQyxJQUFJO3dEQUFPO29EQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNcEUsOERBQUMzTDtvQ0FDQ3pCLE1BQU1rSDtvQ0FDTnhGLFVBQVVxRixzQkFBc0IrRSxPQUFPO29DQUN2Q25LLFNBQVNrSztvQ0FDVGpLLGVBQWVBO29DQUNmQyxTQUFTQTtvQ0FDVEMsZUFBZXVLO29DQUNmdEssZUFBZUE7b0NBQ2ZoRCxPQUFPQTtvQ0FDUGlGLHNCQUFzQkE7b0NBQ3RCc0sscUJBQXFCckM7b0NBQ3JCOUgsY0FBYytIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNeEIsOERBQUNsTDtnQkFDQ3lDLFNBQVM4RTtnQkFDVHZJLE1BQU1BO2dCQUNOa0MsSUFBSTtvQkFDRmpDLFFBQVE7b0JBQ1JzTyxhQUFhaEksU0FBUyxVQUFVLG9CQUFvQjtnQkFDdEQ7MEJBRUEsNEVBQUN6TCwwREFBR0E7b0JBQ0ZvSCxJQUFJO3dCQUNGakQsaUJBQWlCRixNQUFNa0MsT0FBTyxDQUFDQyxVQUFVLENBQUNxRCxPQUFPO3dCQUNqRGlLLFFBQVE7d0JBQ1JsUCxjQUFjO3dCQUNkRCxRQUFRO29CQUNWOztzQ0FFQSw4REFBQ3BFLDhEQUFPQTs0QkFDTmlILElBQUk7Z0NBQ0Z3QixTQUFTO2dDQUNUQyxZQUFZO2dDQUNaOEssV0FBVztnQ0FDWDFLLGdCQUFnQjtnQ0FDaEJGLElBQUk7b0NBQUM7aUNBQUU7NEJBQ1Q7c0NBRUEsNEVBQUNyRixpREFBT0E7Ozs7Ozs7Ozs7c0NBRVYsOERBQUN0RCwyREFBSUE7NEJBQ0hnTCxXQUFVOzRCQUNWaEUsSUFBSTtnQ0FDRndCLFNBQVM7Z0NBQ1RxQyxlQUFlO2dDQUNmaEMsZ0JBQWdCO2dDQUNoQjFFLFFBQVE7Z0NBQ1JvUCxXQUFXOzRCQUNiOzs4Q0FFQSw4REFBQzNULDBEQUFHQTs7c0RBQ0YsOERBQUN3RCxzREFBYUE7Ozs7O3dDQUNidUkseUJBQ0M7OzhEQUNFLDhEQUFDMUosaU5BQWNBO29EQUFDK0csU0FBU21IOztzRUFDdkIsOERBQUNuTywrTUFBWUE7c0VBQ1gsNEVBQUNkLHFFQUFZQTtnRUFDWDhGLElBQUk7b0VBQUV0SCxPQUFPMkwsU0FBUyxVQUFVLFVBQVU7Z0VBQVU7Ozs7Ozs7Ozs7O3NFQUd4RCw4REFBQ3ZKLCtNQUFZQTs0REFBQ3VILFNBQVE7Ozs7Ozt3REFDckJ5Qyx5QkFBVyw4REFBQzlLLHVFQUFVQTs7OztpRkFBTSw4REFBQ0MsdUVBQVVBOzs7Ozs7Ozs7Ozs4REFFMUMsOERBQUNjLDJNQUFRQTtvREFBQ3lSLElBQUkxSDtvREFBVWpFLFNBQVE7b0RBQU80TCxhQUFhOzhEQUNsRCw0RUFBQ3pULDJEQUFJQTt3REFBQ2dMLFdBQVU7d0RBQU0wSSxjQUFjOzswRUFDbEMsOERBQUNuUyxrREFBSUE7Z0VBQUM0SSxNQUFLO2dFQUFjRSxRQUFROzBFQUMvQiw0RUFBQ3BJLGlOQUFjQTtvRUFBQytFLElBQUk7d0VBQUU3QyxRQUFRO29FQUFHO29FQUFHZ0YsTUFBSzs7c0ZBQ3ZDLDhEQUFDbkgsK01BQVlBO3NGQUNYLDRFQUFDYixzRUFBYUE7Z0ZBQ1o2RixJQUFJO29GQUFFdEgsT0FBTzJMLFNBQVMsVUFBVSxVQUFVO2dGQUFVOzs7Ozs7Ozs7OztzRkFHeEQsOERBQUN2SiwrTUFBWUE7NEVBQUN1SCxTQUFROzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHMUIsOERBQUM5SCxrREFBSUE7Z0VBQUM0SSxNQUFLO2dFQUF1QkUsUUFBUTswRUFDeEMsNEVBQUNwSSxpTkFBY0E7b0VBQUMrRSxJQUFJO3dFQUFFN0MsUUFBUTtvRUFBRztvRUFBR2dGLE1BQUs7O3NGQUN2Qyw4REFBQ25ILCtNQUFZQTtzRkFDWCw0RUFBQ3pCLGlOQUFRQTtnRkFDUHlHLElBQUk7b0ZBQUV0SCxPQUFPMkwsU0FBUyxVQUFVLFVBQVU7Z0ZBQVU7Ozs7Ozs7Ozs7O3NGQUd4RCw4REFBQ3ZKLCtNQUFZQTs0RUFBQ3VILFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUcxQiw4REFBQzlILGtEQUFJQTtnRUFBQzRJLE1BQUs7Z0VBQWdCRSxRQUFROzBFQUNqQyw0RUFBQ3BJLGlOQUFjQTtvRUFBQytFLElBQUk7d0VBQUU3QyxRQUFRO29FQUFHO29FQUFHZ0YsTUFBSzs7c0ZBQ3ZDLDhEQUFDbkgsK01BQVlBO3NGQUNYLDRFQUFDeEIsb05BQVdBO2dGQUNWd0csSUFBSTtvRkFBRXRILE9BQU8yTCxTQUFTLFVBQVUsVUFBVTtnRkFBVTs7Ozs7Ozs7Ozs7c0ZBR3hELDhEQUFDdkosK01BQVlBOzRFQUFDdUgsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzFCLDhEQUFDOUgsa0RBQUlBO2dFQUFDNEksTUFBSztnRUFBZ0JFLFFBQVE7MEVBQ2pDLDRFQUFDcEksaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUdnRixNQUFLOztzRkFDdkMsOERBQUNuSCwrTUFBWUE7c0ZBQ1gsNEVBQUN2QixvTkFBV0E7Z0ZBQ1Z1RyxJQUFJO29GQUFFdEgsT0FBTzJMLFNBQVMsVUFBVSxVQUFVO2dGQUFVOzs7Ozs7Ozs7OztzRkFHeEQsOERBQUN2SiwrTUFBWUE7NEVBQUN1SCxTQUFROzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHMUIsOERBQUM5SCxrREFBSUE7Z0VBQUM0SSxNQUFLO2dFQUFjRSxRQUFROzBFQUMvQiw0RUFBQ3BJLGlOQUFjQTtvRUFBQytFLElBQUk7d0VBQUU3QyxRQUFRO29FQUFHO29FQUFHZ0YsTUFBSzs7c0ZBQ3ZDLDhEQUFDbkgsK01BQVlBO3NGQUNYLDRFQUFDckIsbU5BQVVBO2dGQUNUcUcsSUFBSTtvRkFBRXRILE9BQU8yTCxTQUFTLFVBQVUsVUFBVTtnRkFBVTs7Ozs7Ozs7Ozs7c0ZBR3hELDhEQUFDdkosK01BQVlBOzRFQUFDdUgsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBRzFCLDhEQUFDOUgsa0RBQUlBO2dFQUFDNEksTUFBSztnRUFBdUJFLFFBQVE7MEVBQ3hDLDRFQUFDcEksaU5BQWNBO29FQUFDK0UsSUFBSTt3RUFBRTdDLFFBQVE7b0VBQUc7b0VBQUdnRixNQUFLOztzRkFDdkMsOERBQUNuSCwrTUFBWUE7c0ZBQ1gsNEVBQUN0QixtT0FBMEJBO2dGQUN6QnNHLElBQUk7b0ZBQUV0SCxPQUFPMkwsU0FBUyxVQUFVLFVBQVU7Z0ZBQVU7Ozs7Ozs7Ozs7O3NGQUd4RCw4REFBQ3ZKLCtNQUFZQTs0RUFBQ3VILFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUcxQiw4REFBQzlILGtEQUFJQTtnRUFBQzRJLE1BQUs7Z0VBQWdCRSxRQUFROzBFQUNqQyw0RUFBQ3BJLGlOQUFjQTtvRUFBQytFLElBQUk7d0VBQUU3QyxRQUFRO29FQUFHO29FQUFHZ0YsTUFBSzs7c0ZBQ3ZDLDhEQUFDbkgsK01BQVlBO3NGQUNYLDRFQUFDN0IsNk1BQUlBO2dGQUNINkcsSUFBSTtvRkFBRXRILE9BQU8yTCxTQUFTLFVBQVUsVUFBVTtnRkFBVTs7Ozs7Ozs7Ozs7c0ZBR3hELDhEQUFDdkosK01BQVlBOzRFQUFDdUgsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVFwQyw4REFBQ3pKLDBEQUFHQTs4Q0FDRiw0RUFBQ3lELDJEQUFrQkE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLM0IsOERBQUNKLGtIQUFRQTtnQkFDUDZCLE1BQU13SDtnQkFDTnFILGtCQUFrQjtnQkFDbEJsTixTQUFTLElBQU04RixnQkFBZ0I7MEJBRS9CLDRFQUFDckosK0dBQUtBO29CQUNKMFEsVUFBVWxIO29CQUNWakcsU0FBUyxJQUFNOEYsZ0JBQWdCO29CQUMvQnZGLElBQUk7d0JBQUV4QixPQUFPO29CQUFPOzhCQUVuQmdIOzs7Ozs7Ozs7Ozs7O0FBS1g7R0FybUJ3QnRCOztRQUVJM0ksaURBQU9BO1FBQ05GLG1EQUFRQTtRQUNyQjlDLDJEQUFRQTtRQUNMTSxvRUFBYUE7UUFDSWtELHdEQUFVQTtRQW9CdEJ0RCw4REFBZ0JBO1FBSUxnRCxxREFBUUE7UUFTbURBLHFEQUFRQTtRQVF0RUQsd0RBQVdBO1FBQ2pCQSx3REFBV0E7OztNQWhEYjBJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0hlYWRlci9IZWFkZXIuanM/OTUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcclxuaW1wb3J0IHsgZ3FsIH0gZnJvbSBcIkBhcG9sbG8vY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlQ29udGV4dCwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgc3R5bGVkLCB1c2VUaGVtZSB9IGZyb20gXCJAbXVpL21hdGVyaWFsL3N0eWxlc1wiO1xyXG5pbXBvcnQgTXVpRHJhd2VyIGZyb20gXCJAbXVpL21hdGVyaWFsL0RyYXdlclwiO1xyXG5pbXBvcnQge1xyXG4gIHVzZVNlbGVjdGVkQ29sb3IsXHJcbiAgY29sb3IsXHJcbiAgZm9ybWF0RGF0ZVRpbWVVVEMsXHJcbn0gZnJvbSBcIi4uL0hlbHBlckZ1bmN0aW9uc1wiO1xyXG5pbXBvcnQgQm94IGZyb20gXCJAbXVpL21hdGVyaWFsL0JveFwiO1xyXG5pbXBvcnQgdXNlTWVkaWFRdWVyeSBmcm9tIFwiQG11aS9tYXRlcmlhbC91c2VNZWRpYVF1ZXJ5XCI7XHJcbmltcG9ydCBNdWlBcHBCYXIgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvQXBwQmFyXCI7XHJcbmltcG9ydCBUb29sYmFyIGZyb20gXCJAbXVpL21hdGVyaWFsL1Rvb2xiYXJcIjtcclxuaW1wb3J0IExpc3QgZnJvbSBcIkBtdWkvbWF0ZXJpYWwvTGlzdFwiO1xyXG5pbXBvcnQgVHlwb2dyYXBoeSBmcm9tIFwiQG11aS9tYXRlcmlhbC9UeXBvZ3JhcGh5XCI7XHJcbmltcG9ydCBJY29uQnV0dG9uIGZyb20gXCJAbXVpL21hdGVyaWFsL0ljb25CdXR0b25cIjtcclxuaW1wb3J0IHtcclxuICBUYXNrLFxyXG4gIE5vdGlmaWNhdGlvbnMsXHJcbiAgRGFya01vZGUsXHJcbiAgTGlnaHRNb2RlLFxyXG4gIENhbXBhaWduLFxyXG4gIENlbGVicmF0aW9uLFxyXG4gIEZvcm1hdFF1b3RlLFxyXG4gIE5vdGlmaWNhdGlvbnNBY3RpdmVSb3VuZGVkLFxyXG4gIExvY2FsT2ZmZXIsXHJcbiAgTm90aWZpY2F0aW9uc0FjdGl2ZSxcclxufSBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbFwiO1xyXG5pbXBvcnQgQXJyb3dGb3J3YXJkSWNvbiBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9BcnJvd0ZvcndhcmRcIjtcclxuaW1wb3J0IEFycm93QmFja0ljb24gZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvQXJyb3dCYWNrXCI7XHJcbmltcG9ydCBNZW51SWNvbiBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9NZW51XCI7XHJcbmltcG9ydCBFeHBhbmRMZXNzIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL0V4cGFuZExlc3NcIjtcclxuaW1wb3J0IEV4cGFuZE1vcmUgZnJvbSBcIkBtdWkvaWNvbnMtbWF0ZXJpYWwvRXhwYW5kTW9yZVwiO1xyXG5pbXBvcnQgU2V0dGluZ3NJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL1NldHRpbmdzXCI7XHJcbmltcG9ydCBOZXdzcGFwZXJJY29uIGZyb20gXCJAbXVpL2ljb25zLW1hdGVyaWFsL05ld3NwYXBlclwiO1xyXG5pbXBvcnQgQ2lyY2xlSWNvbiBmcm9tIFwiQG11aS9pY29ucy1tYXRlcmlhbC9DaXJjbGVcIjtcclxuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XHJcbmltcG9ydCBCYWRnZSBmcm9tIFwiQG11aS9tYXRlcmlhbC9CYWRnZVwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCB7XHJcbiAgUG9wcGVyLFxyXG4gIFBhcGVyLFxyXG4gIENsaWNrQXdheUxpc3RlbmVyLFxyXG4gIE1lbnVJdGVtLFxyXG4gIE1lbnVMaXN0LFxyXG4gIExpc3RJdGVtLFxyXG4gIExpc3RJdGVtVGV4dCxcclxuICBDb2xsYXBzZSxcclxuICBMaXN0SXRlbUljb24sXHJcbiAgTGlzdEl0ZW1CdXR0b24sXHJcbiAgU2xpZGUsXHJcbiAgWm9vbSxcclxuICBrZXlmcmFtZXMsXHJcbn0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgdXNlQ29sb3IgfSBmcm9tIFwiLi4vQ29sb3JDb250ZXh0XCI7XHJcbmltcG9ydCB7IERyYXdlckNvbnRleHQgfSBmcm9tIFwiLi9EcmF3ZXJDb250ZXh0XCI7XHJcbmltcG9ydCB7IHVzZU1vZGUgfSBmcm9tIFwiLi4vTW9kZUNvbnRleHRcIjtcclxuaW1wb3J0IHsgdXNlTXV0YXRpb24sIHVzZVF1ZXJ5IH0gZnJvbSBcIkBhcG9sbG8vY2xpZW50XCI7XHJcbmltcG9ydCB7XHJcbiAgZ2V0TmV3Tm90aWZpY2F0aW9ucyxcclxuICBnZXROb3RpZmljYXRpb25zLFxyXG4gIGdldFVuc2Vlbk5vdGlmaWNhdGlvbnNDb3VudCxcclxuICBtdXRhdGlvbk1hcmtBbGxOb3RpZmljYXRpb25zQXNTZWVuXHJcbn0gZnJvbSBcIkAvRGF0YS9Ob3RpZmljYXRpb25cIjtcclxuaW1wb3J0IHsgbXV0YXRpb25BZGROb3RpZmljYXRpb25WaWV3IH0gZnJvbSBcIkAvRGF0YS9Bbm5vdW5jZW1lbnRcIjtcclxuaW1wb3J0IHsgdXNlU2Vzc2lvbiB9IGZyb20gXCJuZXh0LWF1dGgvcmVhY3RcIjtcclxuaW1wb3J0IHsgQ2lyY3VsYXJQcm9ncmVzcywgU25hY2tiYXIsIEFsZXJ0LCBEaXZpZGVyIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgTWFpbkxpc3RJdGVtcywgU2Vjb25kYXJ5TGlzdEl0ZW1zIH0gZnJvbSBcIi4uL0xpc3RJdGVtc1wiO1xyXG5pbXBvcnQgUHJvZmlsZSBmcm9tIFwiLi4vUHJvZmlsZVwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9yb3V0ZXJcIjtcclxuXHJcbmNvbnN0IGRyYXdlcldpZHRoID0gXCIxN3JlbVwiO1xyXG5cclxuLy8gS2V5ZnJhbWVzIGZvciBub3RpZmljYXRpb24gYW5pbWF0aW9uc1xyXG5jb25zdCBwdWxzZSA9IGtleWZyYW1lc2BcclxuICAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMSk7IH1cclxuICA1MCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7IH1cclxuICAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgfVxyXG5gO1xyXG5cclxuY29uc3Qgc2hha2UgPSBrZXlmcmFtZXNgXHJcbiAgMCUsIDEwMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7IH1cclxuICAxMCUsIDMwJSwgNTAlLCA3MCUsIDkwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMnB4KTsgfVxyXG4gIDIwJSwgNDAlLCA2MCUsIDgwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgycHgpOyB9XHJcbmA7XHJcblxyXG5jb25zdCBnbG93ID0ga2V5ZnJhbWVzYFxyXG4gIDAlIHsgYm94LXNoYWRvdzogMCAwIDVweCAjZmY0NDQ0OyB9XHJcbiAgNTAlIHsgYm94LXNoYWRvdzogMCAwIDIwcHggI2ZmNDQ0NCwgMCAwIDMwcHggI2ZmNDQ0NDsgfVxyXG4gIDEwMCUgeyBib3gtc2hhZG93OiAwIDAgNXB4ICNmZjQ0NDQ7IH1cclxuYDtcclxuXHJcbmNvbnN0IEFuaW1hdGVkQmFkZ2UgPSBzdHlsZWQoQmFkZ2UpKCh7IHRoZW1lLCBoYXNOZXdOb3RpZmljYXRpb25zIH0pID0+ICh7XHJcbiAgXCImIC5NdWlCYWRnZS1iYWRnZVwiOiB7XHJcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI2ZmNDQ0NFwiLFxyXG4gICAgY29sb3I6IFwid2hpdGVcIixcclxuICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxyXG4gICAgZm9udFNpemU6IFwiMTJweFwiLFxyXG4gICAgbWluV2lkdGg6IFwiMjBweFwiLFxyXG4gICAgaGVpZ2h0OiBcIjIwcHhcIixcclxuICAgIGJvcmRlclJhZGl1czogXCIxMHB4XCIsXHJcbiAgICBib3JkZXI6IFwiMnB4IHNvbGlkIHdoaXRlXCIsXHJcbiAgICBhbmltYXRpb246IGhhc05ld05vdGlmaWNhdGlvbnNcclxuICAgICAgPyBgJHtwdWxzZX0gMnMgaW5maW5pdGUsICR7Z2xvd30gMnMgaW5maW5pdGVgXHJcbiAgICAgIDogXCJub25lXCIsXHJcbiAgICBib3hTaGFkb3c6IFwiMCAycHggOHB4IHJnYmEoMjU1LCA2OCwgNjgsIDAuMylcIixcclxuICB9LFxyXG59KSk7XHJcblxyXG5jb25zdCBBbmltYXRlZE5vdGlmaWNhdGlvbkljb24gPSBzdHlsZWQoQm94KSgoeyB0aGVtZSwgaGFzTmV3Tm90aWZpY2F0aW9ucyB9KSA9PiAoe1xyXG4gIGFuaW1hdGlvbjogaGFzTmV3Tm90aWZpY2F0aW9ucyA/IGAke3NoYWtlfSAwLjVzIGVhc2UtaW4tb3V0YCA6IFwibm9uZVwiLFxyXG4gIFwiJjpob3ZlclwiOiB7XHJcbiAgICB0cmFuc2Zvcm06IFwic2NhbGUoMS4xKVwiLFxyXG4gICAgdHJhbnNpdGlvbjogXCJ0cmFuc2Zvcm0gMC4ycyBlYXNlLWluLW91dFwiLFxyXG4gIH0sXHJcbn0pKTtcclxuXHJcbmNvbnN0IEFwcEJhciA9IHN0eWxlZChNdWlBcHBCYXIsIHtcclxuICBzaG91bGRGb3J3YXJkUHJvcDogKHByb3ApID0+IHByb3AgIT09IFwib3BlblwiLFxyXG59KSgoeyB0aGVtZSwgb3BlbiB9KSA9PiAoe1xyXG4gIHpJbmRleDogdGhlbWUuekluZGV4LmRyYXdlciArIDEsXHJcbiAgdHJhbnNpdGlvbjogdGhlbWUudHJhbnNpdGlvbnMuY3JlYXRlKFtcIndpZHRoXCIsIFwibWFyZ2luXCJdLCB7XHJcbiAgICBlYXNpbmc6IHRoZW1lLnRyYW5zaXRpb25zLmVhc2luZy5zaGFycCxcclxuICAgIGR1cmF0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5kdXJhdGlvbi5sZWF2aW5nU2NyZWVuLFxyXG4gIH0pLFxyXG4gIG1hcmdpbkxlZnQ6IG9wZW4gPyBkcmF3ZXJXaWR0aCA6IDAsXHJcbiAgd2lkdGg6IG9wZW4gPyBgY2FsYygxMDAlIC0gJHtkcmF3ZXJXaWR0aH0pYCA6IFwiMTAwJVwiLFxyXG4gIFt0aGVtZS5icmVha3BvaW50cy51cChcInNtXCIpXToge1xyXG4gICAgbWFyZ2luTGVmdDogb3BlbiA/IGRyYXdlcldpZHRoIDogdGhlbWUuc3BhY2luZyg5KSxcclxuICAgIHdpZHRoOiBvcGVuXHJcbiAgICAgID8gYGNhbGMoMTAwJSAtICR7ZHJhd2VyV2lkdGh9KWBcclxuICAgICAgOiBgY2FsYygxMDAlIC0gJHt0aGVtZS5zcGFjaW5nKDkpfSlgLFxyXG4gIH0sXHJcbiAgW3RoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJ4c1wiKV06IHtcclxuICAgIG1hcmdpbkxlZnQ6IDAsXHJcbiAgICB3aWR0aDogXCIxMDAlXCIsXHJcbiAgfSxcclxuICAuLi4ob3BlbiAmJiB7XHJcbiAgICB0cmFuc2l0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5jcmVhdGUoW1wid2lkdGhcIiwgXCJtYXJnaW5cIl0sIHtcclxuICAgICAgZWFzaW5nOiB0aGVtZS50cmFuc2l0aW9ucy5lYXNpbmcuc2hhcnAsXHJcbiAgICAgIGR1cmF0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5kdXJhdGlvbi5lbnRlcmluZ1NjcmVlbixcclxuICAgIH0pLFxyXG4gIH0pLFxyXG59KSk7XHJcblxyXG5jb25zdCBEcmF3ZXIgPSBzdHlsZWQoTXVpRHJhd2VyLCB7XHJcbiAgc2hvdWxkRm9yd2FyZFByb3A6IChwcm9wKSA9PiBwcm9wICE9PSBcIm9wZW5cIixcclxufSkoKHsgdGhlbWUsIG9wZW4gfSkgPT4gKHtcclxuICBcIiYgLk11aURyYXdlci1wYXBlclwiOiB7XHJcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IHRoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5zZWNvbmRhcnksXHJcbiAgICBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiLFxyXG4gICAgd2hpdGVTcGFjZTogXCJub3dyYXBcIixcclxuICAgIHdpZHRoOiBvcGVuID8gZHJhd2VyV2lkdGggOiB0aGVtZS5zcGFjaW5nKDcpLFxyXG4gICAgdHJhbnNpdGlvbjogdGhlbWUudHJhbnNpdGlvbnMuY3JlYXRlKFwid2lkdGhcIiwge1xyXG4gICAgICBlYXNpbmc6IHRoZW1lLnRyYW5zaXRpb25zLmVhc2luZy5zaGFycCxcclxuICAgICAgZHVyYXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmR1cmF0aW9uLmNvbXBsZXgsXHJcbiAgICB9KSxcclxuICAgIGJveFNpemluZzogXCJib3JkZXItYm94XCIsXHJcbiAgICAuLi4oIW9wZW4gJiYge1xyXG4gICAgICBvdmVyZmxvd1g6IFwiaGlkZGVuXCIsXHJcbiAgICAgIHRyYW5zaXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmNyZWF0ZShcIndpZHRoXCIsIHtcclxuICAgICAgICBlYXNpbmc6IHRoZW1lLnRyYW5zaXRpb25zLmVhc2luZy5zaGFycCxcclxuICAgICAgICBkdXJhdGlvbjogdGhlbWUudHJhbnNpdGlvbnMuZHVyYXRpb24ubGVhdmluZ1NjcmVlbixcclxuICAgICAgfSksXHJcbiAgICAgIHdpZHRoOiB0aGVtZS5zcGFjaW5nKDcpLFxyXG4gICAgICBbdGhlbWUuYnJlYWtwb2ludHMudXAoXCJzbVwiKV06IHtcclxuICAgICAgICB3aWR0aDogdGhlbWUuc3BhY2luZyg5KSxcclxuICAgICAgfSxcclxuICAgICAgW3RoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJ4c1wiKV06IHtcclxuICAgICAgICB3aWR0aDogXCIxMDAlXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9KSxcclxuICB9LFxyXG59KSk7XHJcblxyXG4vLyBFbmhhbmNlZCBTb2NpYWwgTWVkaWEgU3R5bGUgTm90aWZpY2F0aW9uIFBvcHBlclxyXG5jb25zdCBTb2NpYWxOb3RpZmljYXRpb25Qb3BwZXIgPSAoe1xyXG4gIG9wZW4sXHJcbiAgYW5jaG9yRWwsXHJcbiAgb25DbG9zZSxcclxuICBub3RpZmljYXRpb25zLFxyXG4gIGxvYWRpbmcsXHJcbiAgcmVtb3ZlSGFuZGxlcixcclxuICBzZWxlY3RlZENvbG9yLFxyXG4gIHRoZW1lLFxyXG59KSA9PiAoXHJcbiAgPFBvcHBlclxyXG4gICAgb3Blbj17b3Blbn1cclxuICAgIGFuY2hvckVsPXthbmNob3JFbH1cclxuICAgIHJvbGU9e3VuZGVmaW5lZH1cclxuICAgIHRyYW5zaXRpb25cclxuICAgIHN4PXt7XHJcbiAgICAgIG1heEhlaWdodDogbm90aWZpY2F0aW9ucy5sZW5ndGggPiA0ID8gXCI3MHZoXCIgOiBcImF1dG9cIixcclxuICAgICAgb3ZlcmZsb3dZOiBub3RpZmljYXRpb25zLmxlbmd0aCA+IDQgPyBcImF1dG9cIiA6IFwidmlzaWJsZVwiLFxyXG4gICAgICB6SW5kZXg6IDk5OTksXHJcbiAgICAgIHdpZHRoOiBcIjQwMHB4XCIsXHJcbiAgICAgIG1heFdpZHRoOiBcIjkwdndcIixcclxuICAgIH19XHJcbiAgICBkaXNhYmxlUG9ydGFsXHJcbiAgICBwb3BwZXJPcHRpb25zPXt7XHJcbiAgICAgIG1vZGlmaWVyczogW1xyXG4gICAgICAgIHsgbmFtZTogXCJvZmZzZXRcIiwgb3B0aW9uczogeyBvZmZzZXQ6IFswLCAxNV0gfSB9LFxyXG4gICAgICAgIHsgbmFtZTogXCJwcmV2ZW50T3ZlcmZsb3dcIiwgb3B0aW9uczogeyBwYWRkaW5nOiAyMCB9IH0sXHJcbiAgICAgIF0sXHJcbiAgICB9fVxyXG4gID5cclxuICAgIHsoeyBUcmFuc2l0aW9uUHJvcHMgfSkgPT4gKFxyXG4gICAgICA8Wm9vbSB7Li4uVHJhbnNpdGlvblByb3BzfSB0aW1lb3V0PXszMDB9PlxyXG4gICAgICAgIDxQYXBlclxyXG4gICAgICAgICAgZWxldmF0aW9uPXsyNH1cclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIxNnB4XCIsXHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBcImhpZGRlblwiLFxyXG4gICAgICAgICAgICBib3JkZXI6IFwiMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKVwiLFxyXG4gICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogXCJibHVyKDEwcHgpXCIsXHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6XHJcbiAgICAgICAgICAgICAgXCJsaW5lYXItZ3JhZGllbnQoMTQ1ZGVnLCByZ2JhKDI1NSwyNTUsMjU1LDAuOTUpIDAlLCByZ2JhKDI1NSwyNTUsMjU1LDAuOSkgMTAwJSlcIixcclxuICAgICAgICAgICAgYm94U2hhZG93OlxyXG4gICAgICAgICAgICAgIFwiMCAyMHB4IDQwcHggcmdiYSgwLDAsMCwwLjE1KSwgMCAwIDAgMXB4IHJnYmEoMjU1LDI1NSwyNTUsMC4xKVwiLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Q2xpY2tBd2F5TGlzdGVuZXIgb25DbGlja0F3YXk9e29uQ2xvc2V9PlxyXG4gICAgICAgICAgICA8Qm94IHN4PXt7IG1heFdpZHRoOiBcIjQwMHB4XCIsIG1pbldpZHRoOiBcIjMyMHB4XCIgfX0+XHJcbiAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgcDogMixcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOiBcIjFweCBzb2xpZCByZ2JhKDAsMCwwLDAuMSlcIixcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLmhlYWRlcixcclxuICAgICAgICAgICAgICAgICAgY29sb3I6IHRoZW1lLnBhbGV0dGUudGV4dC53aGl0ZSxcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImg2XCJcclxuICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA2MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTZweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZ2FwOiAxLFxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uc0FjdGl2ZSBzeD17eyBmb250U2l6ZTogXCIyMHB4XCIgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgTm90aWZpY2F0aW9uc1xyXG4gICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwicmdiYSgyNTUsMjU1LDI1NSwwLjIpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIxMnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHB4OiAxLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBweTogMC41LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxMnB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IFwiYm9sZFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGh9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgPEJveCBzeD17eyBtYXhIZWlnaHQ6IFwiNDAwcHhcIiwgb3ZlcmZsb3dZOiBcImF1dG9cIiB9fT5cclxuICAgICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6IFwiZmxleFwiLCBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIiwgcDogNCB9fT5cclxuICAgICAgICAgICAgICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyBjb2xvcj1cInByaW1hcnlcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICkgOiBzZWxlY3RlZE5vdGlmaWNhdGlvbiA/IChcclxuICAgICAgICAgICAgICAgICAgLy8gU2hvdyBub3RpZmljYXRpb24gZGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IHA6IDIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiBcImZsZXhcIiwgYWxpZ25JdGVtczogXCJjZW50ZXJcIiwgbWI6IDIgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkJhY2tUb0xpc3R9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IG1yOiAxLCBwOiAwLjUgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93QmFja0ljb24gc3g9e3sgZm9udFNpemU6IFwiMThweFwiIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBzeD17eyBmb250U2l6ZTogXCIxNnB4XCIsIGZvbnRXZWlnaHQ6IDYwMCB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTm90aWZpY2F0aW9uIERldGFpbHNcclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICA8RGl2aWRlciBzeD17eyBtYjogMiB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTRweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLjYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQucHJpbWFyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWI6IDIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZE5vdGlmaWNhdGlvbi5ub3RpZmljYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTJweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogdGhlbWUucGFsZXR0ZS50ZXh0LnNlY29uZGFyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFN0eWxlOiBcIml0YWxpY1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bmV3IERhdGUoc2VsZWN0ZWROb3RpZmljYXRpb24uY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoXCJlbi1VU1wiLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHllYXI6IFwibnVtZXJpY1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtb250aDogXCJsb25nXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRheTogXCJudW1lcmljXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhvdXI6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW51dGU6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIG5vdGlmaWNhdGlvbnMubWFwKChub3RpZmljYXRpb25EYXRhLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17bm90aWZpY2F0aW9uRGF0YS5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL25vdGlmaWNhdGlvbi1kZXRhaWxzLyR7bm90aWZpY2F0aW9uRGF0YS5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBhc3NIcmVmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVIYW5kbGVyKG5vdGlmaWNhdGlvbkRhdGEuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcDogMixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4IDwgbm90aWZpY2F0aW9ucy5sZW5ndGggLSAxXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiMXB4IHNvbGlkIHJnYmEoMCwwLDAsMC4wNSlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCImOmhvdmVyXCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwicmdiYSgxMDIsIDEyNiwgMjM0LCAwLjA1KVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogXCJwb2ludGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IFwiYWxsIDAuMnMgZWFzZS1pbi1vdXRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFwicmVsYXRpdmVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBkaXNwbGF5OiBcImZsZXhcIiwgZ2FwOiAyLCBhbGlnbkl0ZW1zOiBcImZsZXgtc3RhcnRcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA0MCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogNDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiNTAlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXhTaHJpbms6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiMCA0cHggMTJweCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMylcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvbnNBY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiBcIndoaXRlXCIsIGZvbnRTaXplOiBcIjIwcHhcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGZsZXg6IDEsIG1pbldpZHRoOiAwIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTRweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjMzMzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1iOiAwLjUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdvcmRCcmVhazogXCJicmVhay13b3JkXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25EYXRhLm5vdGlmaWNhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjEycHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzY2NlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaXJjbGVJY29uIHN4PXt7IGZvbnRTaXplOiBcIjRweFwiIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZVRpbWVVVEMobm90aWZpY2F0aW9uRGF0YS5jcmVhdGVkQXQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmRleCA9PT0gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3A6IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3R0b206IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogXCIzcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIwIDJweCAycHggMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICApKVxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmxleERpcmVjdGlvbjogXCJjb2x1bW5cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHA6IDQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dEFsaWduOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGZvbnRTaXplOiBcIjQ4cHhcIiwgY29sb3I6IFwiI2NjY1wiLCBtYjogMiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBzeD17eyBjb2xvcjogXCIjNjY2XCIsIG1iOiAxIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE5vIG5ldyBub3RpZmljYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e3sgY29sb3I6IFwiIzk5OVwiIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFlvdSZhcG9zO3JlIGFsbCBjYXVnaHQgdXAhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAge25vdGlmaWNhdGlvbnMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcDogMixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJUb3A6IFwiMXB4IHNvbGlkIHJnYmEoMCwwLDAsMC4xKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IFwicmdiYSgxMDIsIDEyNiwgMjM0LCAwLjAyKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL25vdGlmaWNhdGlvbnNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50PVwiYVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiYm9keTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjNjY3ZWVhXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiIzc2NGJhMlwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogXCJ0cmFuc2xhdGVYKDJweClcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4ycyBlYXNlLWluLW91dFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBWaWV3IEFsbCBOb3RpZmljYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dGb3J3YXJkSWNvbiBzeD17eyBmb250U2l6ZTogXCIxNnB4XCIgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICA8L0NsaWNrQXdheUxpc3RlbmVyPlxyXG4gICAgICAgIDwvUGFwZXI+XHJcbiAgICAgIDwvWm9vbT5cclxuICAgICl9XHJcbiAgPC9Qb3BwZXI+XHJcbik7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIZWFkZXIoKSB7XHJcbiAgY29uc3QgeyBvcGVuLCBzZXRPcGVuIH0gPSB1c2VDb250ZXh0KERyYXdlckNvbnRleHQpO1xyXG4gIGNvbnN0IHsgbW9kZSwgc2V0TW9kZSB9ID0gdXNlTW9kZSgpO1xyXG4gIGNvbnN0IHsgc2V0R2xvYmFsQ29sb3IgfSA9IHVzZUNvbG9yKCk7XHJcbiAgY29uc3QgdGhlbWUgPSB1c2VUaGVtZSgpO1xyXG4gIGNvbnN0IGlzTW9iaWxlID0gdXNlTWVkaWFRdWVyeSh0aGVtZS5icmVha3BvaW50cy5kb3duKFwic21cIikpO1xyXG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiwgc3RhdHVzIH0gPSB1c2VTZXNzaW9uKCk7XHJcbiAgY29uc3QgaXNBZG1pbiA9IHNlc3Npb24/LnVzZXI/LnJvbGUgPT09IFwiQURNSU5cIjtcclxuICBjb25zdCBub3RpZmljYXRpb25BbmNob3JSZWYgPSB1c2VSZWYobnVsbCk7XHJcblxyXG4gIGNvbnN0IFttb3JlSXRlbSwgc2V0TW9yZUl0ZW1dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtub3RpZmljYXRpb25PcGVuLCBzZXROb3RpZmljYXRpb25PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbYW5jaG9yRWwsIHNldEFuY2hvckVsXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEljb24sIHNldFNlbGVjdGVkSWNvbl0gPSB1c2VTdGF0ZSg8TGlnaHRNb2RlIC8+KTtcclxuICBjb25zdCBbbm90aWZpY2F0aW9ucywgc2V0Tm90aWZpY2F0aW9uc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW3NuYWNrYmFyT3Blbiwgc2V0U25hY2tiYXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc25hY2tiYXJNZXNzYWdlLCBzZXRTbmFja2Jhck1lc3NhZ2VdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NuYWNrYmFyU2V2ZXJpdHksIHNldFNuYWNrYmFyU2V2ZXJpdHldID0gdXNlU3RhdGUoXCJzdWNjZXNzXCIpO1xyXG4gIGNvbnN0IFtoYXNOZXdOb3RpZmljYXRpb25zLCBzZXRIYXNOZXdOb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcHJldmlvdXNOb3RpZmljYXRpb25Db3VudCwgc2V0UHJldmlvdXNOb3RpZmljYXRpb25Db3VudF0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbbGFzdE5vdGlmaWNhdGlvblRpbWUsIHNldExhc3ROb3RpZmljYXRpb25UaW1lXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFtlbmFibGVQb2xsaW5nLCBzZXRFbmFibGVQb2xsaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZE5vdGlmaWNhdGlvbiwgc2V0U2VsZWN0ZWROb3RpZmljYXRpb25dID0gdXNlU3RhdGUobnVsbCk7XHJcblxyXG4gIGNvbnN0IGxvZ29Tb3VyY2UgPSBcIi9IYXNzYW5hTG9nb0QucG5nXCI7XHJcbiAgY29uc3QgZHJhd2VyVmFyaWFudCA9IGlzTW9iaWxlICYmICFvcGVuID8gXCJ0ZW1wb3JhcnlcIiA6IFwicGVybWFuZW50XCI7XHJcbiAgY29uc3Qgc2VsZWN0ZWRDb2xvciA9IHVzZVNlbGVjdGVkQ29sb3IoY29sb3IpO1xyXG5cclxuICBjb25zdCB1c2VySWQgPSBzZXNzaW9uPy51c2VyPy5pZCB8fCBzZXNzaW9uPy51c2VyPy51c2VyX2lkO1xyXG5cclxuICBjb25zdCB7IGxvYWRpbmcsIGVycm9yLCBkYXRhIH0gPSB1c2VRdWVyeShnZXROb3RpZmljYXRpb25zLCB7XHJcbiAgICB2YXJpYWJsZXM6IHsgdXNlcklkOiB1c2VySWQgPyB1c2VySWQgOiB1bmRlZmluZWQgfSxcclxuICAgIHNraXA6ICF1c2VySWQgfHwgc3RhdHVzICE9PSBcImF1dGhlbnRpY2F0ZWRcIixcclxuICAgIHBvbGxJbnRlcnZhbDogZW5hYmxlUG9sbGluZyA/IDMwMDAwIDogMCxcclxuICAgIGZldGNoUG9saWN5OiBcImNhY2hlLWFuZC1uZXR3b3JrXCIsXHJcbiAgICBlcnJvclBvbGljeTogXCJhbGxcIixcclxuICAgIG5vdGlmeU9uTmV0d29ya1N0YXR1c0NoYW5nZTogdHJ1ZSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgeyBkYXRhOiB1bnNlZW5Db3VudERhdGEsIGxvYWRpbmc6IHVuc2VlbkNvdW50TG9hZGluZywgcmVmZXRjaDogcmVmZXRjaFVuc2VlbkNvdW50IH0gPSB1c2VRdWVyeShnZXRVbnNlZW5Ob3RpZmljYXRpb25zQ291bnQsIHtcclxuICAgIHZhcmlhYmxlczogeyB1c2VySWQ6IHVzZXJJZCA/IHVzZXJJZCA6IHVuZGVmaW5lZCB9LFxyXG4gICAgc2tpcDogIXVzZXJJZCB8fCBzdGF0dXMgIT09IFwiYXV0aGVudGljYXRlZFwiLFxyXG4gICAgcG9sbEludGVydmFsOiBlbmFibGVQb2xsaW5nID8gMTAwMDAgOiAwLFxyXG4gICAgZmV0Y2hQb2xpY3k6IFwiY2FjaGUtYW5kLW5ldHdvcmtcIixcclxuICAgIGVycm9yUG9saWN5OiBcImFsbFwiLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBbYWRkTm90aWZpY2F0aW9uVmlld10gPSB1c2VNdXRhdGlvbihtdXRhdGlvbkFkZE5vdGlmaWNhdGlvblZpZXcpO1xyXG4gIGNvbnN0IFttYXJrQWxsQXNTZWVuXSA9IHVzZU11dGF0aW9uKG11dGF0aW9uTWFya0FsbE5vdGlmaWNhdGlvbnNBc1NlZW4pO1xyXG5cclxuICBjb25zdCBwbGF5Tm90aWZpY2F0aW9uU291bmQgPSAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBhdWRpbyA9IG5ldyBBdWRpbyhcIi9zb3VuZHMvbm90aWZpY2F0aW9uLm1wM1wiKTtcclxuICAgICAgYXVkaW8udm9sdW1lID0gMC41O1xyXG4gICAgICBhdWRpby5wbGF5KCkuY2F0Y2goKGUpID0+IGNvbnNvbGUubG9nKFwiQ291bGQgbm90IHBsYXkgbm90aWZpY2F0aW9uIHNvdW5kOlwiLCBlKSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk5vdGlmaWNhdGlvbiBzb3VuZCBub3QgYXZhaWxhYmxlOlwiLCBlcnJvcik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgc2hvd0Jyb3dzZXJOb3RpZmljYXRpb24gPSAobWVzc2FnZSkgPT4ge1xyXG4gICAgaWYgKFwiTm90aWZpY2F0aW9uXCIgaW4gd2luZG93ICYmIE5vdGlmaWNhdGlvbi5wZXJtaXNzaW9uID09PSBcImdyYW50ZWRcIikge1xyXG4gICAgICBuZXcgTm90aWZpY2F0aW9uKFwiSGFzc2FuYSBQb3J0YWxcIiwge1xyXG4gICAgICAgIGJvZHk6IG1lc3NhZ2UsXHJcbiAgICAgICAgaWNvbjogXCIvZmF2aWNvbi5pY29cIixcclxuICAgICAgICBiYWRnZTogXCIvZmF2aWNvbi5pY29cIixcclxuICAgICAgICB0YWc6IFwiaGFzc2FuYS1ub3RpZmljYXRpb25cIixcclxuICAgICAgICByZXF1aXJlSW50ZXJhY3Rpb246IGZhbHNlLFxyXG4gICAgICAgIHNpbGVudDogZmFsc2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoXCJOb3RpZmljYXRpb25cIiBpbiB3aW5kb3cgJiYgTm90aWZpY2F0aW9uLnBlcm1pc3Npb24gPT09IFwiZGVmYXVsdFwiKSB7XHJcbiAgICAgIE5vdGlmaWNhdGlvbi5yZXF1ZXN0UGVybWlzc2lvbigpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChzdGF0dXMgPT09IFwiYXV0aGVudGljYXRlZFwiICYmICF1c2VySWQpIHtcclxuICAgICAgY29uc29sZS53YXJuKFwiVXNlciBJRCBpcyBtaXNzaW5nIGluIGF1dGhlbnRpY2F0ZWQgc2Vzc2lvbjpcIiwgc2Vzc2lvbj8udXNlcik7XHJcbiAgICB9XHJcbiAgICBjb25zb2xlLmxvZyhcIj09PSBTZXNzaW9uIERlYnVnID09PVwiKTtcclxuICAgIGNvbnNvbGUubG9nKFwiU2Vzc2lvbiBTdGF0dXM6XCIsIHN0YXR1cyk7XHJcbiAgICBjb25zb2xlLmxvZyhcIlVzZXIgT2JqZWN0OlwiLCBzZXNzaW9uPy51c2VyKTtcclxuICAgIGNvbnNvbGUubG9nKFwiVXNlciBJRDpcIiwgdXNlcklkKTtcclxuICB9LCBbc2Vzc2lvbiwgc3RhdHVzLCB1c2VySWRdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiPT09IE5vdGlmaWNhdGlvbiBCYWNrZW5kIERlYnVnID09PVwiKTtcclxuICAgIGNvbnNvbGUubG9nKFwiTG9hZGluZzpcIiwgbG9hZGluZyk7XHJcbiAgICBjb25zb2xlLmxvZyhcIkVycm9yOlwiLCBlcnJvcik7XHJcbiAgICBjb25zb2xlLmxvZyhcIkRhdGE6XCIsIGRhdGE/Lm5vdGlmaWNhdGlvbnMpO1xyXG4gICAgY29uc29sZS5sb2coXCJVc2VyIElEOlwiLCB1c2VySWQpO1xyXG5cclxuICAgIGlmICghbG9hZGluZyAmJiAhZXJyb3IgJiYgZGF0YT8ubm90aWZpY2F0aW9ucykge1xyXG4gICAgICBjb25zdCBhbGxOb3RpZmljYXRpb25zID0gZGF0YS5ub3RpZmljYXRpb25zO1xyXG4gICAgICBjb25zdCBjdXJyZW50Q291bnQgPSBhbGxOb3RpZmljYXRpb25zLmxlbmd0aDtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQmFja2VuZCBDb25uZWN0ZWQgU3VjY2Vzc2Z1bGx5IVwiKTtcclxuICAgICAgY29uc29sZS5sb2coXCJBbGwgbm90aWZpY2F0aW9ucyByZWNlaXZlZDpcIiwgYWxsTm90aWZpY2F0aW9ucyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQ291bnQ6XCIsIGN1cnJlbnRDb3VudCk7XHJcblxyXG4gICAgICBzZXRFbmFibGVQb2xsaW5nKHRydWUpO1xyXG5cclxuICAgICAgaWYgKFxyXG4gICAgICAgIGN1cnJlbnRDb3VudCA+IHByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQgJiZcclxuICAgICAgICBwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50ID4gMFxyXG4gICAgICApIHtcclxuICAgICAgICBzZXRIYXNOZXdOb3RpZmljYXRpb25zKHRydWUpO1xyXG4gICAgICAgIHNldExhc3ROb3RpZmljYXRpb25UaW1lKERhdGUubm93KCkpO1xyXG5cclxuICAgICAgICBwbGF5Tm90aWZpY2F0aW9uU291bmQoKTtcclxuXHJcbiAgICAgICAgaWYgKGN1cnJlbnRDb3VudCA+IHByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQpIHtcclxuICAgICAgICAgIGNvbnN0IG5ld05vdGlmaWNhdGlvbkNvdW50ID0gY3VycmVudENvdW50IC0gcHJldmlvdXNOb3RpZmljYXRpb25Db3VudDtcclxuICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPVxyXG4gICAgICAgICAgICBuZXdOb3RpZmljYXRpb25Db3VudCA9PT0gMVxyXG4gICAgICAgICAgICAgID8gXCJZb3UgaGF2ZSBhIG5ldyBub3RpZmljYXRpb24hXCJcclxuICAgICAgICAgICAgICA6IGBZb3UgaGF2ZSAke25ld05vdGlmaWNhdGlvbkNvdW50fSBuZXcgbm90aWZpY2F0aW9ucyFgO1xyXG5cclxuICAgICAgICAgIHNob3dCcm93c2VyTm90aWZpY2F0aW9uKG1lc3NhZ2UpO1xyXG5cclxuICAgICAgICAgIHNldFNuYWNrYmFyTWVzc2FnZShtZXNzYWdlKTtcclxuICAgICAgICAgIHNldFNuYWNrYmFyU2V2ZXJpdHkoXCJpbmZvXCIpO1xyXG4gICAgICAgICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBzZXRIYXNOZXdOb3RpZmljYXRpb25zKGZhbHNlKTtcclxuICAgICAgICB9LCAxMDAwKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc2V0Tm90aWZpY2F0aW9ucyhhbGxOb3RpZmljYXRpb25zKTtcclxuICAgICAgc2V0UHJldmlvdXNOb3RpZmljYXRpb25Db3VudChjdXJyZW50Q291bnQpO1xyXG4gICAgICBjb25zb2xlLmxvZyhgTm90aWZpY2F0aW9uIGNvdW50IHVwZGF0ZWQgdG86ICR7Y3VycmVudENvdW50fWApO1xyXG4gICAgfSBlbHNlIGlmIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiQmFja2VuZCBDb25uZWN0aW9uIEVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgIGlmIChlcnJvci5ncmFwaFFMRXJyb3JzKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkdyYXBoUUwgRXJyb3JzOlwiLCBlcnJvci5ncmFwaFFMRXJyb3JzLm1hcCgoZSkgPT4gZS5tZXNzYWdlKSk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKGVycm9yLm5ldHdvcmtFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJOZXR3b3JrIEVycm9yOlwiLCBlcnJvci5uZXR3b3JrRXJyb3IpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBzZXRFbmFibGVQb2xsaW5nKGZhbHNlKTtcclxuXHJcbiAgICAgIHNldFNuYWNrYmFyTWVzc2FnZShcIkZhaWxlZCB0byBsb2FkIG5vdGlmaWNhdGlvbnMuIFJldHJ5aW5nIGluIDMwIHNlY29uZHMuLi5cIik7XHJcbiAgICAgIHNldFNuYWNrYmFyU2V2ZXJpdHkoXCJlcnJvclwiKTtcclxuICAgICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xyXG5cclxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgc2V0RW5hYmxlUG9sbGluZyh0cnVlKTtcclxuICAgICAgfSwgMzAwMDApO1xyXG4gICAgfSBlbHNlIGlmICghdXNlcklkKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybihcIk5vIHVzZXIgSUQgZm91bmQgaW4gc2Vzc2lvblwiKTtcclxuICAgIH0gZWxzZSBpZiAoIWxvYWRpbmcgJiYgIWRhdGEpIHtcclxuICAgICAgY29uc29sZS53YXJuKFwiTm8gZGF0YSByZWNlaXZlZCBmcm9tIGJhY2tlbmRcIik7XHJcbiAgICB9XHJcbiAgfSwgW2xvYWRpbmcsIGVycm9yLCBkYXRhLCBwcmV2aW91c05vdGlmaWNhdGlvbkNvdW50LCB1c2VySWRdKTtcclxuXHJcbiAgaWYgKHN0YXR1cyA9PT0gXCJsb2FkaW5nXCIpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogXCJmbGV4XCIsIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLCBwOiA0IH19PlxyXG4gICAgICAgIDxDaXJjdWxhclByb2dyZXNzIGNvbG9yPVwicHJpbWFyeVwiIC8+XHJcbiAgICAgIDwvQm94PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGNvbnN0IHRvZ2dsZURyYXdlciA9ICgpID0+IHNldE9wZW4oIW9wZW4pO1xyXG4gIGNvbnN0IGhhbmRsZVNldE1vcmVJdGVtQ2xpY2sgPSAoKSA9PiBzZXRNb3JlSXRlbSghbW9yZUl0ZW0pO1xyXG4gIGNvbnN0IGhhbmRsZUNsaWNrID0gKGV2ZW50KSA9PiBzZXRBbmNob3JFbChldmVudC5jdXJyZW50VGFyZ2V0KTtcclxuICBjb25zdCBoYW5kbGVDbG9zZSA9ICgpID0+IHNldEFuY2hvckVsKG51bGwpO1xyXG4gIGNvbnN0IGhhbmRsZU5vdGlmaWNhdGlvblRvZ2dsZSA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnN0IHdhc09wZW4gPSBub3RpZmljYXRpb25PcGVuO1xyXG4gICAgc2V0Tm90aWZpY2F0aW9uT3BlbigocHJldikgPT4gIXByZXYpO1xyXG5cclxuICAgIGlmICghd2FzT3BlbiAmJiB1c2VySWQpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBhd2FpdCBtYXJrQWxsQXNTZWVuKHtcclxuICAgICAgICAgIHZhcmlhYmxlczogeyB1c2VySWQgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICByZWZldGNoVW5zZWVuQ291bnQoKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coXCJBbGwgbm90aWZpY2F0aW9ucyBtYXJrZWQgYXMgc2VlblwiKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbWFya2luZyBub3RpZmljYXRpb25zIGFzIHNlZW46XCIsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZU5vdGlmaWNhdGlvbkNsb3NlID0gKGV2ZW50KSA9PiB7XHJcbiAgICBpZiAobm90aWZpY2F0aW9uQW5jaG9yUmVmLmN1cnJlbnQ/LmNvbnRhaW5zKGV2ZW50LnRhcmdldCkpIHJldHVybjtcclxuICAgIHNldE5vdGlmaWNhdGlvbk9wZW4oZmFsc2UpO1xyXG4gICAgc2V0U2VsZWN0ZWROb3RpZmljYXRpb24obnVsbCk7IC8vIFJlc2V0IHNlbGVjdGVkIG5vdGlmaWNhdGlvbiB3aGVuIGNsb3NpbmdcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25DbGljayA9IChub3RpZmljYXRpb24pID0+IHtcclxuICAgIHNldFNlbGVjdGVkTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQmFja1RvTGlzdCA9ICgpID0+IHtcclxuICAgIHNldFNlbGVjdGVkTm90aWZpY2F0aW9uKG51bGwpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVRoZW1lQ2hhbmdlID0gKHRoZW1lLCBpY29uKSA9PiB7XHJcbiAgICBzZXRNb2RlKHRoZW1lKTtcclxuICAgIHNldFNlbGVjdGVkSWNvbihpY29uKTtcclxuICAgIGhhbmRsZUNsb3NlKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQ29sb3JDaGFuZ2UgPSAoY29sb3IsIGljb24pID0+IHtcclxuICAgIHNldEdsb2JhbENvbG9yKGNvbG9yKTtcclxuICAgIHNldFNlbGVjdGVkSWNvbihpY29uKTtcclxuICAgIGhhbmRsZUNsb3NlKCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVtb3ZlQW5ub3VuY2VtZW50SGFuZGxlciA9IGFzeW5jIChub3RpZmljYXRpb25JZCkgPT4ge1xyXG4gICAgaWYgKCF1c2VySWQpIHtcclxuICAgICAgc2V0U25hY2tiYXJNZXNzYWdlKFwiVXNlciBub3QgYXV0aGVudGljYXRlZFwiKTtcclxuICAgICAgc2V0U25hY2tiYXJTZXZlcml0eShcImVycm9yXCIpO1xyXG4gICAgICBzZXRTbmFja2Jhck9wZW4odHJ1ZSk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFkZE5vdGlmaWNhdGlvblZpZXcoe1xyXG4gICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgbm90aWZpY2F0aW9uSWQ6IG5vdGlmaWNhdGlvbklkLFxyXG4gICAgICAgICAgdXNlcklkOiB1c2VySWQsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5hZGROb3RpZmljYXRpb25WaWV3KSB7XHJcbiAgICAgICAgY29uc3QgdXBkYXRlZE5vdGlmaWNhdGlvbnMgPSBub3RpZmljYXRpb25zLmZpbHRlcihcclxuICAgICAgICAgIChuKSA9PiBuLmlkICE9PSBub3RpZmljYXRpb25JZFxyXG4gICAgICAgICk7XHJcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9ucyh1cGRhdGVkTm90aWZpY2F0aW9ucyk7XHJcbiAgICAgICAgc2V0UHJldmlvdXNOb3RpZmljYXRpb25Db3VudCh1cGRhdGVkTm90aWZpY2F0aW9ucy5sZW5ndGgpO1xyXG5cclxuICAgICAgICBzZXRTbmFja2Jhck1lc3NhZ2UoXCJOb3RpZmljYXRpb24gbWFya2VkIGFzIHZpZXdlZFwiKTtcclxuICAgICAgICBzZXRTbmFja2JhclNldmVyaXR5KFwic3VjY2Vzc1wiKTtcclxuICAgICAgICBzZXRTbmFja2Jhck9wZW4odHJ1ZSk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBtYXJraW5nIG5vdGlmaWNhdGlvbjpcIiwgZXJyb3IpO1xyXG4gICAgICBzZXRTbmFja2Jhck1lc3NhZ2UoXCJGYWlsZWQgdG8gbWFyayBub3RpZmljYXRpb25cIik7XHJcbiAgICAgIHNldFNuYWNrYmFyU2V2ZXJpdHkoXCJlcnJvclwiKTtcclxuICAgICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICB7KG1vZGUgPT09IFwibGlnaHRcIiB8fCBtb2RlID09PSBcImRhcmtcIikgJiYgKFxyXG4gICAgICAgIDxBcHBCYXIgcG9zaXRpb249XCJmaXhlZFwiIG9wZW49e29wZW59PlxyXG4gICAgICAgICAgPFRvb2xiYXJcclxuICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxyXG4gICAgICAgICAgICAgIHdpZHRoOiBcIjEwMCVcIixcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHRoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5oZWFkZXIsXHJcbiAgICAgICAgICAgICAgekluZGV4OiAxLFxyXG4gICAgICAgICAgICAgIGJvcmRlclRvcDogYDRweCBzb2xpZCAke3RoZW1lLnBhbGV0dGUudGV4dC5wdXJwbGV9YCxcclxuICAgICAgICAgICAgICBib3JkZXJCb3R0b206IGA0cHggc29saWQgJHt0aGVtZS5wYWxldHRlLnRleHQucHVycGxlfWAsXHJcbiAgICAgICAgICAgICAgW3RoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJ4c1wiKV06IHtcclxuICAgICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246IFwiY29sdW1uXCIsXHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICBlZGdlPVwic3RhcnRcIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJUb2dnbGUgZHJhd2VyXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVEcmF3ZXJ9XHJcbiAgICAgICAgICAgICAgc3g9e3sgbWFyZ2luUmlnaHQ6IFwiMTVweFwiIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7b3BlbiA/IChcclxuICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICBzcmM9XCIvTmF2SWNvbnMvbGVmdF9oYW1idXJnZXIuc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgYWx0PVwiQ2xvc2UgZHJhd2VyXCJcclxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezI0fVxyXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ9ezI0fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPE1lbnVJY29uIHN4PXt7IGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQud2hpdGUgfX0gLz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgY29tcG9uZW50PVwiaDFcIlxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJoNlwiXHJcbiAgICAgICAgICAgICAgY29sb3I9XCJpbmhlcml0XCJcclxuICAgICAgICAgICAgICBub1dyYXBcclxuICAgICAgICAgICAgICBzeD17eyBmbGV4R3JvdzogMSB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIj5cclxuICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICBzcmM9e2xvZ29Tb3VyY2V9XHJcbiAgICAgICAgICAgICAgICAgIGFsdD1cIkhhc3NhbmEgTG9nb1wiXHJcbiAgICAgICAgICAgICAgICAgIGxvYWRpbmc9XCJsYXp5XCJcclxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezE4MH1cclxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs0Mn1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgIDxCb3hcclxuICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxyXG4gICAgICAgICAgICAgICAgZ2FwOiB7IHhzOiAwLjUsIHNtOiAxLCBtZDogMS41IH0sXHJcbiAgICAgICAgICAgICAgICBmbGV4U2hyaW5rOiAwLFxyXG4gICAgICAgICAgICAgICAgW3RoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJ4c1wiKV06IHtcclxuICAgICAgICAgICAgICAgICAgZmxleERpcmVjdGlvbjogXCJyb3dcIixcclxuICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPEJveFxyXG4gICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFwicmVsYXRpdmVcIixcclxuICAgICAgICAgICAgICAgICAgXCImOjphZnRlclwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29udGVudDogXCInJ1wiLFxyXG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IFwiLThweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHRvcDogXCI1MCVcIixcclxuICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IFwidHJhbnNsYXRlWSgtNTAlKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiBcIjFweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogXCIyNHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgIFt0aGVtZS5icmVha3BvaW50cy5kb3duKFwic21cIildOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcIm5vbmVcIixcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2hhbmdlIHRoZW1lIG9yIGNvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgYXJpYS1jb250cm9scz1cInRoZW1lLW1lbnVcIlxyXG4gICAgICAgICAgICAgICAgICBhcmlhLWhhc3BvcHVwPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxyXG4gICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcImluaGVyaXRcIixcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiB7IHhzOiBcIjZweFwiLCBzbTogXCI4cHhcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwicmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IFwic2NhbGUoMS4wNSlcIixcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IFwiYWxsIDAuMnMgZWFzZS1pbi1vdXRcIixcclxuICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkSWNvbn1cclxuICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgICAgICA8UG9wcGVyXHJcbiAgICAgICAgICAgICAgICBpZD1cInRoZW1lLW1lbnVcIlxyXG4gICAgICAgICAgICAgICAgb3Blbj17Qm9vbGVhbihhbmNob3JFbCl9XHJcbiAgICAgICAgICAgICAgICBhbmNob3JFbD17YW5jaG9yRWx9XHJcbiAgICAgICAgICAgICAgICBwbGFjZW1lbnQ9XCJib3R0b20tZW5kXCJcclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb25cclxuICAgICAgICAgICAgICAgIHN4PXt7IHpJbmRleDogMTAwMDAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7KHsgVHJhbnNpdGlvblByb3BzIH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgPFNsaWRlIHsuLi5UcmFuc2l0aW9uUHJvcHN9IGRpcmVjdGlvbj1cImRvd25cIiB0aW1lb3V0PXszNTB9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxQYXBlclxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnNlY29uZGFyeSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjI1cHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENsaWNrQXdheUxpc3RlbmVyIG9uQ2xpY2tBd2F5PXtoYW5kbGVDbG9zZX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51TGlzdCBhdXRvRm9jdXNJdGVtPXtCb29sZWFuKGFuY2hvckVsKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUaGVtZUNoYW5nZShcImxpZ2h0XCIsIDxMaWdodE1vZGUgLz4pfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaWdodE1vZGUgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVGhlbWVDaGFuZ2UoXCJkYXJrXCIsIDxEYXJrTW9kZSAvPil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPERhcmtNb2RlIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVudUl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNvbG9yQ2hhbmdlKFwiYmx1ZVwiLCA8Q2lyY2xlSWNvbiBzeD17eyBmaWxsOiB0aGVtZS5wYWxldHRlLmJsdWUubWFpbiB9fSAvPilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2lyY2xlSWNvbiBzeD17eyBmaWxsOiB0aGVtZS5wYWxldHRlLmJsdWUubWFpbiB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDb2xvckNoYW5nZShcImdyZWVuXCIsIDxDaXJjbGVJY29uIHN4PXt7IGZpbGw6IHRoZW1lLnBhbGV0dGUuZ3JlZW4ubWFpbiB9fSAvPilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2lyY2xlSWNvbiBzeD17eyBmaWxsOiB0aGVtZS5wYWxldHRlLmdyZWVuLm1haW4gfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ29sb3JDaGFuZ2UoXCJwdXJwbGVcIiwgPENpcmNsZUljb24gc3g9e3sgZmlsbDogdGhlbWUucGFsZXR0ZS5wdXJwbGUubWFpbiB9fSAvPilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2lyY2xlSWNvbiBzeD17eyBmaWxsOiB0aGVtZS5wYWxldHRlLnB1cnBsZS5tYWluIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9NZW51TGlzdD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2xpY2tBd2F5TGlzdGVuZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICAgICAgICAgICAgPC9TbGlkZT5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9Qb3BwZXI+XHJcbiAgICAgICAgICAgICAgPEJveCBzeD17eyBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiIH19PlxyXG4gICAgICAgICAgICAgICAgPEFuaW1hdGVkTm90aWZpY2F0aW9uSWNvbiBoYXNOZXdOb3RpZmljYXRpb25zPXtoYXNOZXdOb3RpZmljYXRpb25zfT5cclxuICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImluaGVyaXRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlZj17bm90aWZpY2F0aW9uQW5jaG9yUmVmfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU5vdGlmaWNhdGlvblRvZ2dsZX1cclxuICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPXtgU2hvdyAke25vdGlmaWNhdGlvbnMubGVuZ3RofSBub3RpZmljYXRpb25zfSBub3RpZmljYXRpb25zYH1cclxuICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFwicmVsYXRpdmVcIixcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcImluaGVyaXRcIixcclxuICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IHsgeHM6IFwiOHB4XCIsIHNtOiBcIjEwcHhcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgXCImOmhvdmVyXCI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IFwic2NhbGUoMS4wNSlcIixcclxuICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAwLjJzIGVhc2UtaW4tb3V0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxBbmltYXRlZEJhZGdlXHJcbiAgICAgICAgICAgICAgICAgICAgICBiYWRnZUNvbnRlbnQ9e3Vuc2VlbkNvdW50RGF0YT8udW5zZWVuTm90aWZpY2F0aW9uc0NvdW50IHx8IDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBoYXNOZXdOb3RpZmljYXRpb25zPXtoYXNOZXdOb3RpZmljYXRpb25zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbWF4PXs5OX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogaGFzTmV3Tm90aWZpY2F0aW9ucyA/IFwiI2ZmNDQ0NFwiIDogXCJpbmhlcml0XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogeyB4czogXCIyMHB4XCIsIHNtOiBcIjI0cHhcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiBoYXNOZXdOb3RpZmljYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJkcm9wLXNoYWRvdygwIDAgOHB4IHJnYmEoMjU1LCA2OCwgNzAsIDAuNSkpXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPE5vdGlmaWNhdGlvbnMgc3g9e3sgZm9udFNpemU6IHsgeHM6IFwiMjBweFwiLCBzbTogXCIyNHB4XCIgfSB9fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L0FuaW1hdGVkQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvQW5pbWF0ZWROb3RpZmljYXRpb25JY29uPlxyXG4gICAgICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgICAgICAgIDxTb2NpYWxOb3RpZmljYXRpb25Qb3BwZXJcclxuICAgICAgICAgICAgICAgIG9wZW49e25vdGlmaWNhdGlvbk9wZW59XHJcbiAgICAgICAgICAgICAgICBhbmNob3JFbD17bm90aWZpY2F0aW9uQW5jaG9yUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAgICAgICBvbkNsb3NlPXtoYW5kbGVOb3RpZmljYXRpb25DbG9zZX1cclxuICAgICAgICAgICAgICAgIG5vdGlmaWNhdGlvbnM9e25vdGlmaWNhdGlvbnN9XHJcbiAgICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxyXG4gICAgICAgICAgICAgICAgcmVtb3ZlSGFuZGxlcj17cmVtb3ZlQW5ub3VuY2VtZW50SGFuZGxlcn1cclxuICAgICAgICAgICAgICAgIHNlbGVjdGVkQ29sb3I9e3NlbGVjdGVkQ29sb3J9XHJcbiAgICAgICAgICAgICAgICB0aGVtZT17dGhlbWV9XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZE5vdGlmaWNhdGlvbj17c2VsZWN0ZWROb3RpZmljYXRpb259XHJcbiAgICAgICAgICAgICAgICBvbk5vdGlmaWNhdGlvbkNsaWNrPXtoYW5kbGVOb3RpZmljYXRpb25DbGlja31cclxuICAgICAgICAgICAgICAgIG9uQmFja1RvTGlzdD17aGFuZGxlQmFja1RvTGlzdH1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgIDwvVG9vbGJhcj5cclxuICAgICAgICA8L0FwcEJhcj5cclxuICAgICAgKX1cclxuICAgICAgPERyYXdlclxyXG4gICAgICAgIHZhcmlhbnQ9e2RyYXdlclZhcmlhbnR9XHJcbiAgICAgICAgb3Blbj17b3Blbn1cclxuICAgICAgICBzeD17e1xyXG4gICAgICAgICAgekluZGV4OiAyLFxyXG4gICAgICAgICAgYm9yZGVyUmlnaHQ6IG1vZGUgPT09IFwibGlnaHRcIiA/IFwiMXB4IHNvbGlkIHdoaXRlXCIgOiBcIm5vbmVcIixcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgPEJveFxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiB0aGVtZS5wYWxldHRlLmJhY2tncm91bmQucHJpbWFyeSxcclxuICAgICAgICAgICAgbWFyZ2luOiBcIjEwcHhcIixcclxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjAuNjI1cmVtXCIsXHJcbiAgICAgICAgICAgIGhlaWdodDogXCIxMDAlXCIsXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxUb29sYmFyXHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcclxuICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiYXV0b1wiLFxyXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiBcImZsZXgtZW5kXCIsXHJcbiAgICAgICAgICAgICAgcHg6IFsxXSxcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPFByb2ZpbGUgLz5cclxuICAgICAgICAgIDwvVG9vbGJhcj5cclxuICAgICAgICAgIDxMaXN0XHJcbiAgICAgICAgICAgIGNvbXBvbmVudD1cIm5hdlwiXHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXHJcbiAgICAgICAgICAgICAgZmxleERpcmVjdGlvbjogXCJjb2x1bW5cIixcclxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsXHJcbiAgICAgICAgICAgICAgaGVpZ2h0OiBcIjgwdmhcIixcclxuICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMjBweFwiLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8Qm94PlxyXG4gICAgICAgICAgICAgIDxNYWluTGlzdEl0ZW1zIC8+XHJcbiAgICAgICAgICAgICAge2lzQWRtaW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIG9uQ2xpY2s9e2hhbmRsZVNldE1vcmVJdGVtQ2xpY2t9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3NJY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiBtb2RlICE9PSBcImxpZ2h0XCIgPyBcIndoaXRlXCIgOiBcImluaGVyaXRcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1UZXh0IHByaW1hcnk9XCJBZG1pbiBTZXR0aW5nc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAge21vcmVJdGVtID8gPEV4cGFuZExlc3MgLz4gOiA8RXhwYW5kTW9yZSAvPn1cclxuICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPENvbGxhcHNlIGluPXttb3JlSXRlbX0gdGltZW91dD1cImF1dG9cIiB1bm1vdW50T25FeGl0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaXN0IGNvbXBvbmVudD1cImRpdlwiIGRpc2FibGVQYWRkaW5nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9uZXdzXCIgcGFzc0hyZWY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUJ1dHRvbiBzeD17eyBoZWlnaHQ6IDMwIH19IHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TmV3c3BhcGVySWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiTmV3c1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL2Fubm91bmNlbWVudHNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYW1wYWlnblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiQW5ub3VuY2VtZW50c1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL2V2ZW50c1wiIHBhc3NIcmVmPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1CdXR0b24gc3g9e3sgaGVpZ2h0OiAzMCB9fSBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENlbGVicmF0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiBtb2RlICE9PSBcImxpZ2h0XCIgPyBcIndoaXRlXCIgOiBcImluaGVyaXRcIiB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1UZXh0IHByaW1hcnk9XCJFdmVudHNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9xdW90ZXNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtYXRRdW90ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiUXVvdGVzXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW5PZmZlclwiIHBhc3NIcmVmPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1CdXR0b24gc3g9e3sgaGVpZ2h0OiAzMCB9fSBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExvY2FsT2ZmZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIk9mZmVyc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL25vdGlmaWNhdGlvbnNcIiBwYXNzSHJlZj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uIHN4PXt7IGhlaWdodDogMzAgfX0gc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxOb3RpZmljYXRpb25zQWN0aXZlUm91bmRlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBjb2xvcjogbW9kZSAhPT0gXCJsaWdodFwiID8gXCJ3aGl0ZVwiIDogXCJpbmhlcml0XCIgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaXN0SXRlbUljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtVGV4dCBwcmltYXJ5PVwiTm90aWZpY2F0aW9uc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL2xlYXZlc1wiIHBhc3NIcmVmPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1CdXR0b24gc3g9e3sgaGVpZ2h0OiAzMCB9fSBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhc2tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3sgY29sb3I6IG1vZGUgIT09IFwibGlnaHRcIiA/IFwid2hpdGVcIiA6IFwiaW5oZXJpdFwiIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHQgcHJpbWFyeT1cIkxlYXZlc1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGlzdEl0ZW1CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaXN0PlxyXG4gICAgICAgICAgICAgICAgICA8L0NvbGxhcHNlPlxyXG4gICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9Cb3g+XHJcbiAgICAgICAgICAgIDxCb3g+XHJcbiAgICAgICAgICAgICAgPFNlY29uZGFyeUxpc3RJdGVtcyAvPlxyXG4gICAgICAgICAgICA8L0JveD5cclxuICAgICAgICAgIDwvTGlzdD5cclxuICAgICAgICA8L0JveD5cclxuICAgICAgPC9EcmF3ZXI+XHJcbiAgICAgIDxTbmFja2JhclxyXG4gICAgICAgIG9wZW49e3NuYWNrYmFyT3Blbn1cclxuICAgICAgICBhdXRvSGlkZUR1cmF0aW9uPXs2MDAwfVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNuYWNrYmFyT3BlbihmYWxzZSl9XHJcbiAgICAgID5cclxuICAgICAgICA8QWxlcnRcclxuICAgICAgICAgIHNldmVyaXR5PXtzbmFja2JhclNldmVyaXR5fVxyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U25hY2tiYXJPcGVuKGZhbHNlKX1cclxuICAgICAgICAgIHN4PXt7IHdpZHRoOiBcIjEwMCVcIiB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIHtzbmFja2Jhck1lc3NhZ2V9XHJcbiAgICAgICAgPC9BbGVydD5cclxuICAgICAgPC9TbmFja2Jhcj5cclxuICAgIDwvPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImdxbCIsIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VDb250ZXh0IiwidXNlUmVmIiwidXNlRWZmZWN0Iiwic3R5bGVkIiwidXNlVGhlbWUiLCJNdWlEcmF3ZXIiLCJ1c2VTZWxlY3RlZENvbG9yIiwiY29sb3IiLCJmb3JtYXREYXRlVGltZVVUQyIsIkJveCIsInVzZU1lZGlhUXVlcnkiLCJNdWlBcHBCYXIiLCJUb29sYmFyIiwiTGlzdCIsIlR5cG9ncmFwaHkiLCJJY29uQnV0dG9uIiwiVGFzayIsIk5vdGlmaWNhdGlvbnMiLCJEYXJrTW9kZSIsIkxpZ2h0TW9kZSIsIkNhbXBhaWduIiwiQ2VsZWJyYXRpb24iLCJGb3JtYXRRdW90ZSIsIk5vdGlmaWNhdGlvbnNBY3RpdmVSb3VuZGVkIiwiTG9jYWxPZmZlciIsIk5vdGlmaWNhdGlvbnNBY3RpdmUiLCJBcnJvd0ZvcndhcmRJY29uIiwiQXJyb3dCYWNrSWNvbiIsIk1lbnVJY29uIiwiRXhwYW5kTGVzcyIsIkV4cGFuZE1vcmUiLCJTZXR0aW5nc0ljb24iLCJOZXdzcGFwZXJJY29uIiwiQ2lyY2xlSWNvbiIsIkltYWdlIiwiQmFkZ2UiLCJMaW5rIiwiUG9wcGVyIiwiUGFwZXIiLCJDbGlja0F3YXlMaXN0ZW5lciIsIk1lbnVJdGVtIiwiTWVudUxpc3QiLCJMaXN0SXRlbSIsIkxpc3RJdGVtVGV4dCIsIkNvbGxhcHNlIiwiTGlzdEl0ZW1JY29uIiwiTGlzdEl0ZW1CdXR0b24iLCJTbGlkZSIsIlpvb20iLCJrZXlmcmFtZXMiLCJ1c2VDb2xvciIsIkRyYXdlckNvbnRleHQiLCJ1c2VNb2RlIiwidXNlTXV0YXRpb24iLCJ1c2VRdWVyeSIsImdldE5ld05vdGlmaWNhdGlvbnMiLCJnZXROb3RpZmljYXRpb25zIiwiZ2V0VW5zZWVuTm90aWZpY2F0aW9uc0NvdW50IiwibXV0YXRpb25NYXJrQWxsTm90aWZpY2F0aW9uc0FzU2VlbiIsIm11dGF0aW9uQWRkTm90aWZpY2F0aW9uVmlldyIsInVzZVNlc3Npb24iLCJDaXJjdWxhclByb2dyZXNzIiwiU25hY2tiYXIiLCJBbGVydCIsIkRpdmlkZXIiLCJNYWluTGlzdEl0ZW1zIiwiU2Vjb25kYXJ5TGlzdEl0ZW1zIiwiUHJvZmlsZSIsInVzZVJvdXRlciIsImRyYXdlcldpZHRoIiwicHVsc2UiLCJzaGFrZSIsImdsb3ciLCJBbmltYXRlZEJhZGdlIiwidGhlbWUiLCJoYXNOZXdOb3RpZmljYXRpb25zIiwiYmFja2dyb3VuZENvbG9yIiwiZm9udFdlaWdodCIsImZvbnRTaXplIiwibWluV2lkdGgiLCJoZWlnaHQiLCJib3JkZXJSYWRpdXMiLCJib3JkZXIiLCJhbmltYXRpb24iLCJib3hTaGFkb3ciLCJBbmltYXRlZE5vdGlmaWNhdGlvbkljb24iLCJ0cmFuc2Zvcm0iLCJ0cmFuc2l0aW9uIiwiQXBwQmFyIiwic2hvdWxkRm9yd2FyZFByb3AiLCJwcm9wIiwib3BlbiIsInpJbmRleCIsImRyYXdlciIsInRyYW5zaXRpb25zIiwiY3JlYXRlIiwiZWFzaW5nIiwic2hhcnAiLCJkdXJhdGlvbiIsImxlYXZpbmdTY3JlZW4iLCJtYXJnaW5MZWZ0Iiwid2lkdGgiLCJicmVha3BvaW50cyIsInVwIiwic3BhY2luZyIsImRvd24iLCJlbnRlcmluZ1NjcmVlbiIsIkRyYXdlciIsInBhbGV0dGUiLCJiYWNrZ3JvdW5kIiwic2Vjb25kYXJ5IiwicG9zaXRpb24iLCJ3aGl0ZVNwYWNlIiwiY29tcGxleCIsImJveFNpemluZyIsIm92ZXJmbG93WCIsIlNvY2lhbE5vdGlmaWNhdGlvblBvcHBlciIsImFuY2hvckVsIiwib25DbG9zZSIsIm5vdGlmaWNhdGlvbnMiLCJsb2FkaW5nIiwicmVtb3ZlSGFuZGxlciIsInNlbGVjdGVkQ29sb3IiLCJyb2xlIiwidW5kZWZpbmVkIiwic3giLCJtYXhIZWlnaHQiLCJsZW5ndGgiLCJvdmVyZmxvd1kiLCJtYXhXaWR0aCIsImRpc2FibGVQb3J0YWwiLCJwb3BwZXJPcHRpb25zIiwibW9kaWZpZXJzIiwibmFtZSIsIm9wdGlvbnMiLCJvZmZzZXQiLCJwYWRkaW5nIiwiVHJhbnNpdGlvblByb3BzIiwidGltZW91dCIsImVsZXZhdGlvbiIsIm92ZXJmbG93IiwiYmFja2Ryb3BGaWx0ZXIiLCJvbkNsaWNrQXdheSIsInAiLCJib3JkZXJCb3R0b20iLCJoZWFkZXIiLCJ0ZXh0Iiwid2hpdGUiLCJ2YXJpYW50IiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJnYXAiLCJweCIsInB5IiwianVzdGlmeUNvbnRlbnQiLCJzZWxlY3RlZE5vdGlmaWNhdGlvbiIsIm1iIiwib25DbGljayIsIm9uQmFja1RvTGlzdCIsIm1yIiwic2l6ZSIsImxpbmVIZWlnaHQiLCJwcmltYXJ5Iiwibm90aWZpY2F0aW9uIiwiZm9udFN0eWxlIiwiRGF0ZSIsImNyZWF0ZWRBdCIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImhvdXIiLCJtaW51dGUiLCJtYXAiLCJub3RpZmljYXRpb25EYXRhIiwiaW5kZXgiLCJocmVmIiwiaWQiLCJwYXNzSHJlZiIsImN1cnNvciIsImZsZXhTaHJpbmsiLCJmbGV4Iiwid29yZEJyZWFrIiwibGVmdCIsInRvcCIsImJvdHRvbSIsImZsZXhEaXJlY3Rpb24iLCJ0ZXh0QWxpZ24iLCJib3JkZXJUb3AiLCJjb21wb25lbnQiLCJ0ZXh0RGVjb3JhdGlvbiIsIkhlYWRlciIsInNlc3Npb24iLCJzZXRPcGVuIiwibW9kZSIsInNldE1vZGUiLCJzZXRHbG9iYWxDb2xvciIsImlzTW9iaWxlIiwiZGF0YSIsInN0YXR1cyIsImlzQWRtaW4iLCJ1c2VyIiwibm90aWZpY2F0aW9uQW5jaG9yUmVmIiwibW9yZUl0ZW0iLCJzZXRNb3JlSXRlbSIsIm5vdGlmaWNhdGlvbk9wZW4iLCJzZXROb3RpZmljYXRpb25PcGVuIiwic2V0QW5jaG9yRWwiLCJzZWxlY3RlZEljb24iLCJzZXRTZWxlY3RlZEljb24iLCJzZXROb3RpZmljYXRpb25zIiwic25hY2tiYXJPcGVuIiwic2V0U25hY2tiYXJPcGVuIiwic25hY2tiYXJNZXNzYWdlIiwic2V0U25hY2tiYXJNZXNzYWdlIiwic25hY2tiYXJTZXZlcml0eSIsInNldFNuYWNrYmFyU2V2ZXJpdHkiLCJzZXRIYXNOZXdOb3RpZmljYXRpb25zIiwicHJldmlvdXNOb3RpZmljYXRpb25Db3VudCIsInNldFByZXZpb3VzTm90aWZpY2F0aW9uQ291bnQiLCJsYXN0Tm90aWZpY2F0aW9uVGltZSIsInNldExhc3ROb3RpZmljYXRpb25UaW1lIiwiZW5hYmxlUG9sbGluZyIsInNldEVuYWJsZVBvbGxpbmciLCJzZXRTZWxlY3RlZE5vdGlmaWNhdGlvbiIsImxvZ29Tb3VyY2UiLCJkcmF3ZXJWYXJpYW50IiwidXNlcklkIiwidXNlcl9pZCIsImVycm9yIiwidmFyaWFibGVzIiwic2tpcCIsInBvbGxJbnRlcnZhbCIsImZldGNoUG9saWN5IiwiZXJyb3JQb2xpY3kiLCJub3RpZnlPbk5ldHdvcmtTdGF0dXNDaGFuZ2UiLCJ1bnNlZW5Db3VudERhdGEiLCJ1bnNlZW5Db3VudExvYWRpbmciLCJyZWZldGNoIiwicmVmZXRjaFVuc2VlbkNvdW50IiwiYWRkTm90aWZpY2F0aW9uVmlldyIsIm1hcmtBbGxBc1NlZW4iLCJwbGF5Tm90aWZpY2F0aW9uU291bmQiLCJhdWRpbyIsIkF1ZGlvIiwidm9sdW1lIiwicGxheSIsImNhdGNoIiwiZSIsImNvbnNvbGUiLCJsb2ciLCJzaG93QnJvd3Nlck5vdGlmaWNhdGlvbiIsIm1lc3NhZ2UiLCJ3aW5kb3ciLCJOb3RpZmljYXRpb24iLCJwZXJtaXNzaW9uIiwiYm9keSIsImljb24iLCJiYWRnZSIsInRhZyIsInJlcXVpcmVJbnRlcmFjdGlvbiIsInNpbGVudCIsInJlcXVlc3RQZXJtaXNzaW9uIiwid2FybiIsImFsbE5vdGlmaWNhdGlvbnMiLCJjdXJyZW50Q291bnQiLCJub3ciLCJuZXdOb3RpZmljYXRpb25Db3VudCIsInNldFRpbWVvdXQiLCJncmFwaFFMRXJyb3JzIiwibmV0d29ya0Vycm9yIiwidG9nZ2xlRHJhd2VyIiwiaGFuZGxlU2V0TW9yZUl0ZW1DbGljayIsImhhbmRsZUNsaWNrIiwiZXZlbnQiLCJjdXJyZW50VGFyZ2V0IiwiaGFuZGxlQ2xvc2UiLCJoYW5kbGVOb3RpZmljYXRpb25Ub2dnbGUiLCJ3YXNPcGVuIiwicHJldiIsImhhbmRsZU5vdGlmaWNhdGlvbkNsb3NlIiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiaGFuZGxlTm90aWZpY2F0aW9uQ2xpY2siLCJoYW5kbGVCYWNrVG9MaXN0IiwiaGFuZGxlVGhlbWVDaGFuZ2UiLCJoYW5kbGVDb2xvckNoYW5nZSIsInJlbW92ZUFubm91bmNlbWVudEhhbmRsZXIiLCJub3RpZmljYXRpb25JZCIsInJlc3BvbnNlIiwidXBkYXRlZE5vdGlmaWNhdGlvbnMiLCJmaWx0ZXIiLCJuIiwicHVycGxlIiwiZWRnZSIsImFyaWEtbGFiZWwiLCJtYXJnaW5SaWdodCIsInNyYyIsImFsdCIsIm5vV3JhcCIsImZsZXhHcm93IiwieHMiLCJzbSIsIm1kIiwiY29udGVudCIsInJpZ2h0IiwiYXJpYS1jb250cm9scyIsImFyaWEtaGFzcG9wdXAiLCJCb29sZWFuIiwicGxhY2VtZW50IiwiZGlyZWN0aW9uIiwiYXV0b0ZvY3VzSXRlbSIsImZpbGwiLCJibHVlIiwibWFpbiIsImdyZWVuIiwicmVmIiwiYmFkZ2VDb250ZW50IiwidW5zZWVuTm90aWZpY2F0aW9uc0NvdW50IiwibWF4Iiwib25Ob3RpZmljYXRpb25DbGljayIsImJvcmRlclJpZ2h0IiwibWFyZ2luIiwibWFyZ2luVG9wIiwiaW4iLCJ1bm1vdW50T25FeGl0IiwiZGlzYWJsZVBhZGRpbmciLCJhdXRvSGlkZUR1cmF0aW9uIiwic2V2ZXJpdHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/Header/Header.js\n"));

/***/ })

});