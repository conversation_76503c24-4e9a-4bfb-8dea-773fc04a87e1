"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/news",{

/***/ "./src/components/AnnouncementCard.js":
/*!********************************************!*\
  !*** ./src/components/AnnouncementCard.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Close */ \"./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst AnnouncementCard = (param)=>{\n    let { title, details, isSelected, onClick, borderLeft, isCurrentAnnouncement, showDate, date, image } = param;\n    _s();\n    const [openImageModal, setOpenImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width: 1200px)\");\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const handleCardClick = (e)=>{\n        if (image && !e.target.closest(\"a, button\")) {\n            setOpenImageModal(true);\n        }\n        if (onClick) onClick(e);\n    };\n    const handleCloseModal = (e)=>{\n        e.stopPropagation();\n        setOpenImageModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n            gap: \"15px\",\n            height: \"100%\",\n            backgroundColor: \"blue\",\n            cursor: image ? \"pointer\" : \"default\"\n        },\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                sx: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n                    gap: \"7.5px\",\n                    width: \"100%\",\n                    padding: \"0 16px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        sx: {\n                            fontWeight: 700,\n                            fontSize: \"16px\",\n                            fontFamily: \"Urbanist\",\n                            wordWrap: \"break-word\",\n                            width: \"100%\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                        sx: {\n                            width: \"122px\",\n                            border: isCurrentAnnouncement ? \"1px solid #A665E1\" : \"1px solid #00BC82\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"body2\",\n                sx: {\n                    color: theme.palette.text.primary,\n                    fontWeight: 500,\n                    fontFamily: \"Inter\",\n                    wordWrap: \"normal\",\n                    fontSize: \"14px\",\n                    wordWrap: \"break-word\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: details\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"caption\",\n                sx: {\n                    color: theme.palette.text.secondary,\n                    fontFamily: \"Inter\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: new Date(date).toLocaleDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, undefined),\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n                open: openImageModal,\n                onClose: handleCloseModal,\n                \"aria-labelledby\": \"image-modal\",\n                \"aria-describedby\": \"image-modal-description\",\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backdropFilter: \"blur(5px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                    sx: {\n                        position: \"relative\",\n                        width: isMediumScreen ? \"90%\" : \"70%\",\n                        height: isMediumScreen ? \"auto\" : \"80%\",\n                        bgcolor: \"background.paper\",\n                        boxShadow: 24,\n                        p: 2,\n                        display: \"flex\",\n                        flexDirection: isMediumScreen ? \"column\" : \"row\",\n                        borderRadius: \"8px\",\n                        overflow: \"hidden\"\n                    },\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.IconButton, {\n                            onClick: handleCloseModal,\n                            sx: {\n                                position: \"absolute\",\n                                right: 10,\n                                top: 10,\n                                zIndex: 1,\n                                backgroundColor: \"rgba(0,0,0,0.5)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(0,0,0,0.7)\"\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                height: isMediumScreen ? \"300px\" : \"100%\",\n                                overflow: \"hidden\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                backgroundColor: \"#f5f5f5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imgUpdate,\n                                alt: title,\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                padding: \"20px\",\n                                overflowY: \"auto\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h5\",\n                                    gutterBottom: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined),\n                                showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"subtitle1\",\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    children: new Date(date).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Divider, {\n                                    sx: {\n                                        my: 0\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"body1\",\n                                    children: details\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 139,\n                columnNumber: 15\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementCard, \"KuuCFbfhsAlXP8yulIWuHTzVnBM=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = AnnouncementCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementCard);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementCard.js\n"));

/***/ })

});