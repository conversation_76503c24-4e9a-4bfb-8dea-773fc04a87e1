import { baseUrl } from "@/Data/ApolloClient";
import {
  Al<PERSON>,
  Box,
  CircularProgress,
  Container,
  Typography,
  useTheme,
} from "@mui/material";
import Image from "next/image";
import { useEffect, useState } from "react";
import { getCurrentTime } from "./HelperFunctions";

const WeatherComponent = () => {
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const theme = useTheme();

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lon = position.coords.longitude;
          fetchWeatherData(lat, lon);
        },
        () => {
          // this function will be called if the user denies permission
          const defaultLat = 24.6877;
          const defaultLon = 46.7219;
          fetchWeatherData(defaultLat, defaultLon);
        }
      );
    } else {
      setError("Geolocation is not supported by this browser.");
    }
  }, []);

  function fetchWeatherData(lat, lon) {
    fetch(
      `${baseUrl}/test/testing-route?lat=${lat}&lon=${lon}`
      //`http://10.0.1.16:3001/test/testing-route?lat=${lat}&lon=${lon}`
    )
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        setWeatherData(data.data);
        setLoading(false);
      })
      .catch((error) => {
        setError(error.message);
        setLoading(false);
      });
  }
  const formatDateTime = (unixTime) => {
    const date = new Date(unixTime * 1000);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  if (loading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!weatherData) {
    return <div>No weather data available.</div>;
  }
  if (!weatherData.weather || !weatherData.main) {
    return <div>Weather data is incomplete.</div>;
  }

  const weatherIconUrl = `/weatherIcons/${weatherData.weather[0].icon}.svg`;
  // const weatherIconUrl = `/weatherIcons/01d.png`;

  return (
    // <Container>
    <Box
      sx={{
        background: theme.palette.background.secondary,
        borderRadius: "10px",
        boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
        padding: "20px",
        // margin: "75px 0px 20px 0px", 
        height: "100%",
        marginTop:'20px'
      }}>
        <Typography variant="h6" fontSize="1.3rem" fontWeight="700" marginX="5px">Weather </Typography>

      <Box sx={{
        // display: "flex",
        // flexDirection: "column",
        // justifyContent: "center",
        // background: theme.palette.background.secondary,
        // borderRadius: "10px",
        // boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
        // padding: "20px",
        margin: "75px 0px 20px 0px", 
        // height: "100%"
      }}>
        <Typography variant="body1" sx={{ fontWeight: "600", fontSize: "1rem" }}>
          Current Weather in {weatherData.name}
        </Typography>
        <Box sx={{ margin: "10px" }}>
          <Box>
            <Typography
              variant="body2"
              sx={{ fontWeight: "600", lineHeight: "0px", fontSize: "1rem" }}
            >
              {/* {formatDateTime(weatherData.dt)} */}
              {getCurrentTime()}
            </Typography>
          </Box>
          <Box
            sx={{
              margin: "30px 0px 0px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Image
              // src='/weatherIcons/09d.svg'
              src={weatherIconUrl}
              alt={weatherData.weather[0].description}
              width={125}
              height={125}
              style={{
                WebkitFilter: "drop-shadow(5px 5px 5px #666666)",
                filter: "drop-shadow(5px 5px 5px #666666)",
              }}
            />
          </Box>
          {/* , boxShadow:"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)"       shadow apply on weather icon */}
          <Box
            sx={{
              marginTop: "10px",
              display: "flex",
              justifyContent: "space-around",
            }}
          >
            <Box marginLeft="20px">
              <Typography variant="h3">
                {Math.round(weatherData.main.temp - 273.15)}
                <sup>o</sup>C
              </Typography>
            </Box>
            <Box sx={{ marginLeft: "10px" }}>
              <Typography
                variant="body2"
                sx={{ fontSize: "1rem", fontWeight: "600" }}
              >
                {weatherData.weather[0].main}
              </Typography>
              <Typography
                sx={{ width: "100%", fontSize: "1rem", fontWeight: "600" }}
                variant="body2"
              >
                RealFeel {Math.round(weatherData.main.feels_like - 273.15)}°C
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>

    // </Container>
  );
};

export default WeatherComponent;
