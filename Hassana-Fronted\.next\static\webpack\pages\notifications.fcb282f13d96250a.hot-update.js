"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/notifications",{

/***/ "./src/pages/notifications.js":
/*!************************************!*\
  !*** ./src/pages/notifications.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/material/IconButton */ \"./node_modules/@mui/material/IconButton/index.js\");\n/* harmony import */ var _mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mui/material/Divider */ \"./node_modules/@mui/material/Divider/index.js\");\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NotificationButtons */ \"./src/components/NotificationButtons.jsx\");\n/* harmony import */ var _components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/AnnouncementCard */ \"./src/components/AnnouncementCard.js\");\n/* harmony import */ var _components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/NotificationCard */ \"./src/components/NotificationCard.jsx\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Dashboard */ \"./src/components/Dashboard.jsx\");\n/* harmony import */ var _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _Data_Events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/Data/Events */ \"./src/Data/Events.js\");\n/* harmony import */ var _Data_Notification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/Data/Notification */ \"./src/Data/Notification.js\");\n/* harmony import */ var _Data_Booking__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Data/Booking */ \"./src/Data/Booking.js\");\n/* harmony import */ var _components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/auth/withAuth */ \"./src/components/auth/withAuth.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ResponsiveText = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(\"div\")((param)=>{\n    let { theme } = param;\n    return {\n        color: \"#1B3745\",\n        fontSize: \"16.02px\",\n        fontFamily: \"Helvetica\",\n        fontWeight: 400,\n        wordWrap: \"break-word\",\n        maxWidth: \"100%\"\n    };\n});\nconst ResponsiveBox = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.styled)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box)((param)=>{\n    let { theme, backgroundColor, disableBorder, isCurrentNotification } = param;\n    return {\n        width: \"100%\",\n        height: \"auto !important\",\n        background: backgroundColor || \"white\",\n        backgroundColor: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : theme.palette.background.secondary,\n        boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.05)\",\n        borderRadius: 10,\n        position: \"relative\",\n        \"&::before\": !disableBorder && {\n            content: '\"\"',\n            position: \"absolute\",\n            top: 0,\n            left: 0.6,\n            width: \"5px\",\n            height: \"100%\",\n            background: !isCurrentNotification ? theme.palette.type === \"light\" ? \"#F6F5FD\" : theme.palette.background.secondary : isCurrentNotification ? \"linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)\" : \"none\",\n            borderRadius: \"20px 0 0 20px\"\n        }\n    };\n});\n_c = ResponsiveBox;\nconst Notifications = ()=>{\n    var _notificationData_notifications;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const { notificationId } = router.query; // Get notificationId from query params\n    const { loading: eventLoading, data: eventData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Events__WEBPACK_IMPORTED_MODULE_8__.getEvents);\n    const { loading: notificationLoading, data: notificationData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Notification__WEBPACK_IMPORTED_MODULE_9__.getNotifications);\n    const { loading: bookingLoading, data: bookingData } = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery)(_Data_Booking__WEBPACK_IMPORTED_MODULE_10__.getBookings);\n    const [selectedCard, setSelectedCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [openModal, setOpenModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAnnouncement, setSelectedAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [meetings, setMeetings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeButton, setActiveButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [display, setDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const isWideScreen = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1150px)\");\n    const isLargeTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1079px)\");\n    const isTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1060px)\");\n    const isSmallTablet = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:967px)\");\n    const isWideMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:869px)\");\n    const isMediumMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:823px)\");\n    const isNarrowMobile = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:528px)\");\n    const isStandardDesktop = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(\"(max-width:1439px)\");\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme)();\n    const { color } = (0,_components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor)();\n    const selectedColor = (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor)(color);\n    // Handle navigation from query params\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (notificationId && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            // Find the notification directly in notifications data\n            const notification = notificationData.notifications.find((notif)=>notif.id === notificationId);\n            if (notification) {\n                // Switch to notifications tab and select the notification\n                setDisplay(\"notifications\");\n                setActiveButton(\"notifications\");\n                // Find the index in the notifications array\n                const notifIndex = notificationData.notifications.findIndex((notif)=>notif.id === notificationId);\n                // Adjust index based on how notifications are displayed (newest first)\n                const adjustedIndex = notifIndex === notificationData.notifications.length - 1 ? 0 : notificationData.notifications.length - 1 - notifIndex;\n                setSelectedCard(adjustedIndex);\n                setSelectedAnnouncement(notification);\n            }\n        } else if (!notificationId && router.asPath === \"/notifications\") {\n            // If no notificationId in URL (like when clicking \"View All Notifications\"), reset selection\n            setSelectedCard(null);\n            setSelectedAnnouncement(null);\n            // Ensure we're on the notifications tab when coming from \"View All Notifications\"\n            if (display !== \"notifications\") {\n                setDisplay(\"notifications\");\n                setActiveButton(\"notifications\");\n            }\n        }\n    }, [\n        notificationId,\n        notificationData\n    ]);\n    const handleButtonClick = (buttonType)=>{\n        setDisplay(buttonType);\n        setActiveButton(buttonType);\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!notificationLoading && (notificationData === null || notificationData === void 0 ? void 0 : notificationData.notifications)) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        notificationLoading,\n        notificationData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (eventData && eventData.events) {\n            setEvents(eventData.events);\n        }\n        if (bookingData && bookingData.bookings) {\n            setMeetings(bookingData.bookings);\n        }\n        if (notificationData && notificationData.notifications) {\n            setNotifications(notificationData.notifications);\n        }\n    }, [\n        eventData,\n        bookingData,\n        notificationData\n    ]);\n    const handleCardClick = (index)=>{\n        setSelectedCard(index);\n        let selectedItem = null;\n        if (display === \"all\") {\n            selectedItem = combinedItems[index];\n        } else if (display === \"events\") {\n            selectedItem = index < currentEvents.length ? currentEvents[index] : previousEvents[index - currentEvents.length];\n        } else if (display === \"notifications\") {\n            selectedItem = index === 0 ? notifications[notifications.length - 1] : notifications.slice(0, -1)[index - 1];\n        } else if (display === \"meetings\") {\n            selectedItem = index < upcomingMeetings.length ? upcomingMeetings[index] : previousMeetings[index - upcomingMeetings.length];\n        }\n        setSelectedAnnouncement(selectedItem);\n    };\n    const handleOpenModal = (announcement)=>{\n        setSelectedAnnouncement(announcement);\n        setOpenModal(true);\n    };\n    const handleReset = ()=>{\n        setSelectedCard(null);\n        setSelectedAnnouncement(null);\n        // Remove notificationId from URL\n        router.push(\"/notifications\", undefined, {\n            shallow: true\n        });\n    };\n    const handleCloseModal = ()=>{\n        setOpenModal(false);\n        setSelectedAnnouncement(null);\n    };\n    const currentDate = new Date();\n    const startOfWeek = currentDate.getDate() - currentDate.getDay();\n    const endOfWeek = startOfWeek + 6;\n    const currentWeekStart = new Date(currentDate);\n    currentWeekStart.setDate(startOfWeek);\n    const currentWeekEnd = new Date(currentDate);\n    currentWeekEnd.setDate(endOfWeek);\n    const currentEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate >= currentWeekStart && eventDate <= currentWeekEnd;\n    });\n    const previousEvents = events.filter((event)=>{\n        const eventDate = new Date(event.date);\n        return eventDate < currentWeekStart;\n    });\n    const upcomingMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) >= currentDate).sort((a, b)=>new Date(a.startTime) - new Date(b.startTime));\n    const previousMeetings = meetings.filter((meeting)=>new Date(meeting.startTime) < currentDate).sort((a, b)=>new Date(b.startTime) - new Date(a.startTime));\n    const currentNotifications = notifications.filter((notification)=>new Date(notification.createdAt) >= currentWeekStart).sort((a, b)=>new Date(a.createdAt) - new Date(b.createdAt));\n    const previousNotifications = notifications.filter((notification)=>new Date(notification.createdAt) < currentWeekStart).sort((a, b)=>new Date(b.createdAt) - new Date(a.createdAt));\n    const combinedItems = [\n        ...events.map((item)=>({\n                ...item,\n                type: \"event\"\n            })),\n        ...meetings.map((item)=>({\n                ...item,\n                type: \"meeting\"\n            })),\n        ...notifications.map((item)=>({\n                ...item,\n                type: \"notification\"\n            }))\n    ].sort((a, b)=>{\n        const dateA = a.createdAt || a.startTime || a.date;\n        const dateB = b.createdAt || b.startTime || b.date;\n        return new Date(dateB) - new Date(dateA);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                sx: {\n                    display: \"flex\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                    component: \"main\",\n                    sx: {\n                        background: selectedColor === theme.palette.background.primary ? theme.palette.background.secondary : selectedColor,\n                        paddingLeft: isLargeTablet ? \"50px\" : \"\",\n                        paddingRight: isLargeTablet ? \"50px\" : \"\",\n                        width: isMediumMobile ? \"100%\" : isWideMobile ? \"45rem\" : isSmallTablet ? \"48rem\" : isTablet ? \"54rem\" : \"60vw\",\n                        margin: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginLeft: \"15px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                        variant: \"h5\",\n                                        style: {\n                                            marginTop: \"32px\",\n                                            fontSize: \"16.024px\",\n                                            marginLeft: isStandardDesktop ? \"16px\" : \"\"\n                                        },\n                                        children: \"Latest updates from Hassana\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: \"20px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"h1\",\n                                            style: {\n                                                fontSize: isWideScreen ? \"22px\" : \"27.47px\",\n                                                fontWeight: \"700\",\n                                                marginTop: \"0\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"15px\"\n                                            },\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                paddingRight: \"25px\",\n                                                marginLeft: isStandardDesktop ? \"30px\" : \"20px\",\n                                                marginTop: \"20px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationButtons__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                handleButtonClick: handleButtonClick,\n                                                activeButton: activeButton,\n                                                isNarrowMobile: isNarrowMobile,\n                                                isWideScreen: isWideScreen\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedCard !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                marginTop: \"20px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_IconButton__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    onClick: handleReset,\n                                    style: {\n                                        marginLeft: isStandardDesktop ? \"20px\" : \"5px\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\",\n                                        color: \"#888\",\n                                        gap: 5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icons/arrow-left.svg\",\n                                            alt: \"arrow\",\n                                            height: 16,\n                                            width: true,\n                                            parametern: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        gap: \"8px\",\n                                        alignItems: \"center\",\n                                        fontSize: \"16px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_Divider__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            sx: {\n                                                width: \"15px\",\n                                                backgroundColor: \"#A7A7A7\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginTop: \"2px\"\n                                            },\n                                            children: (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 338,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"2px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 350,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: \"#888\",\n                                                fontSize: \"12px\",\n                                                marginBottom: \"4px\"\n                                            },\n                                            children: [\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" —\",\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 323,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 308,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                            container: true,\n                            spacing: 3,\n                            sx: {\n                                padding: \"3%\",\n                                height: \"70vh\",\n                                overflow: \"auto\",\n                                marginTop: \"10px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 12,\n                                lg: 12,\n                                order: {\n                                    xs: 2,\n                                    sm: 2,\n                                    md: 2,\n                                    lg: 1,\n                                    xl: 1\n                                },\n                                children: [\n                                    display === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading || eventLoading || bookingLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 398,\n                                            columnNumber: 23\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: combinedItems.map((item, index)=>{\n                                                const isEvent = item.type === \"event\";\n                                                const isMeeting = item.type === \"meeting\";\n                                                const isNotification = item.type === \"notification\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: isNotification,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\",\n                                                        background: isNotification || isMeeting ? theme.palette.background.secondary : undefined\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        isEvent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.date : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isMeeting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: item.title,\n                                                            details: item.details,\n                                                            isSelected: selectedCard === index,\n                                                            date: selectedCard === index ? item.startTime : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 33\n                                                        }, undefined),\n                                                        isNotification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: item.notification,\n                                                            isSelected: selectedCard === index,\n                                                            isCurrentNotification: true,\n                                                            showDate: selectedCard === index,\n                                                            date: selectedCard === index ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(item.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 33\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 29\n                                                }, undefined);\n                                            })\n                                        }, void 0, false)\n                                    }, void 0, false),\n                                    display === \"events\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            currentEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        isCurrentNotification: true,\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        date: selectedCard === index ? event.date : null,\n                                                        isSelected: selectedCard === index\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousEvents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Events\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 490,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousEvents.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + currentEvents.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + currentEvents.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + currentEvents.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: event.title,\n                                                        details: event.details,\n                                                        isSelected: selectedCard === index + currentEvents.length,\n                                                        date: selectedCard === index + currentEvents.length ? event.date : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, event.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true),\n                                    display === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: notificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"Loading...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 552,\n                                            columnNumber: 23\n                                        }, undefined) : (notificationData === null || notificationData === void 0 ? void 0 : (_notificationData_notifications = notificationData.notifications) === null || _notificationData_notifications === void 0 ? void 0 : _notificationData_notifications.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    isCurrentNotification: true,\n                                                    sx: {\n                                                        padding: \"16px\",\n                                                        marginTop: \"10px\",\n                                                        display: selectedCard !== null && selectedCard !== 0 ? \"none\" : \"block\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(0),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        notification: notificationData.notifications[notificationData.notifications.length - 1].notification,\n                                                        isSelected: selectedCard === 0,\n                                                        isCurrentNotification: true,\n                                                        showDate: selectedCard === 0,\n                                                        date: selectedCard === 0 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notificationData.notifications[notificationData.notifications.length - 1].createdAt)) : null\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                notificationData.notifications.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    marginBottom: \"24px\",\n                                                    marginTop: \"24px\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderBottom: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Previous Notifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                            borderTop: \"2px solid #E2E0F1\",\n                                                            width: \"100%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                notificationData.notifications.slice(0, -1).map((notification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                        sx: {\n                                                            background: theme.palette.background.secondary,\n                                                            padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                            marginTop: \"20px\",\n                                                            display: selectedCard !== null && selectedCard !== index + 1 ? \"none\" : \"block\",\n                                                            height: selectedCard === index + 1 ? \"auto\" : \"170px\"\n                                                        },\n                                                        onClick: ()=>handleCardClick(index + 1),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            notification: notification.notification,\n                                                            isSelected: selectedCard === index + 1,\n                                                            isCurrentNotification: false,\n                                                            showDate: selectedCard === index + 1,\n                                                            date: selectedCard === index + 1 ? \"\".concat((0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(notification.createdAt)) : null\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, notification.id || index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 27\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            children: \"No notifications available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 612,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false),\n                                    display === \"meetings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            upcomingMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                            variant: \"body2\",\n                                                            align: \"center\",\n                                                            sx: {\n                                                                mx: 0,\n                                                                color: \"#949494\",\n                                                                fontSize: \"12px\",\n                                                                whiteSpace: \"nowrap\"\n                                                            },\n                                                            children: \"Upcoming Meetings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            title: meeting.title,\n                                                            details: meeting.details,\n                                                            date: selectedCard === index ? meeting.startTime : null,\n                                                            isSelected: selectedCard === index,\n                                                            onClick: ()=>handleOpenModal(meeting)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, meeting.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 23\n                                                }, undefined)),\n                                            previousMeetings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                marginBottom: \"24px\",\n                                                marginTop: \"24px\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderBottom: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                                        variant: \"body2\",\n                                                        align: \"center\",\n                                                        sx: {\n                                                            mx: 0,\n                                                            color: \"#949494\",\n                                                            fontSize: \"12px\",\n                                                            whiteSpace: \"nowrap\"\n                                                        },\n                                                        children: \"Previous Meetings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                                        borderTop: \"2px solid #E2E0F1\",\n                                                        width: \"100%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                lineNumber: 658,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            previousMeetings.map((meeting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ResponsiveBox, {\n                                                    sx: {\n                                                        background: theme.palette.background.secondary,\n                                                        padding: \"30.41px 16.91px 29.04px 16.91px\",\n                                                        marginTop: \"20px\",\n                                                        display: selectedCard !== null && selectedCard !== index + upcomingMeetings.length ? \"none\" : \"block\",\n                                                        height: selectedCard !== null && selectedCard === index + upcomingMeetings.length ? \"auto\" : \"170px\"\n                                                    },\n                                                    onClick: ()=>handleCardClick(index + upcomingMeetings.length),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnnouncementCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        title: meeting.title,\n                                                        details: meeting.details,\n                                                        date: selectedCard === index + upcomingMeetings.length ? meeting.startTime : null,\n                                                        isSelected: selectedCard === index + upcomingMeetings.length,\n                                                        onClick: ()=>handleOpenModal(meeting)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, meeting.id || index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                lineNumber: 388,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                            open: openModal,\n                            onClose: handleCloseModal,\n                            maxWidth: \"sm\",\n                            fullWidth: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                    children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.title) || \"Notification Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body1\",\n                                            children: (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.notification) || (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.details) || \"No details available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.date),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.date))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Start Time: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.startTime),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.startTime))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 738,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        (selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.createdAt) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.date) && !(selectedAnnouncement === null || selectedAnnouncement === void 0 ? void 0 : selectedAnnouncement.startTime) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Typography, {\n                                            variant: \"body2\",\n                                            color: \"textSecondary\",\n                                            children: [\n                                                \"Date: \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.getFormattedDate)(selectedAnnouncement.createdAt),\n                                                \" \",\n                                                (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.monthName)(new Date(selectedAnnouncement.createdAt))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.DialogActions, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__.Button, {\n                                        onClick: handleCloseModal,\n                                        color: \"primary\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                            lineNumber: 724,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\pages\\\\notifications.js\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(Notifications, \"k15HFFbkW2IpBjetlj1ltXnmtLg=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_13__.useTheme,\n        _components_ColorContext__WEBPACK_IMPORTED_MODULE_6__.useColor,\n        _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_7__.useSelectedColor\n    ];\n});\n_c1 = Notifications;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c2 = (0,_components_auth_withAuth__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(Notifications));\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ResponsiveBox\");\n$RefreshReg$(_c1, \"Notifications\");\n$RefreshReg$(_c2, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/notifications.js\n"));

/***/ })

});