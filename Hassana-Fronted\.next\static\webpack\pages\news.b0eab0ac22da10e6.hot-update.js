"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/news",{

/***/ "./src/components/AnnouncementCard.js":
/*!********************************************!*\
  !*** ./src/components/AnnouncementCard.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/icons-material/Close */ \"./node_modules/@mui/icons-material/Close.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/imageUtils */ \"./src/utils/imageUtils.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AnnouncementCard = (param)=>{\n    let { title, details, isSelected, onClick, borderLeft, isCurrentAnnouncement, showDate, date, image } = param;\n    _s();\n    const [openImageModal, setOpenImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isMediumScreen = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useMediaQuery)(\"(max-width: 1200px)\");\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const cleanImagePath = image ? image.replace(\"http://localhost:3009\", \"\") : null;\n    const imgUpdate = \"https://hassana-api.360xpertsolutions.com\" + cleanImagePath;\n    const handleCardClick = (e)=>{\n        if (image && !e.target.closest(\"a, button\")) {\n            setOpenImageModal(true);\n        }\n        if (onClick) onClick(e);\n    };\n    const handleCloseModal = (e)=>{\n        e.stopPropagation();\n        setOpenImageModal(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        sx: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n            gap: \"15px\",\n            height: \"100%\",\n            backgroundColor: \"blue\",\n            cursor: image ? \"pointer\" : \"default\"\n        },\n        onClick: handleCardClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                sx: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    justifyContent: \"center\",\n                    alignItems: isMediumScreen ? \"center\" : \"flex-start\",\n                    gap: \"7.5px\",\n                    width: \"100%\",\n                    padding: \"0 16px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                        variant: \"h6\",\n                        sx: {\n                            fontWeight: 700,\n                            fontSize: \"16px\",\n                            fontFamily: \"Urbanist\",\n                            wordWrap: \"break-word\",\n                            width: \"100%\"\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Divider, {\n                        sx: {\n                            width: \"122px\",\n                            border: isCurrentAnnouncement ? \"1px solid #A665E1\" : \"1px solid #00BC82\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                variant: \"body2\",\n                sx: {\n                    color: theme.palette.text.primary,\n                    fontWeight: 500,\n                    fontFamily: \"Inter\",\n                    wordWrap: \"normal\",\n                    fontSize: \"14px\",\n                    wordWrap: \"break-word\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: details\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                variant: \"caption\",\n                sx: {\n                    color: theme.palette.text.secondary,\n                    fontFamily: \"Inter\",\n                    padding: \"0 16px 16px\",\n                    width: \"100%\"\n                },\n                children: new Date(date).toLocaleDateString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined),\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                open: openImageModal,\n                onClose: handleCloseModal,\n                \"aria-labelledby\": \"image-modal\",\n                \"aria-describedby\": \"image-modal-description\",\n                sx: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backdropFilter: \"blur(5px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                    sx: {\n                        position: \"relative\",\n                        width: isMediumScreen ? \"90%\" : \"70%\",\n                        height: isMediumScreen ? \"auto\" : \"80%\",\n                        bgcolor: \"background.paper\",\n                        boxShadow: 24,\n                        p: 2,\n                        display: \"flex\",\n                        flexDirection: isMediumScreen ? \"column\" : \"row\",\n                        borderRadius: \"8px\",\n                        overflow: \"hidden\"\n                    },\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.IconButton, {\n                            onClick: handleCloseModal,\n                            sx: {\n                                position: \"absolute\",\n                                right: 10,\n                                top: 10,\n                                zIndex: 1,\n                                backgroundColor: \"rgba(0,0,0,0.5)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(0,0,0,0.7)\"\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Close__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                height: isMediumScreen ? \"300px\" : \"100%\",\n                                overflow: \"hidden\",\n                                display: \"flex\",\n                                justifyContent: \"center\",\n                                alignItems: \"center\",\n                                backgroundColor: \"#f5f5f5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: imgUpdate,\n                                alt: title,\n                                style: {\n                                    width: \"100%\",\n                                    height: \"100%\",\n                                    objectFit: \"contain\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                            sx: {\n                                width: isMediumScreen ? \"100%\" : \"50%\",\n                                padding: \"20px\",\n                                overflowY: \"auto\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"h5\",\n                                    gutterBottom: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                showDate && date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"subtitle1\",\n                                    color: \"text.secondary\",\n                                    gutterBottom: true,\n                                    children: new Date(date).toLocaleDateString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Divider, {\n                                    sx: {\n                                        my: 0\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_IconButton_Modal_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__.Typography, {\n                                    variant: \"body1\",\n                                    children: details\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n                lineNumber: 141,\n                columnNumber: 15\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hassana\\\\Hassana-Fronted\\\\src\\\\components\\\\AnnouncementCard.js\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnnouncementCard, \"KuuCFbfhsAlXP8yulIWuHTzVnBM=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useMediaQuery,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = AnnouncementCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnnouncementCard);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/AnnouncementCard.js\n"));

/***/ }),

/***/ "./src/utils/imageUtils.js":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.js ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAnnouncementImageUrl: function() { return /* binding */ getAnnouncementImageUrl; },\n/* harmony export */   getImageUrl: function() { return /* binding */ getImageUrl; },\n/* harmony export */   getNewsImageUrl: function() { return /* binding */ getNewsImageUrl; }\n/* harmony export */ });\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/Data/ApolloClient */ \"./src/Data/ApolloClient.js\");\n\n/**\n * Utility function to construct proper image URLs for news/announcements\n * Handles different image path formats and ensures compatibility with any domain\n * \n * @param {string} imagePath - The image path from the backend\n * @returns {string|null} - The properly constructed image URL or null if no image\n */ const getImageUrl = (imagePath)=>{\n    // Return null if no image path provided\n    if (!imagePath) {\n        return null;\n    }\n    // If it's already a base64 data URL (for new uploads), return as is\n    if (typeof imagePath === \"string\" && imagePath.startsWith(\"data:\")) {\n        return imagePath;\n    }\n    // If it's already a complete URL (starts with http/https), return as is\n    if (typeof imagePath === \"string\" && (imagePath.startsWith(\"http://\") || imagePath.startsWith(\"https://\"))) {\n        return imagePath;\n    }\n    // Clean the image path by removing any existing domain/base URL\n    let cleanPath = imagePath;\n    // Remove common localhost URLs that might be in the path\n    cleanPath = cleanPath.replace(/^https?:\\/\\/localhost:\\d+/, \"\");\n    cleanPath = cleanPath.replace(/^http:\\/\\/localhost:\\d+/, \"\");\n    // Remove any existing domain from the path\n    cleanPath = cleanPath.replace(/^https?:\\/\\/[^/]+/, \"\");\n    // Remove leading slash if present (we'll add it back)\n    cleanPath = cleanPath.replace(/^\\/+/, \"\");\n    // Ensure the path starts with the correct resource path\n    if (!cleanPath.startsWith(\"resource/v1\") && !cleanPath.startsWith(\"v1/resource\")) {\n        // If the path doesn't include the resource directory, add it\n        if (cleanPath.includes(\"news/\") || cleanPath.includes(\"announcement/\")) {\n            cleanPath = \"resource/v1/\".concat(cleanPath);\n        } else {\n            // Default to resource/v1 for other images\n            cleanPath = \"resource/v1/\".concat(cleanPath);\n        }\n    }\n    // Construct the final URL with current baseUrl\n    const finalUrl = \"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/\").concat(cleanPath);\n    console.log(\"Image URL construction:\", {\n        original: imagePath,\n        cleaned: cleanPath,\n        final: finalUrl,\n        baseUrl: _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl\n    });\n    return finalUrl;\n};\n/**\n * Utility function specifically for news images\n * @param {string} imagePath - The image path from the backend\n * @returns {string|null} - The properly constructed news image URL\n */ const getNewsImageUrl = (imagePath)=>{\n    if (!imagePath) return null;\n    let cleanPath = imagePath;\n    // Remove any existing domain\n    cleanPath = cleanPath.replace(/^https?:\\/\\/[^/]+/, \"\");\n    cleanPath = cleanPath.replace(/^\\/+/, \"\");\n    // Ensure it's in the correct news directory\n    if (!cleanPath.includes(\"news/\") && !cleanPath.startsWith(\"resource/v1/news/\")) {\n        cleanPath = \"resource/v1/news/\".concat(cleanPath);\n    } else if (!cleanPath.startsWith(\"resource/v1/\")) {\n        cleanPath = \"resource/v1/\".concat(cleanPath);\n    }\n    return \"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/\").concat(cleanPath);\n};\n/**\n * Utility function specifically for announcement images\n * @param {string} imagePath - The image path from the backend\n * @returns {string|null} - The properly constructed announcement image URL\n */ const getAnnouncementImageUrl = (imagePath)=>{\n    if (!imagePath) return null;\n    let cleanPath = imagePath;\n    // Remove any existing domain\n    cleanPath = cleanPath.replace(/^https?:\\/\\/[^/]+/, \"\");\n    cleanPath = cleanPath.replace(/^\\/+/, \"\");\n    // Ensure it's in the correct announcement directory\n    if (!cleanPath.includes(\"announcement/\") && !cleanPath.startsWith(\"resource/v1/announcement/\")) {\n        cleanPath = \"resource/v1/announcement/\".concat(cleanPath);\n    } else if (!cleanPath.startsWith(\"resource/v1/\")) {\n        cleanPath = \"resource/v1/\".concat(cleanPath);\n    }\n    return \"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/\").concat(cleanPath);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/imageUtils.js\n"));

/***/ })

});